{"Title": "Black Mirror", "seriesID": "tt2085059", "Season": 1, "totalSeasons": 7, "Episodes": [{"Title": "The National Anthem", "Year": "2011", "Rated": "TV-MA", "Released": "04 Dec 2011", "Runtime": "44 min", "Genre": "Crime, Drama, Mystery", "Director": "<PERSON>", "Writer": "<PERSON>", "Actors": "<PERSON>, <PERSON>, <PERSON>", "Plot": "Prime Minister <PERSON> faces a shocking dilemma when Princess <PERSON>, a much-loved member of the Royal Family, is kidnapped.", "Language": "English", "Country": "United Kingdom", "Poster": "https://m.media-amazon.com/images/M/MV5BZDJlNTU1NTQtYzBiYi00YmFiLTgyN2UtMmJjNDRhMTVmNjY4XkEyXkFqcGc@._V1_SX300.jpg", "imdbRating": "7.6", "imdbVotes": "67912", "imdbID": "tt2089051", "Episode": 1}, {"Title": "Fifteen Million Merits", "Year": "2011", "Rated": "TV-MA", "Released": "11 Dec 2011", "Runtime": "62 min", "Genre": "Crime, Drama, Mystery", "Director": "Euros Lyn", "Writer": "<PERSON>, <PERSON><PERSON><PERSON>", "Actors": "<PERSON>, <PERSON>, <PERSON>", "Plot": "In a world where people's lives consist of riding exercise bikes to gain credits, <PERSON> tries to help a woman get on to a singing competition show.", "Language": "English", "Country": "United Kingdom", "Poster": "https://m.media-amazon.com/images/M/MV5BMzhiMGMwZDUtYzU1NS00YjQ4LWJkMWYtYmU5MjkyYThlNDliXkEyXkFqcGc@._V1_SX300.jpg", "imdbRating": "8.0", "imdbVotes": "69485", "imdbID": "tt2089049", "Episode": 2}, {"Title": "The Entire History of You", "Year": "2011", "Rated": "TV-MA", "Released": "18 Dec 2011", "Runtime": "49 min", "Genre": "Crime, Drama, Mystery", "Director": "<PERSON>", "Writer": "<PERSON>, <PERSON>", "Actors": "<PERSON>, <PERSON><PERSON>, <PERSON>", "Plot": "In the near future, everyone has access to a memory implant that records everything they do, see and hear. You need never forget a face again - but is that always a good thing?", "Language": "English", "Country": "United Kingdom", "Poster": "https://m.media-amazon.com/images/M/MV5BYjhiODk5ZmUtOGUxMy00MDJkLWJkZDQtNDI3OGJlZDU1MDk1XkEyXkFqcGc@._V1_SX300.jpg", "imdbRating": "8.5", "imdbVotes": "71386", "imdbID": "tt2089050", "Episode": 3}], "Response": "True"}