[{"id": "sitelink", "type": "inputstring", "name": "Site Link", "value": "https://dontorrent.institute/"}, {"id": "cookieheader", "type": "hiddendata", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": ""}, {"id": "lasterror", "type": "hiddendata", "name": "LastError", "value": "Exception (dontorrent): <!DOCTYPE HTML PUBLIC \"-//IETF//DTD HTML 2.0//EN\">\n<html><head>\n<title>301 Moved Permanently</title>\n</head><body>\n<h1>Moved Permanently</h1>\n<p>The document has moved <a href=\"https://dontorrent.loan/ultimos\">here</a>.</p>\n</body></html>\n: <!DOCTYPE HTML PUBLIC \"-//IETF//DTD HTML 2.0//EN\">\n<html><head>\n<title>301 Moved Permanently</title>\n</head><body>\n<h1>Moved Permanently</h1>\n<p>The document has moved <a href=\"https://dontorrent.loan/ultimos\">here</a>.</p>\n</body></html>\n"}, {"id": "matchwordsintitle", "type": "inputbool", "name": "Match words in title", "value": true}, {"id": "tags", "type": "inputtags", "name": "Tags", "value": ""}]