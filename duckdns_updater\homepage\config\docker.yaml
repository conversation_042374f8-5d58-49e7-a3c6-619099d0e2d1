# Homepage Docker Configuration - Configuración Optimizada
# Configuración mejorada para estabilidad y rendimiento

# === CONFIGURACIÓN PRINCIPAL DE DOCKER ===
my-docker:
  socket: /var/run/docker.sock
  # Configuración de timeout aumentada para estabilidad
  timeout: 15000
  # Configuración de reintentos
  retries: 3
  # Configuración de caché para mejorar rendimiento
  cache: 300

# === CONFIGURACIÓN DE SERVICIOS ESPECÍFICOS ===
# Solo incluir contenedores que están actualmente ejecutándose

# Servicios de Media
jellyfin:
  my-docker:
    container: jellyfin
    widget:
      type: jellyfin
      url: http://host.docker.internal:8096
      key: "{{HOMEPAGE_VAR_JELLYFIN_KEY}}"

jellyseerr:
  my-docker:
    container: jellyseerr
    widget:
      type: jellyseerr
      url: http://host.docker.internal:5055
      key: "{{HOMEPAGE_VAR_JELLYSEERR_KEY}}"

qbittorrent:
  my-docker:
    container: qbittorrent
    widget:
      type: qbittorrent
      url: http://host.docker.internal:8091
      username: "{{HOMEPAGE_VAR_QBITTORRENT_USERNAME}}"
      password: "{{HOMEPAGE_VAR_QBITTORRENT_PASSWORD}}"

# Servicios *arr
sonarr:
  my-docker:
    container: sonarr
    widget:
      type: sonarr
      url: http://host.docker.internal:8989
      key: "{{HOMEPAGE_VAR_SONARR_KEY}}"

radarr:
  my-docker:
    container: radarr
    widget:
      type: radarr
      url: http://host.docker.internal:7878
      key: "{{HOMEPAGE_VAR_RADARR_KEY}}"

jackett:
  my-docker:
    container: jackett
    widget:
      type: jackett
      url: http://host.docker.internal:9117
      key: "{{HOMEPAGE_VAR_JACKETT_KEY}}"

# Servicios de Sistema
caddy_proxy:
  my-docker:
    container: caddy_proxy
    # Sin widget específico, solo monitoreo de estado

portainer:
  my-docker:
    container: portainer
    # Widget de Portainer deshabilitado por problemas de API

# Servicios de Soporte
flaresolverr:
  my-docker:
    container: flaresolverr
    # Sin widget específico, solo monitoreo de estado

# Servicios DuckDNS
duckdns_jucago705:
  my-docker:
    container: duckdns_jucago705
    # Sin widget específico, solo monitoreo de estado

duckdns_jucago706:
  my-docker:
    container: duckdns_jucago706
    # Sin widget específico, solo monitoreo de estado

# Watchtower (cuando esté ejecutándose)
watchtower:
  my-docker:
    container: watchtower
    # Sin widget específico, solo monitoreo de estado
