# Script simple para verificar el estado de Watchtower y contenedores monitoreados

Write-Host "=== VERIFICACIÓN DE WATCHTOWER ===" -ForegroundColor Green
Write-Host ""

# Verificar estado de Watchtower
Write-Host "🔍 Estado de Watchtower:" -ForegroundColor Yellow
$watchtowerStatus = docker ps --filter "name=watchtower" --format "{{.Status}}" 2>$null
if ($watchtowerStatus) {
    Write-Host "✅ Watchtower ejecutándose: $watchtowerStatus" -ForegroundColor Green
} else {
    Write-Host "❌ Watchtower no está ejecutándose" -ForegroundColor Red
    exit 1
}

# Verificar configuración
Write-Host ""
Write-Host "⚙️ Configuración de Watchtower:" -ForegroundColor Yellow
$envVars = docker inspect watchtower --format='{{range .Config.Env}}{{println .}}{{end}}' 2>$null

$pollInterval = ($envVars | Where-Object { $_ -like "WATCHTOWER_POLL_INTERVAL=*" }) -replace "WATCHTOWER_POLL_INTERVAL=", ""
$debug = ($envVars | Where-Object { $_ -like "WATCHTOWER_DEBUG=*" }) -replace "WATCHTOWER_DEBUG=", ""
$cleanup = ($envVars | Where-Object { $_ -like "WATCHTOWER_CLEANUP=*" }) -replace "WATCHTOWER_CLEANUP=", ""

if ($pollInterval) {
    $hours = [math]::Round($pollInterval / 3600, 1)
    Write-Host "📅 Intervalo: $hours horas" -ForegroundColor Cyan
} else {
    Write-Host "📅 Intervalo: 24 horas (default)" -ForegroundColor Yellow
}

Write-Host "🐛 Debug: $(if($debug -eq 'true') { 'Habilitado' } else { 'Deshabilitado' })" -ForegroundColor Cyan
Write-Host "🧹 Cleanup: $(if($cleanup -eq 'true') { 'Habilitado' } else { 'Deshabilitado' })" -ForegroundColor Cyan

# Verificar contenedores monitoreados
Write-Host ""
Write-Host "📋 Contenedores monitoreados:" -ForegroundColor Yellow

$containers = @("jackett", "sonarr", "radarr", "qbittorrent", "jellyfin", "jellyseerr")
$monitoredCount = 0

foreach ($container in $containers) {
    $status = docker ps --filter "name=$container" --format "{{.Status}}" 2>$null
    $label = docker inspect $container --format='{{index .Config.Labels "com.centurylinklabs.watchtower.enable"}}' 2>$null
    
    if ($status) {
        $isMonitored = $label -eq "true"
        if ($isMonitored) {
            Write-Host "✅ $container (ejecutándose, monitoreado)" -ForegroundColor Green
            $monitoredCount++
        } else {
            Write-Host "⚠️ $container (ejecutándose, NO monitoreado)" -ForegroundColor Yellow
        }
    } else {
        Write-Host "❌ $container (no ejecutándose)" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "📊 Total monitoreados: $monitoredCount de $($containers.Count)" -ForegroundColor Cyan

# Mostrar logs recientes
Write-Host ""
Write-Host "📜 Logs recientes:" -ForegroundColor Yellow
$logs = docker logs watchtower --tail 3 2>$null
if ($logs) {
    foreach ($log in $logs) {
        Write-Host "   $log" -ForegroundColor Gray
    }
} else {
    Write-Host "   No hay logs disponibles" -ForegroundColor Yellow
}

# Mostrar próxima verificación
Write-Host ""
Write-Host "⏰ Próxima verificación:" -ForegroundColor Yellow
$scheduleLogs = docker logs watchtower 2>$null | Select-String "Scheduling first run" | Select-Object -Last 1
if ($scheduleLogs) {
    $logLine = $scheduleLogs.ToString()
    if ($logLine -match 'Scheduling first run: (.+)"') {
        $nextRun = $matches[1]
        Write-Host "📅 $nextRun" -ForegroundColor Cyan
    }
}

$intervalLogs = docker logs watchtower 2>$null | Select-String "will be performed in" | Select-Object -Last 1
if ($intervalLogs) {
    $intervalLine = $intervalLogs.ToString()
    if ($intervalLine -match 'performed in (.+)"') {
        $timeRemaining = $matches[1]
        Write-Host "⏳ $timeRemaining" -ForegroundColor Cyan
    }
}

Write-Host ""
Write-Host "=== RESUMEN ===" -ForegroundColor Green
if ($monitoredCount -gt 0) {
    Write-Host "✅ Sistema configurado correctamente - $monitoredCount contenedores monitoreados" -ForegroundColor Green
} else {
    Write-Host "⚠️ No hay contenedores monitoreados" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "Para realizar una prueba manual:"
Write-Host "docker run --rm -v /var/run/docker.sock:/var/run/docker.sock containrrr/watchtower --run-once --debug jackett"
