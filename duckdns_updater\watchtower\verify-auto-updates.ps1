# Script para verificar el estado de las actualizaciones automáticas de Watchtower
# Verifica la configuración y el estado de los contenedores monitoreados

param(
    [switch]$ShowDetails = $false,
    [switch]$TestUpdate = $false
)

Write-Host "=== VERIFICACIÓN DE ACTUALIZACIONES AUTOMÁTICAS DE WATCHTOWER ===" -ForegroundColor Green
Write-Host ""

# Función para verificar el estado de Watchtower
function Test-WatchtowerStatus {
    Write-Host "🔍 Verificando estado de Watchtower..." -ForegroundColor Yellow
    
    $watchtowerStatus = docker ps --filter "name=watchtower" --format "{{.Status}}" 2>$null
    if ($watchtowerStatus) {
        Write-Host "✅ Watchtower está ejecutándose: $watchtowerStatus" -ForegroundColor Green
        
        # Verificar configuración
        $envVars = docker inspect watchtower --format='{{range .Config.Env}}{{println .}}{{end}}' 2>$null
        $pollInterval = ($envVars | Where-Object { $_ -like "WATCHTOWER_POLL_INTERVAL=*" }) -replace "WATCHTOWER_POLL_INTERVAL=", ""
        $debug = ($envVars | Where-Object { $_ -like "WATCHTOWER_DEBUG=*" }) -replace "WATCHTOWER_DEBUG=", ""
        $cleanup = ($envVars | Where-Object { $_ -like "WATCHTOWER_CLEANUP=*" }) -replace "WATCHTOWER_CLEANUP=", ""
        
        Write-Host "   📅 Intervalo de verificación: $(if($pollInterval) { "$([math]::Round($pollInterval/3600, 1)) horas" } else { "24 horas (default)" })"
        Write-Host "   🐛 Debug habilitado: $(if($debug -eq 'true') { 'Sí' } else { 'No' })"
        Write-Host "   🧹 Cleanup habilitado: $(if($cleanup -eq 'true') { 'Sí' } else { 'No' })"
        
        return $true
    } else {
        Write-Host "❌ Watchtower no está ejecutándose" -ForegroundColor Red
        return $false
    }
}

# Función para verificar contenedores monitoreados
function Test-MonitoredContainers {
    Write-Host ""
    Write-Host "📋 Verificando contenedores monitoreados..." -ForegroundColor Yellow
    
    $containers = @("jackett", "sonarr", "radarr", "qbittorrent", "jellyfin", "jellyseerr")
    $monitoredCount = 0
    
    foreach ($container in $containers) {
        $status = docker ps --filter "name=$container" --format "{{.Status}}" 2>$null
        $label = docker inspect $container --format='{{index .Config.Labels "com.centurylinklabs.watchtower.enable"}}' 2>$null
        
        if ($status) {
            $isMonitored = $label -eq "true"
            $statusIcon = if ($isMonitored) { "✅" } else { "⚠️" }
            $statusText = if ($isMonitored) { "Monitoreado" } else { "NO monitoreado" }
            
            Write-Host "   $statusIcon $container`: $statusText ($status)" -ForegroundColor $(if ($isMonitored) { "Green" } else { "Yellow" })
            
            if ($isMonitored) { $monitoredCount++ }
            
            if ($ShowDetails -and $isMonitored) {
                $image = docker inspect $container --format='{{.Config.Image}}' 2>$null
                Write-Host "      🖼️ Imagen: $image" -ForegroundColor Gray
            }
        } else {
            Write-Host "   ❌ $container`: No está ejecutándose" -ForegroundColor Red
        }
    }
    
    Write-Host ""
    Write-Host "📊 Resumen: $monitoredCount de $($containers.Count) contenedores monitoreados" -ForegroundColor Cyan
    return $monitoredCount
}

# Función para mostrar logs recientes de Watchtower
function Show-WatchtowerLogs {
    Write-Host ""
    Write-Host "📜 Logs recientes de Watchtower..." -ForegroundColor Yellow
    
    try {
        $logs = docker logs watchtower --tail 5 2>$null
        if ($logs) {
            foreach ($log in $logs) {
                Write-Host "   $log" -ForegroundColor Gray
            }
        } else {
            Write-Host "   No hay logs disponibles" -ForegroundColor Yellow
        }
    } catch {
        Write-Host "   Error obteniendo logs: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Función para realizar prueba de actualización
function Test-UpdateCapability {
    Write-Host ""
    Write-Host "🧪 Realizando prueba de actualización..." -ForegroundColor Yellow

    try {
        Write-Host "   Ejecutando Watchtower en modo test (run-once)..."
        $result = docker run --rm -v /var/run/docker.sock:/var/run/docker.sock containrrr/watchtower --run-once --debug jackett 2>&1

        if ($LASTEXITCODE -eq 0) {
            $scanned = ($result | Select-String "Scanned=(\d+)" | ForEach-Object { $_.Matches[0].Groups[1].Value })
            $updated = ($result | Select-String "Updated=(\d+)" | ForEach-Object { $_.Matches[0].Groups[1].Value })
            $failed = ($result | Select-String "Failed=(\d+)" | ForEach-Object { $_.Matches[0].Groups[1].Value })

            Write-Host "   ✅ Prueba completada exitosamente" -ForegroundColor Green
            Write-Host "      📊 Contenedores escaneados: $scanned"
            Write-Host "      🔄 Contenedores actualizados: $updated"
            Write-Host "      ❌ Fallos: $failed"
        } else {
            Write-Host "   ❌ Error en la prueba de actualización" -ForegroundColor Red
        }
    } catch {
        Write-Host "   ❌ Error ejecutando prueba: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Función para mostrar próxima verificación
function Show-NextCheck {
    Write-Host ""
    Write-Host "⏰ Información de programación..." -ForegroundColor Yellow
    
    try {
        $logs = docker logs watchtower 2>$null | Select-String "Scheduling first run" | Select-Object -Last 1
        if ($logs) {
            $logLine = $logs.ToString()
            if ($logLine -match 'Scheduling first run: (.+)"') {
                $nextRun = $matches[1]
                Write-Host "   📅 Próxima verificación programada: $nextRun" -ForegroundColor Cyan
            }
        }
        
        $intervalLogs = docker logs watchtower 2>$null | Select-String "will be performed in" | Select-Object -Last 1
        if ($intervalLogs) {
            $intervalLine = $intervalLogs.ToString()
            if ($intervalLine -match 'performed in (.+)"') {
                $timeRemaining = $matches[1]
                Write-Host "   ⏳ Tiempo restante: $timeRemaining" -ForegroundColor Cyan
            }
        }
    } catch {
        Write-Host "   ⚠️ No se pudo obtener información de programación" -ForegroundColor Yellow
    }
}

# Ejecutar verificaciones
$watchtowerOk = Test-WatchtowerStatus
$monitoredCount = Test-MonitoredContainers

if ($ShowDetails) {
    Show-WatchtowerLogs
}

Show-NextCheck

if ($TestUpdate -and $watchtowerOk) {
    Test-UpdateCapability
}

# Resumen final
Write-Host ""
Write-Host "=== RESUMEN ===" -ForegroundColor Green
if ($watchtowerOk -and $monitoredCount -gt 0) {
    Write-Host "✅ Sistema de actualizaciones automáticas configurado correctamente" -ForegroundColor Green
    Write-Host "   - Watchtower ejecutándose con intervalo de 6 horas"
    Write-Host "   - $monitoredCount contenedores monitoreados"
    Write-Host "   - Debug y cleanup habilitados"
} else {
    Write-Host "⚠️ Se requiere atención en la configuración" -ForegroundColor Yellow
    if (-not $watchtowerOk) {
        Write-Host "   - Watchtower no está funcionando correctamente"
    }
    if ($monitoredCount -eq 0) {
        Write-Host "   - No hay contenedores monitoreados"
    }
}

Write-Host ""
Write-Host "Para más detalles, ejecuta: .\verify-auto-updates.ps1 -ShowDetails"
Write-Host "Para probar actualizaciones: .\verify-auto-updates.ps1 -TestUpdate"
