<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>PayloadContent</key>
    <array>
        <dict>
            <key>DNSSettings</key>
            <dict>
                <key>DNSProtocol</key>
                <string>HTTPS</string>
                <key>ServerURL</key>
                <string>https://tankeguard.duckdns.org/dns-query</string>
                <key>ServerAddresses</key>
                <array>
                    <string>*************</string>
                </array>
            </dict>
            <key>PayloadDescription</key>
            <string>Configura AdGuard Home como servidor DNS-over-TLS</string>
            <key>PayloadDisplayName</key>
            <string>AdGuard Home DNS-over-TLS</string>
            <key>PayloadIdentifier</key>
            <string>com.adguard.dns.profile</string>
            <key>PayloadType</key>
            <string>com.apple.dnsSettings.managed</string>
            <key>PayloadUUID</key>
            <string>A1B2C3D4-E5F6-7890-ABCD-EF1234567890</string>
            <key>PayloadVersion</key>
            <integer>1</integer>
        </dict>
    </array>
    <key>PayloadDescription</key>
    <string>Perfil DNS para usar AdGuard Home con DNS-over-TLS</string>
    <key>PayloadDisplayName</key>
    <string>AdGuard Home DNS</string>
    <key>PayloadIdentifier</key>
    <string>com.adguard.dns</string>
    <key>PayloadRemovalDisallowed</key>
    <false/>
    <key>PayloadType</key>
    <string>Configuration</string>
    <key>PayloadUUID</key>
    <string>B2C3D4E5-F6G7-8901-BCDE-F23456789012</string>
    <key>PayloadVersion</key>
    <integer>1</integer>
</dict>
</plist>
