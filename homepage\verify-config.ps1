# Homepage Configuration Verification Script
# Script para verificar la configuración de Homepage

param(
    [switch]$Detailed,
    [switch]$TestConnections,
    [switch]$Help
)

# Configuración
$ConfigDir = "C:\docker\duckdns_updater\homepage\config"
$ApiKeysFile = "$ConfigDir\api-keys.env"

function Show-Help {
    Write-Host "=== Homepage Configuration Verification ===" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Uso: .\verify-config.ps1 [OPCIÓN]" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "Opciones:" -ForegroundColor Green
    Write-Host "  -Detailed        Verificación detallada de configuración"
    Write-Host "  -TestConnections Prueba conexiones a servicios"
    Write-Host "  -Help           Muestra esta ayuda"
    Write-Host ""
}

function Test-ConfigFiles {
    Write-Host "🔍 Verificando archivos de configuración..." -ForegroundColor Blue
    
    $requiredFiles = @(
        "settings.yaml",
        "services.yaml", 
        "widgets.yaml",
        "bookmarks.yaml",
        "docker.yaml"
    )
    
    $optionalFiles = @(
        "api-keys.env",
        "services-template.yaml"
    )
    
    $allGood = $true
    
    foreach ($file in $requiredFiles) {
        $filePath = "$ConfigDir\$file"
        if (Test-Path $filePath) {
            Write-Host "✅ $file" -ForegroundColor Green
        } else {
            Write-Host "❌ $file (REQUERIDO)" -ForegroundColor Red
            $allGood = $false
        }
    }
    
    foreach ($file in $optionalFiles) {
        $filePath = "$ConfigDir\$file"
        if (Test-Path $filePath) {
            Write-Host "✅ $file (opcional)" -ForegroundColor Green
        } else {
            Write-Host "⚠️  $file (opcional, pero recomendado)" -ForegroundColor Yellow
        }
    }
    
    return $allGood
}

function Test-ApiKeys {
    Write-Host "🔑 Verificando configuración de API keys..." -ForegroundColor Blue
    
    if (-not (Test-Path $ApiKeysFile)) {
        Write-Host "❌ Archivo api-keys.env no encontrado" -ForegroundColor Red
        Write-Host "   Crea el archivo basándote en la plantilla proporcionada" -ForegroundColor Yellow
        return $false
    }
    
    $content = Get-Content $ApiKeysFile -Raw
    $placeholders = @(
        "TU_API_KEY_HOME_ASSISTANT",
        "TU_USUARIO_ADGUARD",
        "TU_PASSWORD_ADGUARD", 
        "TU_API_KEY_SONARR",
        "TU_API_KEY_RADARR",
        "TU_API_KEY_JACKETT",
        "TU_API_KEY_JELLYSEERR",
        "TU_API_KEY_JELLYFIN",
        "TU_USUARIO_QBITTORRENT",
        "TU_PASSWORD_QBITTORRENT"
    )
    
    $unconfigured = @()
    foreach ($placeholder in $placeholders) {
        if ($content -match $placeholder) {
            $unconfigured += $placeholder
        }
    }
    
    if ($unconfigured.Count -eq 0) {
        Write-Host "✅ Todas las API keys están configuradas" -ForegroundColor Green
        return $true
    } else {
        Write-Host "⚠️  API keys pendientes de configurar:" -ForegroundColor Yellow
        foreach ($key in $unconfigured) {
            Write-Host "   - $key" -ForegroundColor Yellow
        }
        return $false
    }
}

function Test-ServiceConnections {
    Write-Host "🌐 Probando conexiones a servicios..." -ForegroundColor Blue
    
    $services = @(
        @{Name="Jellyfin"; Url="http://host.docker.internal:8096"; Container="jellyfin"},
        @{Name="Sonarr"; Url="http://host.docker.internal:8989"; Container="sonarr"},
        @{Name="Radarr"; Url="http://host.docker.internal:7878"; Container="radarr"},
        @{Name="Jackett"; Url="http://host.docker.internal:9117"; Container="jackett"},
        @{Name="qBittorrent"; Url="http://host.docker.internal:8091"; Container="qbittorrent"},
        @{Name="AdGuard"; Url="http://host.docker.internal:8080"; Container="adguard"},
        @{Name="Jellyseerr"; Url="http://host.docker.internal:5055"; Container="jellyseerr"},
        @{Name="Portainer"; Url="http://host.docker.internal:9000"; Container="portainer"},
        @{Name="FlareSolverr"; Url="http://host.docker.internal:8191"; Container="flaresolverr"}
    )
    
    foreach ($service in $services) {
        # Verificar si el contenedor está ejecutándose
        $containerStatus = docker ps --filter "name=$($service.Container)" --format "{{.Status}}" 2>$null
        
        if ($containerStatus) {
            Write-Host "✅ $($service.Name) - Contenedor ejecutándose" -ForegroundColor Green
            
            # Probar conectividad HTTP
            try {
                $response = Invoke-WebRequest -Uri $service.Url -TimeoutSec 5 -UseBasicParsing -ErrorAction Stop
                Write-Host "   🌐 HTTP OK ($($response.StatusCode))" -ForegroundColor Green
            }
            catch {
                Write-Host "   ⚠️  HTTP no responde" -ForegroundColor Yellow
            }
        } else {
            Write-Host "❌ $($service.Name) - Contenedor no ejecutándose" -ForegroundColor Red
        }
    }
}

function Test-DockerAccess {
    Write-Host "🐳 Verificando acceso a Docker..." -ForegroundColor Blue
    
    try {
        $dockerVersion = docker --version
        Write-Host "✅ Docker disponible: $dockerVersion" -ForegroundColor Green
        
        # Verificar acceso al socket
        $containers = docker ps --format "{{.Names}}" 2>$null
        if ($containers) {
            Write-Host "✅ Socket de Docker accesible" -ForegroundColor Green
            Write-Host "   Contenedores ejecutándose: $($containers.Count)" -ForegroundColor Cyan
        } else {
            Write-Host "⚠️  No se pueden listar contenedores" -ForegroundColor Yellow
        }
        
        return $true
    }
    catch {
        Write-Host "❌ Docker no disponible" -ForegroundColor Red
        return $false
    }
}

function Test-NetworkConnectivity {
    Write-Host "🔗 Verificando conectividad de red..." -ForegroundColor Blue
    
    # Verificar redes de Docker
    $networks = @("caddy_network", "arr_net")
    
    foreach ($network in $networks) {
        $networkExists = docker network ls --filter "name=$network" --format "{{.Name}}" 2>$null
        if ($networkExists) {
            Write-Host "✅ Red $network existe" -ForegroundColor Green
        } else {
            Write-Host "❌ Red $network no encontrada" -ForegroundColor Red
        }
    }
    
    # Verificar acceso a dominios externos
    $domains = @(
        "tankeeee2.duckdns.org",
        "tankeflix.duckdns.org", 
        "tankeguard.duckdns.org"
    )
    
    foreach ($domain in $domains) {
        try {
            $response = Invoke-WebRequest -Uri "https://$domain" -TimeoutSec 10 -UseBasicParsing -ErrorAction Stop
            Write-Host "✅ $domain accesible (HTTPS)" -ForegroundColor Green
        }
        catch {
            Write-Host "⚠️  $domain no accesible" -ForegroundColor Yellow
        }
    }
}

function Show-Summary {
    Write-Host ""
    Write-Host "📋 RESUMEN DE VERIFICACIÓN" -ForegroundColor Cyan
    Write-Host "=========================" -ForegroundColor Cyan
    
    $configOk = Test-ConfigFiles
    $apiKeysOk = Test-ApiKeys
    $dockerOk = Test-DockerAccess
    
    Write-Host ""
    
    if ($configOk -and $apiKeysOk -and $dockerOk) {
        Write-Host "🎉 ¡Configuración completa y lista!" -ForegroundColor Green
        Write-Host ""
        Write-Host "Próximos pasos:" -ForegroundColor Cyan
        Write-Host "1. Inicia Homepage: .\setup-homepage.ps1 -Start" -ForegroundColor White
        Write-Host "2. Accede a: https://tankeeee2.duckdns.org" -ForegroundColor White
        Write-Host "3. Verifica que todos los widgets funcionen correctamente" -ForegroundColor White
    } else {
        Write-Host "⚠️  Configuración incompleta" -ForegroundColor Yellow
        Write-Host ""
        Write-Host "Acciones requeridas:" -ForegroundColor Cyan
        if (-not $configOk) {
            Write-Host "- Crear archivos de configuración faltantes" -ForegroundColor White
        }
        if (-not $apiKeysOk) {
            Write-Host "- Configurar API keys en api-keys.env" -ForegroundColor White
        }
        if (-not $dockerOk) {
            Write-Host "- Verificar instalación y estado de Docker" -ForegroundColor White
        }
    }
}

# Script principal
function Main {
    Write-Host "=== Homepage Configuration Verification ===" -ForegroundColor Cyan
    Write-Host ""
    
    if ($Help) {
        Show-Help
        return
    }
    
    if ($Detailed) {
        Test-ConfigFiles | Out-Null
        Write-Host ""
        Test-ApiKeys | Out-Null
        Write-Host ""
        Test-DockerAccess | Out-Null
        Write-Host ""
        Test-NetworkConnectivity
        Write-Host ""
    }
    
    if ($TestConnections) {
        Test-ServiceConnections
        Write-Host ""
    }
    
    Show-Summary
}

# Ejecutar script
Main
