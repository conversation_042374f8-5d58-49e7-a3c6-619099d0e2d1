#!/usr/bin/env python3
"""
Script de prueba para verificar la funcionalidad de agregar contenido
"""

import asyncio
import httpx
import json
from typing import Dict, Any

# Configuración
SONARR_URL = "http://*************:8989"
RADARR_URL = "http://*************:7878"
SONARR_API_KEY = "2f9da07e98744f4890c0960d15ead111"
RADARR_API_KEY = "cfc0cde90b0f483eb4190dc634ca86f2"

async def test_api_connection(service_name: str, base_url: str, api_key: str):
    """Probar conexión con la API"""
    print(f"\n🔗 Probando conexión con {service_name}...")
    
    async with httpx.AsyncClient() as client:
        try:
            response = await client.get(
                f"{base_url}/api/v3/system/status",
                headers={'X-Api-Key': api_key}
            )
            response.raise_for_status()
            status = response.json()
            print(f"✅ {service_name} conectado: {status.get('appName')} v{status.get('version')}")
            return True
        except Exception as e:
            print(f"❌ Error conectando con {service_name}: {e}")
            return False

async def get_quality_profiles(service_name: str, base_url: str, api_key: str):
    """Obtener perfiles de calidad"""
    print(f"\n📊 Obteniendo perfiles de calidad de {service_name}...")
    
    async with httpx.AsyncClient() as client:
        try:
            response = await client.get(
                f"{base_url}/api/v3/qualityprofile",
                headers={'X-Api-Key': api_key}
            )
            response.raise_for_status()
            profiles = response.json()
            
            print(f"✅ Perfiles de calidad encontrados:")
            for profile in profiles:
                print(f"   - ID: {profile['id']}, Nombre: {profile['name']}")
            
            return profiles
        except Exception as e:
            print(f"❌ Error obteniendo perfiles: {e}")
            return []

async def get_root_folders(service_name: str, base_url: str, api_key: str):
    """Obtener carpetas raíz"""
    print(f"\n📁 Obteniendo carpetas raíz de {service_name}...")
    
    async with httpx.AsyncClient() as client:
        try:
            response = await client.get(
                f"{base_url}/api/v3/rootfolder",
                headers={'X-Api-Key': api_key}
            )
            response.raise_for_status()
            folders = response.json()
            
            print(f"✅ Carpetas raíz encontradas:")
            for folder in folders:
                print(f"   - ID: {folder['id']}, Ruta: {folder['path']}")
            
            return folders
        except Exception as e:
            print(f"❌ Error obteniendo carpetas: {e}")
            return []

async def search_content(service_name: str, base_url: str, api_key: str, term: str):
    """Buscar contenido"""
    print(f"\n🔍 Buscando '{term}' en {service_name}...")
    
    endpoint = "movie/lookup" if service_name == "Radarr" else "series/lookup"
    
    async with httpx.AsyncClient() as client:
        try:
            response = await client.get(
                f"{base_url}/api/v3/{endpoint}?term={term}",
                headers={'X-Api-Key': api_key}
            )
            response.raise_for_status()
            results = response.json()
            
            print(f"✅ Encontrados {len(results)} resultados")
            if results:
                first_result = results[0]
                print(f"   Primer resultado: {first_result.get('title')} ({first_result.get('year', 'N/A')})")
                return first_result
            
            return None
        except Exception as e:
            print(f"❌ Error buscando contenido: {e}")
            return None

async def test_add_content(service_name: str, base_url: str, api_key: str, content_data: Dict[str, Any]):
    """Probar agregar contenido"""
    print(f"\n➕ Intentando agregar '{content_data.get('title')}' en {service_name}...")
    
    # Obtener datos necesarios
    profiles = await get_quality_profiles(service_name, base_url, api_key)
    folders = await get_root_folders(service_name, base_url, api_key)
    
    if not profiles or not folders:
        print("❌ No se pudieron obtener perfiles o carpetas")
        return False
    
    # Preparar datos para agregar
    endpoint = "movie" if service_name == "Radarr" else "series"
    
    add_data = {
        'title': content_data.get('title'),
        'qualityProfileId': profiles[0]['id'],  # Usar primer perfil
        'rootFolderPath': folders[0]['path'],   # Usar primera carpeta
        'monitored': True,
    }
    
    # Agregar campos específicos del servicio
    if service_name == "Radarr":
        add_data.update({
            'tmdbId': content_data.get('tmdbId'),
            'year': content_data.get('year'),
            'addOptions': {'searchForMovie': True}
        })
    else:  # Sonarr
        add_data.update({
            'tvdbId': content_data.get('tvdbId'),
            'languageProfileId': 1,  # Requerido para Sonarr
            'addOptions': {'searchForMissingEpisodes': True}
        })
    
    print(f"📤 Datos a enviar:")
    print(json.dumps(add_data, indent=2))
    
    async with httpx.AsyncClient() as client:
        try:
            response = await client.post(
                f"{base_url}/api/v3/{endpoint}",
                headers={'X-Api-Key': api_key},
                json=add_data
            )
            
            print(f"📥 Respuesta HTTP: {response.status_code}")
            
            if response.status_code == 201:
                result = response.json()
                print(f"✅ ¡Contenido agregado exitosamente!")
                print(f"   ID: {result.get('id')}")
                print(f"   Título: {result.get('title')}")
                return True
            else:
                print(f"❌ Error al agregar contenido:")
                print(f"   Código: {response.status_code}")
                print(f"   Respuesta: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Error en petición: {e}")
            return False

async def main():
    print("🧪 Iniciando pruebas de funcionalidad de agregar contenido...")
    
    # Probar conexiones
    sonarr_ok = await test_api_connection("Sonarr", SONARR_URL, SONARR_API_KEY)
    radarr_ok = await test_api_connection("Radarr", RADARR_URL, RADARR_API_KEY)
    
    if not sonarr_ok or not radarr_ok:
        print("\n❌ No se pudo conectar con las APIs. Abortando pruebas.")
        return
    
    # Buscar contenido de prueba
    print("\n" + "="*60)
    print("PRUEBA 1: AGREGAR SERIE EN SONARR")
    print("="*60)
    
    series_data = await search_content("Sonarr", SONARR_URL, SONARR_API_KEY, "Lost")
    if series_data:
        await test_add_content("Sonarr", SONARR_URL, SONARR_API_KEY, series_data)
    
    print("\n" + "="*60)
    print("PRUEBA 2: AGREGAR PELÍCULA EN RADARR")
    print("="*60)
    
    movie_data = await search_content("Radarr", RADARR_URL, RADARR_API_KEY, "Up")
    if movie_data:
        await test_add_content("Radarr", RADARR_URL, RADARR_API_KEY, movie_data)
    
    print("\n🎉 Pruebas completadas!")
    print("\n📝 Si las pruebas fueron exitosas, el bot debería funcionar correctamente.")
    print("   Ahora puedes probar en Telegram: /agregar Lost")

if __name__ == "__main__":
    asyncio.run(main())
