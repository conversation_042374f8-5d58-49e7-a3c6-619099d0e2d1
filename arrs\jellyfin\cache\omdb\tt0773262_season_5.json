{"Title": "<PERSON>", "seriesID": "tt0773262", "Season": 5, "totalSeasons": 8, "Episodes": [{"Title": "My Bad", "Year": "2010", "Rated": "TV-MA", "Released": "26 Sep 2010", "Runtime": "53 min", "Genre": "Crime, Drama, Mystery", "Director": "<PERSON>", "Writer": "<PERSON>, <PERSON>, <PERSON>", "Actors": "<PERSON>, <PERSON>, <PERSON>", "Plot": "In the Season 5 premiere, <PERSON> reels from <PERSON>'s murder while <PERSON> stirs up trouble by suggesting that <PERSON>'s death doesn't fit <PERSON>'s profile.", "Language": "English", "Country": "United States", "Poster": "https://m.media-amazon.com/images/M/MV5BMmZiN2RkMjktZTdmMy00ZjRhLWFmZWEtNDJjYTg1YzllNGQ5XkEyXkFqcGc@._V1_SX300.jpg", "imdbRating": "8.8", "imdbVotes": "7500", "imdbID": "tt1615586", "Episode": 1}, {"Title": "Hello Bandit", "Year": "2010", "Rated": "TV-MA", "Released": "03 Oct 2010", "Runtime": "50 min", "Genre": "Crime, Drama, Mystery", "Director": "<PERSON>", "Writer": "<PERSON>, <PERSON>, <PERSON>", "Actors": "<PERSON>, <PERSON>, <PERSON>", "Plot": "<PERSON> fights his dark urges, but a shocking discovery puts him on the trail of a new victim; <PERSON><PERSON> hosts her brother and his children but finds herself squeezed out of her own place.", "Language": "English", "Country": "United States", "Poster": "https://m.media-amazon.com/images/M/MV5BMTMyMTkwNzM5MV5BMl5BanBnXkFtZTcwOTc4MDk2Mw@@._V1_SX300.jpg", "imdbRating": "8.1", "imdbVotes": "5432", "imdbID": "tt1681772", "Episode": 2}, {"Title": "Practically Perfect", "Year": "2010", "Rated": "TV-MA", "Released": "10 Oct 2010", "Runtime": "49 min", "Genre": "Crime, Drama, Mystery", "Director": "<PERSON>", "Writer": "<PERSON>, <PERSON>, <PERSON>", "Actors": "<PERSON>, <PERSON>, <PERSON>", "Plot": "In an effort to create more free time to stalk and kill victims, <PERSON> hires a nanny; <PERSON><PERSON> is annoyed by her new rookie partner; <PERSON> pursues the theory that <PERSON> did not kill <PERSON>.", "Language": "English", "Country": "United States", "Poster": "https://m.media-amazon.com/images/M/MV5BMjIxODU3ODU3NF5BMl5BanBnXkFtZTcwOTU0Njc5Mw@@._V1_SX300.jpg", "imdbRating": "8.3", "imdbVotes": "5590", "imdbID": "tt1691535", "Episode": 3}, {"Title": "Beauty And The Beast", "Year": "2010", "Rated": "TV-MA", "Released": "17 Oct 2010", "Runtime": "51 min", "Genre": "Crime, Drama, Mystery", "Director": "<PERSON>", "Writer": "<PERSON>, <PERSON>, <PERSON>", "Actors": "<PERSON>, <PERSON>, <PERSON>", "Plot": "<PERSON> has to save a life instead of taking one; <PERSON><PERSON> has a scary confrontation with a key suspect in the <PERSON> Mu<PERSON>e murder cases; <PERSON> continues following up on the strange similarities between '<PERSON>' and <PERSON>.", "Language": "English", "Country": "United States", "Poster": "https://m.media-amazon.com/images/M/MV5BMjAxNDkxOTg1NV5BMl5BanBnXkFtZTcwODQ0Njc5Mw@@._V1_SX300.jpg", "imdbRating": "8.3", "imdbVotes": "5284", "imdbID": "tt1691360", "Episode": 4}, {"Title": "First Blood", "Year": "2010", "Rated": "TV-MA", "Released": "24 Oct 2010", "Runtime": "50 min", "Genre": "Crime, Drama, Mystery", "Director": "<PERSON>", "Writer": "<PERSON>, <PERSON>, <PERSON>", "Actors": "<PERSON>, <PERSON>, <PERSON>", "Plot": "When <PERSON> is saddled with an unwanted conspirator, he is forced to make some hard decisions about her fate; <PERSON> wonders if <PERSON>'s death has awakened a darkness inside <PERSON>; <PERSON> enlists an old friend to look into <PERSON>.", "Language": "English", "Country": "United States", "Poster": "https://m.media-amazon.com/images/M/MV5BMTM2MDQwNTc2OV5BMl5BanBnXkFtZTcwNDgxMDMwNA@@._V1_SX300.jpg", "imdbRating": "7.9", "imdbVotes": "5308", "imdbID": "tt1699327", "Episode": 5}, {"Title": "Everything Is Illumenated", "Year": "2010", "Rated": "TV-MA", "Released": "31 Oct 2010", "Runtime": "49 min", "Genre": "Crime, Drama, Mystery", "Director": "<PERSON>", "Writer": "<PERSON>, <PERSON>, <PERSON>", "Actors": "<PERSON>, <PERSON>, <PERSON>", "Plot": "Trying to get his life back on track, <PERSON> hunts a new target, but is distracted and drawn into a precarious situation while helping his troubled acquaintance; <PERSON><PERSON> discovers an interesting lead in the <PERSON> Mu<PERSON>e murder cases.", "Language": "English", "Country": "United States", "Poster": "https://m.media-amazon.com/images/M/MV5BMjIyNDAwNzIxNl5BMl5BanBnXkFtZTcwNDIyNzQwNA@@._V1_SX300.jpg", "imdbRating": "8.7", "imdbVotes": "5935", "imdbID": "tt1699544", "Episode": 6}, {"Title": "Circle Us", "Year": "2010", "Rated": "TV-MA", "Released": "07 Nov 2010", "Runtime": "48 min", "Genre": "Crime, Drama, Mystery", "Director": "<PERSON>", "Writer": "<PERSON>, <PERSON>, <PERSON>", "Actors": "<PERSON>, <PERSON>, <PERSON>", "Plot": "While helping <PERSON><PERSON>, <PERSON> finds himself on a collision course with <PERSON><PERSON><PERSON> when he is called to investigate a horrifying crime scene; the <PERSON> case leads to a violent standoff between <PERSON><PERSON> and the killers.", "Language": "English", "Country": "United States", "Poster": "https://m.media-amazon.com/images/M/MV5BMTQ0MTkxMzA1MV5BMl5BanBnXkFtZTcwMTYzOTYwNA@@._V1_SX300.jpg", "imdbRating": "8.3", "imdbVotes": "5043", "imdbID": "tt1721224", "Episode": 7}, {"Title": "Take It!", "Year": "2010", "Rated": "TV-MA", "Released": "14 Nov 2010", "Runtime": "52 min", "Genre": "Crime, Drama, Mystery", "Director": "<PERSON>", "Writer": "<PERSON>, <PERSON>, <PERSON>", "Actors": "<PERSON>, <PERSON>, <PERSON>", "Plot": "While attending a self-help seminar, <PERSON> and <PERSON><PERSON> stalk a violent killer; <PERSON><PERSON> gets into unexpected trouble from the fallout of the <PERSON> Muerte case, forcing <PERSON><PERSON> to make a difficult decision.", "Language": "English", "Country": "United States", "Poster": "https://m.media-amazon.com/images/M/MV5BMTc4NzM5MzM4Ml5BMl5BanBnXkFtZTcwMzM1NzkwNA@@._V1_SX300.jpg", "imdbRating": "8.8", "imdbVotes": "5352", "imdbID": "tt1720655", "Episode": 8}, {"Title": "Teenage Wasteland", "Year": "2010", "Rated": "TV-MA", "Released": "21 Nov 2010", "Runtime": "55 min", "Genre": "Crime, Drama, Mystery", "Director": "<PERSON>", "Writer": "<PERSON>, <PERSON>, <PERSON>", "Actors": "<PERSON>, <PERSON>, <PERSON>", "Plot": "In the midst of hunting their next victim, <PERSON> and <PERSON><PERSON> are surprised by the return of an unannounced guest; <PERSON><PERSON> is assigned to the file room, but manages to uncover new evidence in the Barrel Girl case.", "Language": "English", "Country": "United States", "Poster": "https://m.media-amazon.com/images/M/MV5BMjA1NzM5MTQyNV5BMl5BanBnXkFtZTcwMzAzMjIxNA@@._V1_SX300.jpg", "imdbRating": "8.9", "imdbVotes": "5562", "imdbID": "tt1733347", "Episode": 9}, {"Title": "In The Beginning", "Year": "2010", "Rated": "TV-MA", "Released": "28 Nov 2010", "Runtime": "53 min", "Genre": "Crime, Drama, Mystery", "Director": "<PERSON>", "Writer": "<PERSON>, <PERSON>, <PERSON>", "Actors": "<PERSON>, <PERSON>, <PERSON>", "Plot": "<PERSON> discovers a possible ally in the pursuit of <PERSON><PERSON>'s former captors, but he and <PERSON><PERSON> are forced to step down when homicide uncovers evidence linked to one of their prior victims.", "Language": "English", "Country": "United States", "Poster": "https://m.media-amazon.com/images/M/MV5BNTUxMTI2MjM5MF5BMl5BanBnXkFtZTcwMjQyNDQxNA@@._V1_SX300.jpg", "imdbRating": "8.9", "imdbVotes": "5954", "imdbID": "tt1733346", "Episode": 10}, {"Title": "Hop A Freighter", "Year": "2010", "Rated": "TV-MA", "Released": "05 Dec 2010", "Runtime": "47 min", "Genre": "Crime, Drama, Mystery", "Director": "<PERSON>", "Writer": "<PERSON>, <PERSON>, <PERSON>", "Actors": "<PERSON>, <PERSON>, <PERSON>", "Plot": "<PERSON> is forced to do damage control after he concedes that someone outside of Miami Metro has taken an interest in him and <PERSON><PERSON>.", "Language": "English", "Country": "United States", "Poster": "https://m.media-amazon.com/images/M/MV5BMTQxODc1NjMzM15BMl5BanBnXkFtZTcwMDM1ODYxNA@@._V1_SX300.jpg", "imdbRating": "8.8", "imdbVotes": "6241", "imdbID": "tt1733345", "Episode": 11}, {"Title": "The Big One", "Year": "2010", "Rated": "TV-MA", "Released": "12 Dec 2010", "Runtime": "56 min", "Genre": "Crime, Drama, Mystery", "Director": "<PERSON>", "Writer": "<PERSON>, <PERSON>, <PERSON>", "Actors": "<PERSON>, <PERSON>, <PERSON>", "Plot": "In the Season 5 finale, <PERSON> realizes that he and <PERSON><PERSON> are being lured into a trap and risks everything to escape; <PERSON> needs help that only <PERSON> can provide.", "Language": "English, German", "Country": "United States", "Poster": "https://m.media-amazon.com/images/M/MV5BMTU4MDExOTcyNV5BMl5BanBnXkFtZTcwMjEzMDkxNA@@._V1_SX300.jpg", "imdbRating": "8.8", "imdbVotes": "6307", "imdbID": "tt1733344", "Episode": 12}], "Response": "True"}