# Homepage Bookmarks Configuration - Configuración de Marcadores
# Documentación: https://gethomepage.dev/latest/configs/bookmarks/

# === SERVICIOS PRINCIPALES ===
- Servicios Principales:
    - Jellyfin:
        - icon: "jellyfin.png"
          href: "https://tankeflix.duckdns.org"
          description: "Servidor multimedia"
    - AdGuard Home:
        - icon: "adguard-home.png"
          href: "https://tankeguard.duckdns.org"
          description: "Filtrado DNS"
    - Jellyseerr:
        - icon: "jellyseerr.png"
          href: "https://tankejellyseerr.duckdns.org"
          description: "Solicitudes de contenido"



- Gestión de Contenido:
    - Sonarr:
        - icon: "sonarr.png"
          href: "https://tankesonarr.duckdns.org"
          description: "Gestión de series"
    - Radarr:
        - icon: "radarr.png"
          href: "https://tankeradarr.duckdns.org"
          description: "Gestión de películas"
    - Jack<PERSON>:
        - icon: "jackett.png"
          href: "https://tankejackett.duckdns.org"
          description: "Indexador de torrents"
    - qBittorrent:
        - icon: "qbittorrent.png"
          href: "https://tanketorrent.duckdns.org"
          description: "Cliente torrent"


# === ADMINISTRACIÓN ===
- Administración:
    - Portainer:
        - icon: "portainer.png"
          href: "http://host.docker.internal:9000"
          description: "Gestión de Docker"
    - DuckDNS:
        - icon: "duckdns.png"
          href: "https://www.duckdns.org"
          description: "DNS dinámico"
    - Caddy Admin:
        - icon: "caddy.png"
          href: "http://host.docker.internal:2019"
          description: "Administración de Caddy"

# === HERRAMIENTAS DE DESARROLLO ===
- Desarrollo:
    - GitHub:
        - icon: "github.png"
          href: "https://github.com"
          description: "Repositorios de código"
    - Docker Hub:
        - icon: "docker.png"
          href: "https://hub.docker.com"
          description: "Imágenes de Docker"
    - Homepage Docs:
        - icon: "homepage.png"
          href: "https://gethomepage.dev"
          description: "Documentación de Homepage"

# === MONITOREO Y LOGS ===
- Monitoreo:
    - Logs Caddy:
        - icon: "caddy.png"
          href: "file:///var/log/caddy"
          description: "Logs del proxy reverso"
    - Docker Stats:
        - icon: "docker.png"
          href: "http://host.docker.internal:9000/#!/1/docker/containers"
          description: "Estadísticas de contenedores"

# === RECURSOS ÚTILES ===
- Recursos:
    - Awesome Selfhosted:
        - icon: "github.png"
          href: "https://github.com/awesome-selfhosted/awesome-selfhosted"
          description: "Lista de software self-hosted"
    - r/selfhosted:
        - icon: "reddit.png"
          href: "https://www.reddit.com/r/selfhosted"
          description: "Comunidad de self-hosting"
    - LinuxServer.io:
        - icon: "linuxserver.png"
          href: "https://www.linuxserver.io"
          description: "Imágenes Docker optimizadas"

# === ENTRETENIMIENTO ===
- Entretenimiento:
    - IMDb:
        - icon: "imdb.png"
          href: "https://www.imdb.com"
          description: "Base de datos de películas"
    - TheTVDB:
        - icon: "thetvdb.png"
          href: "https://thetvdb.com"
          description: "Base de datos de series"
    - Trakt:
        - icon: "trakt.png"
          href: "https://trakt.tv"
          description: "Seguimiento de contenido"

# === CONFIGURACIÓN Y AYUDA ===
- Ayuda:
    - Sonarr Wiki:
        - icon: "sonarr.png"
          href: "https://wiki.servarr.com/sonarr"
          description: "Documentación de Sonarr"
    - Radarr Wiki:
        - icon: "radarr.png"
          href: "https://wiki.servarr.com/radarr"
          description: "Documentación de Radarr"
    - Jellyfin Docs:
        - icon: "jellyfin.png"
          href: "https://jellyfin.org/docs"
          description: "Documentación de Jellyfin"
    - AdGuard Docs:
        - icon: "adguard-home.png"
          href: "https://github.com/AdguardTeam/AdGuardHome/wiki"
          description: "Documentación de AdGuard"

# === HERRAMIENTAS DE SISTEMA ===
- Sistema:
    - Speedtest:
        - icon: "speedtest.png"
          href: "https://www.speedtest.net"
          description: "Test de velocidad"
    - What's My IP:
        - icon: "network.png"
          href: "https://whatismyipaddress.com"
          description: "Información de IP pública"
    - SSL Checker:
        - icon: "ssl.png"
          href: "https://www.ssllabs.com/ssltest"
          description: "Verificador de certificados SSL"
