# Homepage Setup Script - Script de Configuración de Homepage
# Este script automatiza la configuración inicial de Homepage

param(
    [switch]$Start,
    [switch]$Stop,
    [switch]$Restart,
    [switch]$Logs,
    [switch]$Status,
    [switch]$Help
)

# Configuración
$DockerComposeFile = "C:\docker\duckdns_updater\DNS-compose.yml"
$HomepageConfigDir = "C:\docker\duckdns_updater\homepage\config"
$ServiceName = "homepage"

# Función para mostrar ayuda
function Show-Help {
    Write-Host "=== Homepage Setup Script ===" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Uso: .\setup-homepage.ps1 [OPCIÓN]" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "Opciones disponibles:" -ForegroundColor Green
    Write-Host "  -Start     Inicia el servicio Homepage"
    Write-Host "  -Stop      Detiene el servicio Homepage"
    Write-Host "  -Restart   Reinicia el servicio Homepage"
    Write-Host "  -Logs      Muestra los logs de Homepage"
    Write-Host "  -Status    Muestra el estado del servicio"
    Write-Host "  -Help      Muestra esta ayuda"
    Write-Host ""
    Write-Host "Ejemplos:" -ForegroundColor Magenta
    Write-Host "  .\setup-homepage.ps1 -Start"
    Write-Host "  .\setup-homepage.ps1 -Logs"
    Write-Host "  .\setup-homepage.ps1 -Status"
    Write-Host ""
}

# Función para verificar Docker
function Test-Docker {
    try {
        docker --version | Out-Null
        return $true
    }
    catch {
        Write-Host "❌ Docker no está instalado o no está en el PATH" -ForegroundColor Red
        return $false
    }
}

# Función para verificar Docker Compose
function Test-DockerCompose {
    if (-not (Test-Path $DockerComposeFile)) {
        Write-Host "❌ Archivo docker-compose no encontrado: $DockerComposeFile" -ForegroundColor Red
        return $false
    }
    return $true
}

# Función para iniciar Homepage
function Start-Homepage {
    Write-Host "🚀 Iniciando Homepage..." -ForegroundColor Green
    
    Set-Location "C:\docker\duckdns_updater"
    
    try {
        docker-compose up -d $ServiceName
        Write-Host "✅ Homepage iniciado correctamente" -ForegroundColor Green
        Write-Host "🌐 Accede a: https://tankeeee2.duckdns.org" -ForegroundColor Cyan
        Write-Host "🏠 URL local: http://localhost:3001" -ForegroundColor Cyan
    }
    catch {
        Write-Host "❌ Error al iniciar Homepage: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Función para detener Homepage
function Stop-Homepage {
    Write-Host "🛑 Deteniendo Homepage..." -ForegroundColor Yellow
    
    Set-Location "C:\docker\duckdns_updater"
    
    try {
        docker-compose stop $ServiceName
        Write-Host "✅ Homepage detenido correctamente" -ForegroundColor Green
    }
    catch {
        Write-Host "❌ Error al detener Homepage: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Función para reiniciar Homepage
function Restart-Homepage {
    Write-Host "🔄 Reiniciando Homepage..." -ForegroundColor Blue
    
    Set-Location "C:\docker\duckdns_updater"
    
    try {
        docker-compose restart $ServiceName
        Write-Host "✅ Homepage reiniciado correctamente" -ForegroundColor Green
        Write-Host "🌐 Accede a: https://tankeeee2.duckdns.org" -ForegroundColor Cyan
    }
    catch {
        Write-Host "❌ Error al reiniciar Homepage: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Función para mostrar logs
function Show-Logs {
    Write-Host "📋 Mostrando logs de Homepage..." -ForegroundColor Blue
    Write-Host "Presiona Ctrl+C para salir" -ForegroundColor Yellow
    Write-Host ""
    
    try {
        docker logs -f $ServiceName
    }
    catch {
        Write-Host "❌ Error al mostrar logs: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Función para mostrar estado
function Show-Status {
    Write-Host "📊 Estado de Homepage:" -ForegroundColor Blue
    Write-Host ""
    
    try {
        # Estado del contenedor
        $containerStatus = docker ps -a --filter "name=$ServiceName" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
        Write-Host $containerStatus
        
        Write-Host ""
        
        # Información adicional
        $containerInfo = docker inspect $ServiceName --format "{{.State.Status}}" 2>$null
        if ($containerInfo) {
            Write-Host "Estado: $containerInfo" -ForegroundColor Green
            
            # Verificar conectividad
            Write-Host "🔍 Verificando conectividad..." -ForegroundColor Yellow
            try {
                $response = Invoke-WebRequest -Uri "http://localhost:3001" -TimeoutSec 5 -UseBasicParsing
                Write-Host "✅ Homepage responde correctamente (HTTP $($response.StatusCode))" -ForegroundColor Green
            }
            catch {
                Write-Host "⚠️  Homepage no responde en el puerto 3001" -ForegroundColor Yellow
            }
        }
        else {
            Write-Host "❌ Contenedor no encontrado" -ForegroundColor Red
        }
    }
    catch {
        Write-Host "❌ Error al obtener estado: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Función para verificar configuración
function Test-Configuration {
    Write-Host "🔍 Verificando configuración..." -ForegroundColor Blue
    
    $configFiles = @(
        "$HomepageConfigDir\settings.yaml",
        "$HomepageConfigDir\services.yaml",
        "$HomepageConfigDir\widgets.yaml",
        "$HomepageConfigDir\bookmarks.yaml",
        "$HomepageConfigDir\docker.yaml"
    )
    
    $allFilesExist = $true
    foreach ($file in $configFiles) {
        if (Test-Path $file) {
            Write-Host "✅ $file" -ForegroundColor Green
        }
        else {
            Write-Host "❌ $file" -ForegroundColor Red
            $allFilesExist = $false
        }
    }
    
    if ($allFilesExist) {
        Write-Host "✅ Todos los archivos de configuración están presentes" -ForegroundColor Green
    }
    else {
        Write-Host "⚠️  Algunos archivos de configuración faltan" -ForegroundColor Yellow
    }
    
    return $allFilesExist
}

# Script principal
function Main {
    Write-Host "=== Homepage Management Script ===" -ForegroundColor Cyan
    Write-Host ""
    
    # Verificar prerrequisitos
    if (-not (Test-Docker)) {
        return
    }
    
    if (-not (Test-DockerCompose)) {
        return
    }
    
    # Verificar configuración
    Test-Configuration | Out-Null
    
    # Ejecutar acción solicitada
    if ($Help) {
        Show-Help
    }
    elseif ($Start) {
        Start-Homepage
    }
    elseif ($Stop) {
        Stop-Homepage
    }
    elseif ($Restart) {
        Restart-Homepage
    }
    elseif ($Logs) {
        Show-Logs
    }
    elseif ($Status) {
        Show-Status
    }
    else {
        Write-Host "⚠️  No se especificó ninguna acción. Usa -Help para ver las opciones disponibles." -ForegroundColor Yellow
        Write-Host ""
        Show-Status
    }
}

# Ejecutar script principal
Main
