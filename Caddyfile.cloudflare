# Caddyfile.cloudflare - Configuración optimizada para Cloudflare
# Versión adaptada para proxy de Cloudflare con tu configuración actual

# === CONFIGURACIÓN GLOBAL ===
{
	# Configuración global optimizada para Cloudflare
	servers {
		timeouts {
			read_body 30s
			read_header 10s
			write 30s
			idle 120s
		}
		# Configuración para IPs reales de Cloudflare
		trusted_proxies cloudflare
	}
	
	# Email para certificados (mantener el tuyo)
	email <EMAIL>
}

# === SNIPPET COMÚN PARA CLOUDFLARE ===
(cloudflare_common) {
	# Headers para obtener IP real del visitante a través de Cloudflare
	header_up X-Real-IP {http.request.header.CF-Connecting-IP}
	header_up X-Forwarded-For {http.request.header.CF-Connecting-IP}
	header_up X-Forwarded-Proto https
	
	# Headers de seguridad optimizados para Cloudflare
	header {
		# Cloudflare ya maneja muchos headers de seguridad, pero mantenemos los esenciales
		X-Content-Type-Options "nosniff"
		X-Frame-Options "SAMEORIGIN"
		Referrer-Policy "strict-origin-when-cross-origin"
		Strict-Transport-Security "max-age=31536000; includeSubDomains; preload"
		
		# Remover headers que pueden revelar información
		-Server
		-X-Powered-By
	}
}

# === SNIPPET REUTILIZABLE PARA APLICACIONES *ARR (ADAPTADO) ===
(arr_common_cf) {
	# Compresión para interfaces web
	encode {
		gzip 6
		zstd
	}

	# Importar configuración común de Cloudflare
	import cloudflare_common

	# Headers de seguridad específicos para aplicaciones *arr
	header {
		# CSP balanceado para funcionalidad completa
		Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: *"
		Permissions-Policy "geolocation=(), microphone=(), camera=()"
		X-Permitted-Cross-Domain-Policies "none"
	}

	# Manejo de errores consistente
	handle_errors {
		@5xx expression {http.error.status_code} >= 500
		respond @5xx "{args[0]} temporalmente no disponible" 503

		@4xx expression {http.error.status_code} >= 400
		respond @4xx "Recurso no encontrado en {args[0]}" 404
	}

	# TLS automático (Cloudflare maneja el certificado externo)
	tls {
		protocols tls1.2 tls1.3
	}
}

# === JELLYFIN MEDIA SERVER ===
# CAMBIAR: tankeflix.duckdns.org → flix.tudominio.com
flix.tudominio.com {
	# Compresión para mejor rendimiento
	encode {
		gzip 6
		zstd
	}

	# Importar configuración común de Cloudflare
	import cloudflare_common

	# Proxy inverso hacia Jellyfin con configuración optimizada
	reverse_proxy host.docker.internal:8096 {
		# Headers esenciales para Jellyfin
		header_up Host {upstream_hostport}

		# Timeouts específicos para streaming
		transport http {
			read_timeout 60s
			write_timeout 60s
			dial_timeout 10s
		}
	}

	# Headers de seguridad optimizados para Jellyfin
	header {
		# CSP relajado para Jellyfin (necesita inline scripts y estilos)
		Content-Security-Policy "default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: *; media-src 'self' blob: data: *"
		Permissions-Policy "geolocation=(), microphone=(), camera=()"
		X-Permitted-Cross-Domain-Policies "none"
	}

	# Logging mejorado con filtros
	log {
		output file /var/log/caddy/Jellyfin/jellyfin.log {
			roll_size 50MiB
			roll_keep 10
			roll_keep_for 720h # 30 días
		}
		format json
		level INFO
	}

	# Manejo de errores personalizado
	handle_errors {
		@5xx expression {http.error.status_code} >= 500
		respond @5xx "Servicio temporalmente no disponible" 503

		@4xx expression {http.error.status_code} >= 400
		respond @4xx "Recurso no encontrado" 404
	}
}

# === QBITTORRENT TORRENT CLIENT ===
# CAMBIAR: tanketorrent.duckdns.org → torrent.tudominio.com
torrent.tudominio.com {
	encode zstd gzip
	
	# Importar configuración común de Cloudflare
	import cloudflare_common

	reverse_proxy host.docker.internal:8091 {
		header_up Host {host}
		transport http {
			read_timeout 30s
			write_timeout 30s
			dial_timeout 5s
		}
	}

	header {
		# CSP específico para qBittorrent
		Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'"
		Permissions-Policy "geolocation=(), microphone=(), camera=(), payment=()"
		X-Permitted-Cross-Domain-Policies "none"
	}

	log {
		output file /var/log/caddy/Qbittorrent/qbittorrent.log {
			roll_size 25MiB
			roll_keep 8
			roll_keep_for 480h # 20 días
		}
		format json
		level INFO
	}

	handle_errors {
		@5xx expression {http.error.status_code} >= 500
		respond @5xx "qBittorrent temporalmente no disponible" 503

		@4xx expression {http.error.status_code} >= 400
		respond @4xx "Acceso no autorizado" 401
	}
}

# === APLICACIONES *ARR CON DOMINIOS ADAPTADOS ===

# Sonarr - Gestión de Series
# CAMBIAR: tankesonarr.duckdns.org → sonarr.tudominio.com
sonarr.tudominio.com {
	# Importar configuración común adaptada
	import arr_common_cf "Sonarr"

	# Proxy específico para Sonarr
	reverse_proxy host.docker.internal:8989 {
		header_up Host {upstream_hostport}

		# Timeouts optimizados para operaciones de series
		transport http {
			read_timeout 45s
			write_timeout 45s
			dial_timeout 5s
		}
	}

	# Logging específico para Sonarr
	log {
		output file /var/log/caddy/Sonarr/sonarr.log {
			roll_size 30MiB
			roll_keep 8
			roll_keep_for 480h # 20 días
		}
		format json
		level INFO
	}
}

# Jackett - Indexador de Torrents
# CAMBIAR: tankejackett.duckdns.org → jackett.tudominio.com
jackett.tudominio.com {
	# Importar configuración común adaptada
	import arr_common_cf "Jackett"

	# Proxy específico para Jackett
	reverse_proxy host.docker.internal:9117 {
		header_up Host {upstream_hostport}

		# Timeouts para búsquedas de indexadores
		transport http {
			read_timeout 60s # Búsquedas pueden tomar tiempo
			write_timeout 30s
			dial_timeout 5s
		}
	}

	# Logging específico para Jackett
	log {
		output file /var/log/caddy/Jackett/jackett.log {
			roll_size 25MiB
			roll_keep 6
			roll_keep_for 360h # 15 días
		}
		format json
		level INFO
	}
}

# Prowlarr - Gestión de Indexadores
# CAMBIAR: tankeprowlarr.duckdns.org → prowlarr.tudominio.com
prowlarr.tudominio.com {
	# Importar configuración común adaptada
	import arr_common_cf "Prowlarr"

	# Proxy específico para Prowlarr
	reverse_proxy host.docker.internal:9696 {
		header_up Host {upstream_hostport}

		# Timeouts para operaciones de indexadores
		transport http {
			read_timeout 45s
			write_timeout 30s
			dial_timeout 5s
		}
	}

	# Logging específico para Prowlarr
	log {
		output file /var/log/caddy/Prowlarr/prowlarr.log {
			roll_size 25MiB
			roll_keep 6
			roll_keep_for 360h # 15 días
		}
		format json
		level INFO
	}
}

# Radarr - Gestión de Películas
# CAMBIAR: tankeradarr.duckdns.org → radarr.tudominio.com
radarr.tudominio.com {
	# Importar configuración común adaptada
	import arr_common_cf "Radarr"

	# Proxy específico para Radarr
	reverse_proxy host.docker.internal:7878 {
		header_up Host {upstream_hostport}

		# Timeouts optimizados para operaciones de películas
		transport http {
			read_timeout 45s
			write_timeout 45s
			dial_timeout 5s
		}
	}

	# Logging específico para Radarr
	log {
		output file /var/log/caddy/Radarr/radarr.log {
			roll_size 30MiB
			roll_keep 8
			roll_keep_for 480h # 20 días
		}
		format json
		level INFO
	}
}

# Jellyseerr - Gestión de Solicitudes para Jellyfin
# CAMBIAR: tankejellyseerr.duckdns.org → jellyseerr.tudominio.com
jellyseerr.tudominio.com {
	# Importar configuración común adaptada
	import arr_common_cf "Jellyseerr"

	# Proxy específico para Jellyseerr
	reverse_proxy host.docker.internal:5055 {
		header_up Host {upstream_hostport}

		# Timeouts optimizados para solicitudes y notificaciones
		transport http {
			read_timeout 45s
			write_timeout 45s
			dial_timeout 5s
		}
	}

	# Logging específico para Jellyseerr
	log {
		output file /var/log/caddy/Jellyseerr/jellyseerr.log {
			roll_size 30MiB
			roll_keep 8
			roll_keep_for 480h # 20 días
		}
		format json
		level INFO
	}
}

# === ADGUARD HOME DNS SERVER & AD BLOCKER ===
# CAMBIAR: tankeguard.duckdns.org → guard.tudominio.com
guard.tudominio.com {
	# Compresión optimizada para AdGuard Home
	encode {
		gzip 6
		zstd
	}

	# Importar configuración común de Cloudflare
	import cloudflare_common

	# Proxy específico para AdGuard Home
	reverse_proxy host.docker.internal:8080 {
		# Headers críticos para AdGuard Home
		header_up Host {host}

		# Timeouts específicos para AdGuard Home
		transport http {
			read_timeout 60s
			write_timeout 60s
			dial_timeout 10s
		}
	}

	# Headers de seguridad específicos para AdGuard Home
	header {
		Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data:"
		Permissions-Policy "geolocation=(), microphone=(), camera=()"
		X-Permitted-Cross-Domain-Policies "none"
	}

	# Logging específico para AdGuard Home
	log {
		output file /var/log/caddy/AdGuard/adguard.log {
			roll_size 30MiB
			roll_keep 8
			roll_keep_for 480h # 20 días
		}
		format json
		level INFO
	}

	# Manejo de errores específico para AdGuard Home
	handle_errors {
		@5xx expression {http.error.status_code} >= 500
		respond @5xx "AdGuard Home temporalmente no disponible" 503

		@4xx expression {http.error.status_code} >= 400
		respond @4xx "Acceso no autorizado a AdGuard Home" 401
	}

	# Ruta especial para DNS-over-HTTPS
	handle /dns-query {
		reverse_proxy host.docker.internal:8080 {
			header_up Host {host}
		}
	}
}

# === HOMEPAGE DASHBOARD ===
# CAMBIAR: tankeeee2.duckdns.org → home.tudominio.com
home.tudominio.com {
	# Compresión optimizada para Homepage
	encode {
		gzip 6
		zstd
	}

	# Importar configuración común de Cloudflare
	import cloudflare_common

	# Proxy específico para Homepage
	reverse_proxy host.docker.internal:3001 {
		header_up Host {host}
		header_up X-Forwarded-For {remote_host}

		# Timeouts específicos para Homepage
		transport http {
			read_timeout 60s
			write_timeout 60s
			dial_timeout 10s
		}
	}

	# Headers de seguridad específicos para Homepage
	header {
		Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: *; connect-src 'self' *"
		Permissions-Policy "geolocation=(), microphone=(), camera=()"
		X-Permitted-Cross-Domain-Policies "none"
	}

	# Logging específico para Homepage
	log {
		output file /var/log/caddy/Homepage/homepage.log {
			roll_size 30MiB
			roll_keep 8
			roll_keep_for 480h # 20 días
		}
		format json
		level INFO
	}

	# Manejo de errores específico para Homepage
	handle_errors {
		@5xx expression {http.error.status_code} >= 500
		respond @5xx "Homepage temporalmente no disponible" 503

		@4xx expression {http.error.status_code} >= 400
		respond @4xx "Recurso no encontrado en Homepage" 404
	}
}

# === CONFIGURACIÓN PARA HOME ASSISTANT (PREPARADO) ===
# Descomenta cuando tengas Home Assistant configurado
# ha.tudominio.com {
# 	import cloudflare_common
# 	encode { gzip 6; zstd }
# 	
# 	reverse_proxy host.docker.internal:8123 {
# 		header_up Host {upstream_hostport}
# 		header_up X-Forwarded-Proto https
# 		header_up X-Forwarded-For {remote_host}
# 		
# 		transport http {
# 			read_timeout 60s
# 			write_timeout 60s
# 			dial_timeout 10s
# 		}
# 	}
# 	
# 	# Headers específicos para Home Assistant
# 	header {
# 		Content-Security-Policy "default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: wss: https:; img-src 'self' data: https: blob:; media-src 'self' https: blob:"
# 	}
# 	
# 	log {
# 		output file /var/log/caddy/HomeAssistant/homeassistant.log {
# 			roll_size 30MiB
# 			roll_keep 8
# 			roll_keep_for 480h
# 		}
# 		format json
# 		level INFO
# 	}
# }
