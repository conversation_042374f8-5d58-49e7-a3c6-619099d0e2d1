---
client: <PERSON><PERSON><PERSON> for Android TV
client_version: 0.18.11
client_repository: https://github.com/jellyfin/jellyfin-androidtv
type: crash_report
format: markdown
---
### Logs

***Stack Trace***: 
```log
org.jellyfin.sdk.api.client.exception.TimeoutException: Connection failed
	at org.jellyfin.sdk.api.ktor.KtorClient.request(KtorClient.kt:163)
	at org.jellyfin.sdk.api.ktor.KtorClient$request$1.invokeSuspend(Unknown Source:19)
	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:98)
	at kotlinx.coroutines.internal.LimitedDispatcher$Worker.run(LimitedDispatcher.kt:113)
	at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:89)
	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:586)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:820)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:717)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:704)
	Suppressed: kotlinx.coroutines.internal.DiagnosticCoroutineContextException: [StandaloneCoroutine{Cancelling}@ef19a5e, Dispatchers.IO]
Caused by: java.net.ConnectException: Failed to connect to tankeflix.duckdns.org/*************:443
	at okhttp3.internal.connection.RealConnection.connectSocket(RealConnection.kt:297)
	at okhttp3.internal.connection.RealConnection.connect(RealConnection.kt:207)
	at okhttp3.internal.connection.ExchangeFinder.findConnection(ExchangeFinder.kt:226)
	at okhttp3.internal.connection.ExchangeFinder.findHealthyConnection(ExchangeFinder.kt:106)
	at okhttp3.internal.connection.ExchangeFinder.find(ExchangeFinder.kt:74)
	at okhttp3.internal.connection.RealCall.initExchange$okhttp(RealCall.kt:255)
	at okhttp3.internal.connection.ConnectInterceptor.intercept(ConnectInterceptor.kt:32)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
	at okhttp3.internal.cache.CacheInterceptor.intercept(CacheInterceptor.kt:95)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
	at okhttp3.internal.http.BridgeInterceptor.intercept(BridgeInterceptor.kt:83)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
	at okhttp3.internal.http.RetryAndFollowUpInterceptor.intercept(RetryAndFollowUpInterceptor.kt:76)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
	at okhttp3.internal.connection.RealCall.getResponseWithInterceptorChain$okhttp(RealCall.kt:201)
	at okhttp3.internal.connection.RealCall$AsyncCall.run(RealCall.kt:517)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1167)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
	at java.lang.Thread.run(Thread.java:923)
Caused by: java.net.ConnectException: failed to connect to tankeflix.duckdns.org/************* (port 443) from /************* (port 45498) after 6000ms: isConnected failed: ECONNREFUSED (Connection refused)
	at libcore.io.IoBridge.isConnected(IoBridge.java:287)
	at libcore.io.IoBridge.connectErrno(IoBridge.java:192)
	at libcore.io.IoBridge.connect(IoBridge.java:134)
	at java.net.PlainSocketImpl.socketConnect(PlainSocketImpl.java:142)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:390)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:230)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:212)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:436)
	at java.net.Socket.connect(Socket.java:621)
	at okhttp3.internal.platform.Platform.connectSocket(Platform.kt:128)
	at okhttp3.internal.connection.RealConnection.connectSocket(RealConnection.kt:295)
	... 18 more
Caused by: android.system.ErrnoException: isConnected failed: ECONNREFUSED (Connection refused)
	at libcore.io.IoBridge.isConnected(IoBridge.java:274)
	... 28 more

```  
***Logcat***: 
```log
07-29 20:08:45.353 W/LeanbackChannelWorker(11757): 		at java.net.Socket.connect(Socket.java:621)
07-29 20:08:45.353 W/LeanbackChannelWorker(11757): 		at okhttp3.internal.platform.Platform.connectSocket(Platform.kt:128)
07-29 20:08:45.353 W/LeanbackChannelWorker(11757): 		at okhttp3.internal.connection.RealConnection.connectSocket(RealConnection.kt:295)
07-29 20:08:45.353 W/LeanbackChannelWorker(11757): 		... 18 more
07-29 20:08:45.353 W/LeanbackChannelWorker(11757): 	Caused by: android.system.ErrnoException: isConnected failed: ECONNREFUSED (Connection refused)
07-29 20:08:45.353 W/LeanbackChannelWorker(11757): 		at libcore.io.IoBridge.isConnected(IoBridge.java:274)
07-29 20:08:45.353 W/LeanbackChannelWorker(11757): 		... 28 more
07-29 20:08:45.353 W/LeanbackChannelWorker(11757): Caused by: java.net.ConnectException: Failed to connect to tankeflix.duckdns.org/*************:443
07-29 20:08:45.353 W/LeanbackChannelWorker(11757): 	at okhttp3.internal.connection.RealConnection.connectSocket(RealConnection.kt:297)
07-29 20:08:45.353 W/LeanbackChannelWorker(11757): 	at okhttp3.internal.connection.RealConnection.connect(RealConnection.kt:207)
07-29 20:08:45.353 W/LeanbackChannelWorker(11757): 	at okhttp3.internal.connection.ExchangeFinder.findConnection(ExchangeFinder.kt:226)
07-29 20:08:45.353 W/LeanbackChannelWorker(11757): 	at okhttp3.internal.connection.ExchangeFinder.findHealthyConnection(ExchangeFinder.kt:106)
07-29 20:08:45.353 W/LeanbackChannelWorker(11757): 	at okhttp3.internal.connection.ExchangeFinder.find(ExchangeFinder.kt:74)
07-29 20:08:45.353 W/LeanbackChannelWorker(11757): 	at okhttp3.internal.connection.RealCall.initExchange$okhttp(RealCall.kt:255)
07-29 20:08:45.353 W/LeanbackChannelWorker(11757): 	at okhttp3.internal.connection.ConnectInterceptor.intercept(ConnectInterceptor.kt:32)
07-29 20:08:45.353 W/LeanbackChannelWorker(11757): 	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
07-29 20:08:45.353 W/LeanbackChannelWorker(11757): 	at okhttp3.internal.cache.CacheInterceptor.intercept(CacheInterceptor.kt:95)
07-29 20:08:45.353 W/LeanbackChannelWorker(11757): 	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
07-29 20:08:45.353 W/LeanbackChannelWorker(11757): 	at okhttp3.internal.http.BridgeInterceptor.intercept(BridgeInterceptor.kt:83)
07-29 20:08:45.353 W/LeanbackChannelWorker(11757): 	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
07-29 20:08:45.353 W/LeanbackChannelWorker(11757): 	at okhttp3.internal.http.RetryAndFollowUpInterceptor.intercept(RetryAndFollowUpInterceptor.kt:76)
07-29 20:08:45.353 W/LeanbackChannelWorker(11757): 	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
07-29 20:08:45.353 W/LeanbackChannelWorker(11757): 	at okhttp3.internal.connection.RealCall.getResponseWithInterceptorChain$okhttp(RealCall.kt:201)
07-29 20:08:45.353 W/LeanbackChannelWorker(11757): 	at okhttp3.internal.connection.RealCall$AsyncCall.run(RealCall.kt:517)
07-29 20:08:45.353 W/LeanbackChannelWorker(11757): 	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1167)
07-29 20:08:45.353 W/LeanbackChannelWorker(11757): 	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
07-29 20:08:45.353 W/LeanbackChannelWorker(11757): 	at java.lang.Thread.run(Thread.java:923)
07-29 20:08:45.353 W/LeanbackChannelWorker(11757): Caused by: java.net.ConnectException: failed to connect to tankeflix.duckdns.org/************* (port 443) from /************* (port 45492) after 6000ms: isConnected failed: ECONNREFUSED (Connection refused)
07-29 20:08:45.353 W/LeanbackChannelWorker(11757): 	at libcore.io.IoBridge.isConnected(IoBridge.java:287)
07-29 20:08:45.353 W/LeanbackChannelWorker(11757): 	at libcore.io.IoBridge.connectErrno(IoBridge.java:192)
07-29 20:08:45.353 W/LeanbackChannelWorker(11757): 	at libcore.io.IoBridge.connect(IoBridge.java:134)
07-29 20:08:45.353 W/LeanbackChannelWorker(11757): 	at java.net.PlainSocketImpl.socketConnect(PlainSocketImpl.java:142)
07-29 20:08:45.353 W/LeanbackChannelWorker(11757): 	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:390)
07-29 20:08:45.353 W/LeanbackChannelWorker(11757): 	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:230)
07-29 20:08:45.353 W/LeanbackChannelWorker(11757): 	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:212)
07-29 20:08:45.353 W/LeanbackChannelWorker(11757): 	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:436)
07-29 20:08:45.353 W/LeanbackChannelWorker(11757): 	at java.net.Socket.connect(Socket.java:621)
07-29 20:08:45.353 W/LeanbackChannelWorker(11757): 	at okhttp3.internal.platform.Platform.connectSocket(Platform.kt:128)
07-29 20:08:45.353 W/LeanbackChannelWorker(11757): 	at okhttp3.internal.connection.RealConnection.connectSocket(RealConnection.kt:295)
07-29 20:08:45.353 W/LeanbackChannelWorker(11757): 	... 18 more
07-29 20:08:45.353 W/LeanbackChannelWorker(11757): Caused by: android.system.ErrnoException: isConnected failed: ECONNREFUSED (Connection refused)
07-29 20:08:45.354 W/LeanbackChannelWorker(11757): 	at libcore.io.IoBridge.isConnected(IoBridge.java:274)
07-29 20:08:45.354 W/LeanbackChannelWorker(11757): 	... 28 more
07-29 20:08:45.387 I/WM-WorkerWrapper(11757): Worker result RETRY for Work [ id=5347c756-a47c-4988-a418-4e9981ddea02, tags={ org.jellyfin.androidtv.integration.LeanbackChannelWorker } ]
07-29 20:08:45.407 I/lyfin.androidt(11757): Background young concurrent copying GC freed 79787(2342KB) AllocSpace objects, 0(0B) LOS objects, 42% free, 9064KB/15MB, paused 196us total 274.702ms
07-29 20:08:45.407 W/lyfin.androidt(11757): Reducing the number of considered missed Gc histogram windows from 104 to 100
07-29 20:08:45.457 I/org.jellyfin.sdk.api.ktor.KtorClient(11757): GET https://tankeflix.duckdns.org/Branding/Configuration
07-29 20:08:45.493 I/org.jellyfin.sdk.api.ktor.KtorClient(11757): GET https://tankeflix.duckdns.org/LiveTv/Programs/Recommended?limit=1&isAiring=true&imageTypeLimit=1&enableTotalRecordCount=false
07-29 20:08:45.507 I/org.jellyfin.sdk.api.ktor.KtorClient(11757): GET https://tankeflix.duckdns.org/Branding/Configuration
07-29 20:08:45.524 I/HomeRowsFragment(11757): Updating audio queue in HomeFragment (onResume)
07-29 20:08:45.563 E/ACRA    (11757): ACRA caught a TimeoutException for org.jellyfin.androidtv
07-29 20:08:45.563 E/ACRA    (11757): org.jellyfin.sdk.api.client.exception.TimeoutException: Connection failed
07-29 20:08:45.563 E/ACRA    (11757): 	at org.jellyfin.sdk.api.ktor.KtorClient.request(KtorClient.kt:163)
07-29 20:08:45.563 E/ACRA    (11757): 	at org.jellyfin.sdk.api.ktor.KtorClient$request$1.invokeSuspend(Unknown Source:19)
07-29 20:08:45.563 E/ACRA    (11757): 	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
07-29 20:08:45.563 E/ACRA    (11757): 	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:98)
07-29 20:08:45.563 E/ACRA    (11757): 	at kotlinx.coroutines.internal.LimitedDispatcher$Worker.run(LimitedDispatcher.kt:113)
07-29 20:08:45.563 E/ACRA    (11757): 	at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:89)
07-29 20:08:45.563 E/ACRA    (11757): 	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:586)
07-29 20:08:45.563 E/ACRA    (11757): 	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:820)
07-29 20:08:45.563 E/ACRA    (11757): 	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:717)
07-29 20:08:45.563 E/ACRA    (11757): 	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:704)
07-29 20:08:45.563 E/ACRA    (11757): 	Suppressed: kotlinx.coroutines.internal.DiagnosticCoroutineContextException: [StandaloneCoroutine{Cancelling}@ef19a5e, Dispatchers.IO]
07-29 20:08:45.563 E/ACRA    (11757): Caused by: java.net.ConnectException: Failed to connect to tankeflix.duckdns.org/*************:443
07-29 20:08:45.563 E/ACRA    (11757): 	at okhttp3.internal.connection.RealConnection.connectSocket(RealConnection.kt:297)
07-29 20:08:45.563 E/ACRA    (11757): 	at okhttp3.internal.connection.RealConnection.connect(RealConnection.kt:207)
07-29 20:08:45.563 E/ACRA    (11757): 	at okhttp3.internal.connection.ExchangeFinder.findConnection(ExchangeFinder.kt:226)
07-29 20:08:45.563 E/ACRA    (11757): 	at okhttp3.internal.connection.ExchangeFinder.findHealthyConnection(ExchangeFinder.kt:106)
07-29 20:08:45.563 E/ACRA    (11757): 	at okhttp3.internal.connection.ExchangeFinder.find(ExchangeFinder.kt:74)
07-29 20:08:45.563 E/ACRA    (11757): 	at okhttp3.internal.connection.RealCall.initExchange$okhttp(RealCall.kt:255)
07-29 20:08:45.563 E/ACRA    (11757): 	at okhttp3.internal.connection.ConnectInterceptor.intercept(ConnectInterceptor.kt:32)
07-29 20:08:45.563 E/ACRA    (11757): 	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
07-29 20:08:45.563 E/ACRA    (11757): 	at okhttp3.internal.cache.CacheInterceptor.intercept(CacheInterceptor.kt:95)
07-29 20:08:45.563 E/ACRA    (11757): 	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
07-29 20:08:45.563 E/ACRA    (11757): 	at okhttp3.internal.http.BridgeInterceptor.intercept(BridgeInterceptor.kt:83)
07-29 20:08:45.563 E/ACRA    (11757): 	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
07-29 20:08:45.563 E/ACRA    (11757): 	at okhttp3.internal.http.RetryAndFollowUpInterceptor.intercept(RetryAndFollowUpInterceptor.kt:76)
07-29 20:08:45.563 E/ACRA    (11757): 	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
07-29 20:08:45.563 E/ACRA    (11757): 	at okhttp3.internal.connection.RealCall.getResponseWithInterceptorChain$okhttp(RealCall.kt:201)
07-29 20:08:45.563 E/ACRA    (11757): 	at okhttp3.internal.connection.RealCall$AsyncCall.run(RealCall.kt:517)
07-29 20:08:45.563 E/ACRA    (11757): 	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1167)
07-29 20:08:45.563 E/ACRA    (11757): 	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
07-29 20:08:45.563 E/ACRA    (11757): 	at java.lang.Thread.run(Thread.java:923)
07-29 20:08:45.563 E/ACRA    (11757): Caused by: java.net.ConnectException: failed to connect to tankeflix.duckdns.org/************* (port 443) from /************* (port 45498) after 6000ms: isConnected failed: ECONNREFUSED (Connection refused)
07-29 20:08:45.563 E/ACRA    (11757): 	at libcore.io.IoBridge.isConnected(IoBridge.java:287)
07-29 20:08:45.563 E/ACRA    (11757): 	at libcore.io.IoBridge.connectErrno(IoBridge.java:192)
07-29 20:08:45.563 E/ACRA    (11757): 	at libcore.io.IoBridge.connect(IoBridge.java:134)
07-29 20:08:45.563 E/ACRA    (11757): 	at java.net.PlainSocketImpl.socketConnect(PlainSocketImpl.java:142)
07-29 20:08:45.563 E/ACRA    (11757): 	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:390)
07-29 20:08:45.563 E/ACRA    (11757): 	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:230)
07-29 20:08:45.563 E/ACRA    (11757): 	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:212)
07-29 20:08:45.563 E/ACRA    (11757): 	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:436)
07-29 20:08:45.563 E/ACRA    (11757): 	at java.net.Socket.connect(Socket.java:621)
07-29 20:08:45.563 E/ACRA    (11757): 	at okhttp3.internal.platform.Platform.connectSocket(Platform.kt:128)
07-29 20:08:45.563 E/ACRA    (11757): 	at okhttp3.internal.connection.RealConnection.connectSocket(RealConnection.kt:295)
07-29 20:08:45.563 E/ACRA    (11757): 	... 18 more
07-29 20:08:45.563 E/ACRA    (11757): Caused by: android.system.ErrnoException: isConnected failed: ECONNREFUSED (Connection refused)
07-29 20:08:45.563 E/ACRA    (11757): 	at libcore.io.IoBridge.isConnected(IoBridge.java:274)
07-29 20:08:45.563 E/ACRA    (11757): 	... 28 more

```  

### App information

***App version***: `0.18.11` (`181199`)  
***Package name***: `org.jellyfin.androidtv`  
***Build***: 
```json
{"BOARD":"karat","BOOTLOADER":"unknown","BRAND":"Amazon","CPU_ABI":"armeabi-v7a","CPU_ABI2":"armeabi","DEVICE":"karat","DISPLAY":"RS8140.3002N","FINGERPRINT":"Amazon\/karat\/karat:11\/RS8140.3002N\/0027783838340:user\/amz-p,release-keys","HARDWARE":"mt8696","HOST":"c5-ri-1804-use1a-b-18-04-fos8-54","ID":"RS8140.3002N","IS_DEBUGGABLE":false,"IS_EMULATOR":false,"MANUFACTURER":"Amazon","MODEL":"AFTKRT","PERMISSIONS_REVIEW_REQUIRED":true,"PRODUCT":"karat","RADIO":"unknown","SUPPORTED_32_BIT_ABIS":["armeabi-v7a","armeabi"],"SUPPORTED_64_BIT_ABIS":[],"SUPPORTED_ABIS":["armeabi-v7a","armeabi"],"TAGS":"amz-p,release-keys","TIME":1750124737000,"TYPE":"user","UNKNOWN":"unknown","USER":"build","VERSION":{"ACTIVE_CODENAMES":[],"BASE_OS":"RP1A.200720.009","CODENAME":"REL","INCREMENTAL":"0027783838340","PREVIEW_SDK_FINGERPRINT":"REL","PREVIEW_SDK_INT":0,"RELEASE":"11","RELEASE_OR_CODENAME":"11","SDK":"30","SDK_INT":30,"SECURITY_PATCH":"2025-02-01"}}
```  
***Build config***: 
```json
{"APPLICATION_ID":"org.jellyfin.androidtv","BUILD_TYPE":"release","DEBUG":false,"DEVELOPMENT":false,"VERSION_CODE":181199,"VERSION_NAME":"0.18.11"}
```  

### Device information

***Android version***: `11`  
***Device brand***: `Amazon`  
***Device product***: `karat`  
***Device model***: `AFTKRT`  

### Crash information

***Start time***: `2025-07-26T10:53:39.857+02:00`  
***Crash time***: `2025-07-29T20:08:45.773+02:00`  

