# Script para configurar el archivo .env del Arr-Bot
# Ejecutar desde PowerShell en el directorio telegram-bot

Write-Host "=== Configuración del Arr-Bot ===" -ForegroundColor Green
Write-Host ""

# Solicitar credenciales de Telegram
Write-Host "1. CONFIGURACIÓN DE TELEGRAM" -ForegroundColor Yellow
$telegram_token = Read-Host "Ingresa el token de tu bot de Telegram (de @BotFather)"
$telegram_chat_id = Read-Host "Ingresa tu Chat ID (número)"

# Solicitar API Keys
Write-Host ""
Write-Host "2. CONFIGURACIÓN DE RADARR" -ForegroundColor Yellow
$radarr_api_key = Read-Host "Ingresa la API Key de Radarr (desde Settings > General)"

Write-Host ""
Write-Host "3. CONFIGURACIÓN DE SONARR" -ForegroundColor Yellow
$sonarr_api_key = Read-Host "Ingresa la API Key de Sonarr (desde Configuración > General)"

# Crear el contenido del archivo .env
$env_content = @"
# Arr-Bot Configuration
# Bot de Telegram para gestionar Radarr y Sonarr
# Integrado con stack DNS DuckDNS

# ==================== TELEGRAM CONFIGURATION ====================
# Token del bot de Telegram (obtenido de @BotFather)
TELEGRAM_BOT_TOKEN=$telegram_token

# ID del chat donde enviar notificaciones (puede ser un chat privado o grupo)
TELEGRAM_CHAT_ID=$telegram_chat_id

# ==================== RADARR CONFIGURATION ====================
# URL de Radarr (desde el contexto del stack DNS)
RADARR_URL=http://host.docker.internal:7878

# Clave API de Radarr (obtenida desde Configuración > General > API Key)
RADARR_API_KEY=$radarr_api_key

# ==================== SONARR CONFIGURATION ====================
# URL de Sonarr (desde el contexto del stack DNS)
SONARR_URL=http://host.docker.internal:8989

# Clave API de Sonarr (obtenida desde Configuración > General > API Key)
SONARR_API_KEY=$sonarr_api_key

# ==================== WEBHOOK CONFIGURATION ====================
# Puerto para el servidor webhook (debe coincidir con el puerto expuesto en Docker)
WEBHOOK_PORT=8082

# ==================== CONFIGURACIÓN COMPLETADA ====================
# URLs de webhook configuradas:
# - Radarr: http://*************:8082/webhook/radarr
# - Sonarr: http://*************:8082/webhook/sonarr
"@

# Escribir el archivo .env
$env_content | Out-File -FilePath ".env" -Encoding UTF8

Write-Host ""
Write-Host "✅ Archivo .env configurado correctamente!" -ForegroundColor Green
Write-Host ""
Write-Host "Próximos pasos:" -ForegroundColor Cyan
Write-Host "1. Ejecutar: docker-compose -f ../DNS-compose.yml restart telegram-bot"
Write-Host "2. Verificar logs: docker logs telegram-bot"
Write-Host "3. Probar el bot enviando /start a tu bot de Telegram"
Write-Host ""
