{"clientId": "a5985995-5628-4909-8d76-de164530ae96", "vapidPrivate": "uulcUwjcM37jcOD7ue1Q1pJLzIpAC3E-q_mVbamXI08", "vapidPublic": "BChN32rzyc7EoDJ_YqSVvCElCIdWFX_AIOMfi3uDcj-1DwMrgF2EAiWB4aOX-0unsNmDLytavrL2rIIjKGO9b1o", "main": {"apiKey": "MTc1Mjc2MDk0NTQ5MzIzMjNhMzVmLWRlOTktNDkxMi04Y2IyLTYzNDU4OTFiN2U5Yg==", "applicationTitle": "<PERSON><PERSON><PERSON><PERSON>", "applicationUrl": "", "cacheImages": false, "defaultPermissions": 32, "defaultQuotas": {"movie": {}, "tv": {}}, "hideAvailable": false, "hideBlacklisted": false, "localLogin": true, "mediaServerLogin": true, "newPlexLogin": true, "discoverRegion": "", "streamingRegion": "ES", "originalLanguage": "", "blacklistedTags": "", "blacklistedTagsLimit": 50, "mediaServerType": 2, "partialRequestsEnabled": true, "enableSpecialEpisodes": false, "locale": "es", "youtubeUrl": ""}, "plex": {"name": "", "ip": "", "port": 32400, "useSsl": false, "libraries": []}, "jellyfin": {"name": "Tankeflix", "ip": "jellyfin", "port": 8096, "useSsl": false, "urlBase": "", "externalHostname": "https://tankeflix.duckdns.org", "jellyfinForgotPasswordUrl": "", "libraries": [{"id": "ed2a25286c558a96e1424971742ca250", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enabled": true, "type": "movie"}, {"id": "5ddaa59a73205234890fdcfc683e14ed", "name": "Series", "enabled": true, "type": "show"}], "serverId": "37e9d167aa7248f5a395aa540baf08a6", "apiKey": "2e3cd7e96e6146518c2f8b6fe5afd233"}, "tautulli": {}, "radarr": [{"name": "<PERSON><PERSON>", "hostname": "radarr", "port": 7878, "apiKey": "cfc0cde90b0f483eb4190dc634ca86f2", "useSsl": false, "baseUrl": "", "activeProfileId": 7, "activeProfileName": "UHD - HD", "activeDirectory": "/CONTENIDO/PELIS", "is4k": false, "minimumAvailability": "released", "tags": [], "isDefault": true, "syncEnabled": true, "preventSearch": false, "tagRequests": true, "id": 0}], "sonarr": [{"name": "Sonarr", "hostname": "sonarr", "port": 8989, "apiKey": "2f9da07e98744f4890c0960d15ead111", "useSsl": false, "activeProfileId": 7, "activeProfileName": "UHD - HD", "activeDirectory": "/CONTENIDO/SERIES", "seriesType": "standard", "activeAnimeDirectory": "/CONTENIDO/SERIES", "tags": [], "animeTags": [], "is4k": false, "isDefault": true, "enableSeasonFolders": true, "syncEnabled": true, "preventSearch": false, "tagRequests": true, "id": 0}], "public": {"initialized": true}, "notifications": {"agents": {"email": {"enabled": false, "options": {"userEmailRequired": false, "emailFrom": "", "smtpHost": "", "smtpPort": 587, "secure": false, "ignoreTls": false, "requireTls": false, "allowSelfSigned": false, "senderName": "<PERSON><PERSON><PERSON><PERSON>"}}, "discord": {"enabled": false, "types": 0, "options": {"webhookUrl": "", "webhookRoleId": "", "enableMentions": true}}, "slack": {"enabled": false, "types": 0, "options": {"webhookUrl": ""}}, "telegram": {"enabled": false, "types": 0, "options": {"botAPI": "", "chatId": "", "messageThreadId": "", "sendSilently": false}}, "pushbullet": {"enabled": false, "types": 0, "options": {"accessToken": ""}}, "pushover": {"enabled": false, "types": 0, "options": {"accessToken": "", "userToken": "", "sound": ""}}, "webhook": {"enabled": false, "types": 0, "options": {"webhookUrl": "", "jsonPayload": "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"}}, "webpush": {"enabled": false, "options": {}}, "gotify": {"enabled": false, "types": 0, "options": {"url": "", "token": "", "priority": 0}}, "ntfy": {"enabled": false, "types": 0, "options": {"url": "", "topic": ""}}}}, "jobs": {"plex-recently-added-scan": {"schedule": "0 */5 * * * *"}, "plex-full-scan": {"schedule": "0 0 3 * * *"}, "plex-watchlist-sync": {"schedule": "0 */3 * * * *"}, "plex-refresh-token": {"schedule": "0 0 5 * * *"}, "radarr-scan": {"schedule": "0 0 4 * * *"}, "sonarr-scan": {"schedule": "0 30 4 * * *"}, "availability-sync": {"schedule": "0 0 5 * * *"}, "download-sync": {"schedule": "0 * * * * *"}, "download-sync-reset": {"schedule": "0 0 1 * * *"}, "jellyfin-recently-added-scan": {"schedule": "0 */5 * * * *"}, "jellyfin-full-scan": {"schedule": "0 0 3 * * *"}, "image-cache-cleanup": {"schedule": "0 0 5 * * *"}, "process-blacklisted-tags": {"schedule": "0 30 1 */7 * *"}}, "network": {"csrfProtection": false, "forceIpv4First": false, "trustProxy": false, "proxy": {"enabled": false, "hostname": "", "port": 8080, "useSsl": false, "user": "", "password": "", "bypassFilter": "", "bypassLocalAddresses": true}}}