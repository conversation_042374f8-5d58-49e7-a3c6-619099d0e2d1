# Homepage Configuration - Configurac<PERSON> Principal
# Documentación: https://gethomepage.dev/latest/configs/settings/

# === CONFIGURACIÓN GENERAL ===
title: "Tanke Dashboard"
favicon: "https://cdn.jsdelivr.net/gh/walkxcode/dashboard-icons/png/homepage.png"
background: 
  image: "https://images.unsplash.com/photo-1451187580459-43490279c0fa?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80"
  blur: "sm"
  saturate: 50
  brightness: 50
  opacity: 20

search:
  provider: google
  target: _blank  # Opcional: abre los resultados en una nueva pestaña

# === CONFIGURACIÓN DE IDIOMA ===
language: "es"

# === CONFIGURACIÓN DE TEMA ===
theme: "dark"
color: "slate"

# === CONFIGURACIÓN DE DISEÑO ===
layout:
  Media:
    style: "row"
    columns: 3
  Automatización:
    style: "row"
    columns: 2
  Descargas:
    style: "row"
    columns: 4
  Sistema:
    style: "row"
    columns: 3

# === CONFIGURACIÓN DE WIDGETS GLOBALES ===
headerStyle: "clean"
hideVersion: false
hideErrors: false
showStats: true
statsOpen: false
target: "_blank"

# === CONFIGURACIÓN DE PROVEEDORES ===
providers:
  openweathermap: "TU_API_KEY_OPENWEATHER"
  weatherapi: "TU_API_KEY_WEATHERAPI"

# === CONFIGURACIÓN DE CARACTERÍSTICAS ===
features:
  - search
  - docker
  - kubernetes: false

# === CONFIGURACIÓN DE BÚSQUEDA ===
quicklaunch:
  searchDescriptions: true
  hideInternetSearch: false
  hideVisitURL: false

# === CONFIGURACIÓN DE DOCKER ===
docker:
  # Configuración para acceso al socket de Docker
  socket: "/var/run/docker.sock"

# === CONFIGURACIÓN DE LOGS ===
logLevel: "info"

# === CONFIGURACIÓN DE SEGURIDAD ===
# Hosts permitidos para acceso externo
allowedHosts:
  - "tankeeee2.duckdns.org"
  - "localhost"
  - "127.0.0.1"
  - "host.docker.internal"

# === CONFIGURACIÓN DE ICONOS ===
iconStyle: "theme"

# === CONFIGURACIÓN DE FECHA Y HORA ===
dateFormat:
  dateStyle: "long"
  timeStyle: "short"
  hour12: false

# === CONFIGURACIÓN DE UNIDADES ===
units:
  temperature: "celsius"
  windSpeed: "kmh"
  pressure: "hPa"
  precipitation: "mm"

# === CONFIGURACIÓN DE WIDGETS DE ESTADO ===
statusStyle: "dot"

# === CONFIGURACIÓN DE ENLACES RÁPIDOS ===
quickConnect: true

# === CONFIGURACIÓN DE NOTIFICACIONES ===
# showErrors y hideErrors ya están definidos en la sección de widgets globales

# === CONFIGURACIÓN DE RENDIMIENTO ===
# Intervalo de actualización de widgets (en segundos)
refreshInterval: 30

# === CONFIGURACIÓN DE ACCESIBILIDAD ===
accessibility:
  reduceMotion: false
  highContrast: false

# === CONFIGURACIÓN DE PERSONALIZACIÓN ===
customCSS: |
  .information-widget-resource {
    border-radius: 8px;
  }
  
  .service-group {
    border-radius: 12px;
  }
  
  .bookmark-group {
    border-radius: 8px;
  }

# === CONFIGURACIÓN DE WIDGETS PERSONALIZADOS ===
customJS: |
  // Funciones personalizadas para el dashboard
  console.log("Tanke Dashboard cargado correctamente");

# === CONFIGURACIÓN DE MÉTRICAS ===
metrics:
  cpu: true
  memory: true
  disk: true
  network: true
  temperature: false

# === CONFIGURACIÓN DE CACHÉ ===
cache:
  ttl: 300 # 5 minutos

# === CONFIGURACIÓN DE PROXY ===
# Si necesitas configurar un proxy para acceder a servicios externos
# proxy:
#   http: "http://proxy.example.com:8080"
#   https: "https://proxy.example.com:8080"

# === CONFIGURACIÓN DE TIEMPO DE ESPERA ===
timeout: 10000 # 10 segundos

# === CONFIGURACIÓN DE FAVICON DINÁMICO ===
dynamicFavicon: true

# === CONFIGURACIÓN DE MODO KIOSKO ===
# Para pantallas dedicadas
# kiosk: true

# === CONFIGURACIÓN DE WIDGETS OCULTOS ===
# hideWidgets:
#   - "weather"
#   - "search"
