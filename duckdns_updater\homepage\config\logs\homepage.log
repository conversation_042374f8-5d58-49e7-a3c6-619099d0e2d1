[2025-08-01T10:07:03.547Z] info: kubernetes.yaml was copied to the config folder
[2025-08-01T10:07:18.581Z] info: proxmox.yaml was copied to the config folder
[2025-08-01T10:07:18.684Z] info: custom.css was copied to the config folder
[2025-08-01T10:07:18.689Z] info: custom.js was copied to the config folder
[2025-08-01T10:07:19.563Z] error: <service-helpers> Error getting services from Docker server 'showStopped': TypeError: Cannot read properties of null (reading 'conn')
    at <unknown> (.next/server/pages/index.js:12:60322)
    at Array.map (<anonymous>)
    at v (.next/server/pages/index.js:12:60264)
    at async g (.next/server/pages/index.js:1:15658)
    at async L (.next/server/pages/index.js:7:54090)
[2025-08-01T10:07:19.670Z] error: <service-helpers> Error getting services from Docker server 'my-docker': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:07:19.758Z] error: <service-helpers> Error getting services from Docker server 'jellyfin': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:07:19.759Z] error: <service-helpers> Error getting services from Docker server 'jellyseerr': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:07:19.759Z] error: <service-helpers> Error getting services from Docker server 'qbittorrent': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:07:19.768Z] error: <service-helpers> Error getting services from Docker server 'adguard': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:07:19.769Z] error: <service-helpers> Error getting services from Docker server 'sonarr': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:07:19.770Z] error: <service-helpers> Error getting services from Docker server 'radarr': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:07:19.770Z] error: <service-helpers> Error getting services from Docker server 'jackett': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:07:19.771Z] error: <service-helpers> Error getting services from Docker server 'flaresolverr': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:07:19.788Z] error: <service-helpers> Error getting services from Docker server 'portainer': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:07:19.789Z] error: <service-helpers> Error getting services from Docker server 'caddy_proxy': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:07:19.789Z] error: <service-helpers> Error getting services from Docker server 'watchtower': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:07:19.789Z] error: <service-helpers> Error getting services from Docker server 'homepage': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:07:19.790Z] error: <service-helpers> Error getting services from Docker server 'duckdns_jucago705': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:07:19.790Z] error: <service-helpers> Error getting services from Docker server 'duckdns_jucago706': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:07:19.790Z] error: <service-helpers> Error getting services from Docker server 'telegram-bot': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:07:19.791Z] error: <service-helpers> Error getting services from Docker server 'media-stack': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:07:19.791Z] error: <service-helpers> Error getting services from Docker server 'arr-stack': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:07:19.800Z] error: <service-helpers> Error getting services from Docker server 'system-stack': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:07:19.801Z] error: <service-helpers> Error getting services from Docker server 'infrastructure-stack': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:07:19.801Z] error: <service-helpers> Error getting services from Docker server 'showStats': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:07:19.802Z] error: <service-helpers> Error getting services from Docker server 'showLogs': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:07:19.802Z] error: <service-helpers> Error getting services from Docker server 'labelFilter': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:07:19.802Z] error: <service-helpers> Error getting services from Docker server 'refreshInterval': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:07:19.803Z] error: <service-helpers> Error getting services from Docker server 'readOnly': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:07:19.803Z] error: <service-helpers> Error getting services from Docker server 'timeout': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:07:19.803Z] error: <service-helpers> Error getting services from Docker server 'cache': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:07:19.816Z] error: <service-helpers> Error getting services from Docker server 'logs': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:07:19.816Z] error: <service-helpers> Error getting services from Docker server 'stats': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:07:19.817Z] error: <service-helpers> Error getting services from Docker server 'events': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:07:19.817Z] error: <service-helpers> Error getting services from Docker server 'images': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:07:19.819Z] error: <service-helpers> Error getting services from Docker server 'networks': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:07:19.829Z] error: <service-helpers> Error getting services from Docker server 'volumes': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:07:20.706Z] error: <dockerStatusService> TypeError: Cannot read properties of null (reading 'conn')
    at l (/app/.next/server/pages/api/docker/status/[...service].js:1:1051)
    at X (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:18447)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async z.render (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:19237)
    at async NextNodeServer.runApi (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:671:9)
    at async NextNodeServer.handleCatchallRenderRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:302:37)
    at async NextNodeServer.handleRequestImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/base-server.js:899:17)
    at async invokeRender (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:237:21)
    at async handleRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:428:24)
    at async requestHandlerImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:452:13)
[2025-08-01T10:07:20.874Z] error: <dockerStatusService> TypeError: Cannot read properties of null (reading 'conn')
    at l (/app/.next/server/pages/api/docker/status/[...service].js:1:1051)
    at X (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:18447)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async z.render (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:19237)
    at async NextNodeServer.runApi (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:671:9)
    at async NextNodeServer.handleCatchallRenderRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:302:37)
    at async NextNodeServer.handleRequestImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/base-server.js:899:17)
    at async invokeRender (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:237:21)
    at async handleRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:428:24)
    at async requestHandlerImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:452:13)
[2025-08-01T10:07:20.922Z] error: <dockerStatsService> TypeError: Cannot read properties of null (reading 'conn')
    at l (/app/.next/server/pages/api/docker/stats/[...service].js:1:3008)
    at X (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:18447)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async z.render (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:19237)
    at async NextNodeServer.runApi (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:671:9)
    at async NextNodeServer.handleCatchallRenderRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:302:37)
    at async NextNodeServer.handleRequestImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/base-server.js:899:17)
    at async invokeRender (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:237:21)
    at async handleRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:428:24)
    at async requestHandlerImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:452:13)
[2025-08-01T10:07:20.941Z] error: <dockerStatsService> TypeError: Cannot read properties of null (reading 'conn')
    at l (/app/.next/server/pages/api/docker/stats/[...service].js:1:3008)
    at X (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:18447)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async z.render (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:19237)
    at async NextNodeServer.runApi (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:671:9)
    at async NextNodeServer.handleCatchallRenderRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:302:37)
    at async NextNodeServer.handleRequestImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/base-server.js:899:17)
    at async invokeRender (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:237:21)
    at async handleRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:428:24)
    at async requestHandlerImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:452:13)
[2025-08-01T10:07:20.949Z] error: <httpProxy> Error calling http://host.docker.internal:61208/api/3/cpu...
[2025-08-01T10:07:20.949Z] error: <httpProxy> [
  500,
  Error: connect ECONNREFUSED **************:61208
      at <unknown> (Error: connect ECONNREFUSED **************:61208) {
    errno: -111,
    code: 'ECONNREFUSED',
    syscall: 'connect',
    address: '**************',
    port: 61208
  }
]
[2025-08-01T10:07:20.950Z] error: <glances> HTTP 500 getting data from glances API. Data: [object Object]
[2025-08-01T10:07:20.967Z] error: <dockerStatusService> TypeError: Cannot read properties of null (reading 'conn')
    at l (/app/.next/server/pages/api/docker/status/[...service].js:1:1051)
    at X (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:18447)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async z.render (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:19237)
    at async NextNodeServer.runApi (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:671:9)
    at async NextNodeServer.handleCatchallRenderRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:302:37)
    at async NextNodeServer.handleRequestImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/base-server.js:899:17)
    at async invokeRender (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:237:21)
    at async handleRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:428:24)
    at async requestHandlerImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:452:13)
[2025-08-01T10:07:21.001Z] error: <dockerStatsService> TypeError: Cannot read properties of null (reading 'conn')
    at l (/app/.next/server/pages/api/docker/stats/[...service].js:1:3008)
    at X (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:18447)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async z.render (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:19237)
    at async NextNodeServer.runApi (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:671:9)
    at async NextNodeServer.handleCatchallRenderRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:302:37)
    at async NextNodeServer.handleRequestImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/base-server.js:899:17)
    at async invokeRender (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:237:21)
    at async handleRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:428:24)
    at async requestHandlerImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:452:13)
[2025-08-01T10:07:21.017Z] error: <dockerStatsService> TypeError: Cannot read properties of null (reading 'conn')
    at l (/app/.next/server/pages/api/docker/stats/[...service].js:1:3008)
    at X (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:18447)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async z.render (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:19237)
    at async NextNodeServer.runApi (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:671:9)
    at async NextNodeServer.handleCatchallRenderRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:302:37)
    at async NextNodeServer.handleRequestImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/base-server.js:899:17)
    at async invokeRender (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:237:21)
    at async handleRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:428:24)
    at async requestHandlerImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:452:13)
[2025-08-01T10:07:21.035Z] error: <dockerStatusService> TypeError: Cannot read properties of null (reading 'conn')
    at l (/app/.next/server/pages/api/docker/status/[...service].js:1:1051)
    at X (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:18447)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async z.render (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:19237)
    at async NextNodeServer.runApi (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:671:9)
    at async NextNodeServer.handleCatchallRenderRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:302:37)
    at async NextNodeServer.handleRequestImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/base-server.js:899:17)
    at async invokeRender (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:237:21)
    at async handleRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:428:24)
    at async requestHandlerImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:452:13)
[2025-08-01T10:07:21.057Z] error: <dockerStatusService> TypeError: Cannot read properties of null (reading 'conn')
    at l (/app/.next/server/pages/api/docker/status/[...service].js:1:1051)
    at X (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:18447)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async z.render (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:19237)
    at async NextNodeServer.runApi (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:671:9)
    at async NextNodeServer.handleCatchallRenderRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:302:37)
    at async NextNodeServer.handleRequestImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/base-server.js:899:17)
    at async invokeRender (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:237:21)
    at async handleRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:428:24)
    at async requestHandlerImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:452:13)
[2025-08-01T10:07:21.081Z] error: <dockerStatsService> TypeError: Cannot read properties of null (reading 'conn')
    at l (/app/.next/server/pages/api/docker/stats/[...service].js:1:3008)
    at X (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:18447)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async z.render (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:19237)
    at async NextNodeServer.runApi (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:671:9)
    at async NextNodeServer.handleCatchallRenderRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:302:37)
    at async NextNodeServer.handleRequestImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/base-server.js:899:17)
    at async invokeRender (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:237:21)
    at async handleRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:428:24)
    at async requestHandlerImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:452:13)
[2025-08-01T10:07:21.100Z] error: <dockerStatsService> TypeError: Cannot read properties of null (reading 'conn')
    at l (/app/.next/server/pages/api/docker/stats/[...service].js:1:3008)
    at X (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:18447)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async z.render (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:19237)
    at async NextNodeServer.runApi (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:671:9)
    at async NextNodeServer.handleCatchallRenderRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:302:37)
    at async NextNodeServer.handleRequestImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/base-server.js:899:17)
    at async invokeRender (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:237:21)
    at async handleRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:428:24)
    at async requestHandlerImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:452:13)
[2025-08-01T10:07:21.137Z] error: <dockerStatusService> TypeError: Cannot read properties of null (reading 'conn')
    at l (/app/.next/server/pages/api/docker/status/[...service].js:1:1051)
    at X (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:18447)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async z.render (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:19237)
    at async NextNodeServer.runApi (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:671:9)
    at async NextNodeServer.handleCatchallRenderRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:302:37)
    at async NextNodeServer.handleRequestImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/base-server.js:899:17)
    at async invokeRender (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:237:21)
    at async handleRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:428:24)
    at async requestHandlerImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:452:13)
[2025-08-01T10:07:21.159Z] error: <dockerStatsService> TypeError: Cannot read properties of null (reading 'conn')
    at l (/app/.next/server/pages/api/docker/stats/[...service].js:1:3008)
    at X (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:18447)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async z.render (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:19237)
    at async NextNodeServer.runApi (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:671:9)
    at async NextNodeServer.handleCatchallRenderRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:302:37)
    at async NextNodeServer.handleRequestImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/base-server.js:899:17)
    at async invokeRender (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:237:21)
    at async handleRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:428:24)
    at async requestHandlerImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:452:13)
[2025-08-01T10:07:21.171Z] error: <dockerStatusService> TypeError: Cannot read properties of null (reading 'conn')
    at l (/app/.next/server/pages/api/docker/status/[...service].js:1:1051)
    at X (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:18447)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async z.render (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:19237)
    at async NextNodeServer.runApi (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:671:9)
    at async NextNodeServer.handleCatchallRenderRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:302:37)
    at async NextNodeServer.handleRequestImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/base-server.js:899:17)
    at async invokeRender (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:237:21)
    at async handleRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:428:24)
    at async requestHandlerImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:452:13)
[2025-08-01T10:07:21.193Z] error: <dockerStatusService> TypeError: Cannot read properties of null (reading 'conn')
    at l (/app/.next/server/pages/api/docker/status/[...service].js:1:1051)
    at X (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:18447)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async z.render (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:19237)
    at async NextNodeServer.runApi (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:671:9)
    at async NextNodeServer.handleCatchallRenderRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:302:37)
    at async NextNodeServer.handleRequestImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/base-server.js:899:17)
    at async invokeRender (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:237:21)
    at async handleRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:428:24)
    at async requestHandlerImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:452:13)
[2025-08-01T10:07:21.252Z] error: <dockerStatsService> TypeError: Cannot read properties of null (reading 'conn')
    at l (/app/.next/server/pages/api/docker/stats/[...service].js:1:3008)
    at X (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:18447)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async z.render (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:19237)
    at async NextNodeServer.runApi (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:671:9)
    at async NextNodeServer.handleCatchallRenderRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:302:37)
    at async NextNodeServer.handleRequestImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/base-server.js:899:17)
    at async invokeRender (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:237:21)
    at async handleRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:428:24)
    at async requestHandlerImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:452:13)
[2025-08-01T10:07:21.282Z] error: <dockerStatsService> TypeError: Cannot read properties of null (reading 'conn')
    at l (/app/.next/server/pages/api/docker/stats/[...service].js:1:3008)
    at X (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:18447)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async z.render (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:19237)
    at async NextNodeServer.runApi (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:671:9)
    at async NextNodeServer.handleCatchallRenderRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:302:37)
    at async NextNodeServer.handleRequestImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/base-server.js:899:17)
    at async invokeRender (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:237:21)
    at async handleRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:428:24)
    at async requestHandlerImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:452:13)
[2025-08-01T10:07:21.306Z] error: <dockerStatusService> TypeError: Cannot read properties of null (reading 'conn')
    at l (/app/.next/server/pages/api/docker/status/[...service].js:1:1051)
    at X (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:18447)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async z.render (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:19237)
    at async NextNodeServer.runApi (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:671:9)
    at async NextNodeServer.handleCatchallRenderRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:302:37)
    at async NextNodeServer.handleRequestImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/base-server.js:899:17)
    at async invokeRender (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:237:21)
    at async handleRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:428:24)
    at async requestHandlerImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:452:13)
[2025-08-01T10:07:21.324Z] error: <dockerStatsService> TypeError: Cannot read properties of null (reading 'conn')
    at l (/app/.next/server/pages/api/docker/stats/[...service].js:1:3008)
    at X (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:18447)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async z.render (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:19237)
    at async NextNodeServer.runApi (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:671:9)
    at async NextNodeServer.handleCatchallRenderRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:302:37)
    at async NextNodeServer.handleRequestImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/base-server.js:899:17)
    at async invokeRender (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:237:21)
    at async handleRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:428:24)
    at async requestHandlerImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:452:13)
[2025-08-01T10:07:21.347Z] error: <dockerStatsService> TypeError: Cannot read properties of null (reading 'conn')
    at l (/app/.next/server/pages/api/docker/stats/[...service].js:1:3008)
    at X (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:18447)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async z.render (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:19237)
    at async NextNodeServer.runApi (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:671:9)
    at async NextNodeServer.handleCatchallRenderRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:302:37)
    at async NextNodeServer.handleRequestImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/base-server.js:899:17)
    at async invokeRender (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:237:21)
    at async handleRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:428:24)
    at async requestHandlerImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:452:13)
[2025-08-01T10:07:21.361Z] error: <dockerStatusService> TypeError: Cannot read properties of null (reading 'conn')
    at l (/app/.next/server/pages/api/docker/status/[...service].js:1:1051)
    at X (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:18447)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async z.render (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:19237)
    at async NextNodeServer.runApi (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:671:9)
    at async NextNodeServer.handleCatchallRenderRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:302:37)
    at async NextNodeServer.handleRequestImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/base-server.js:899:17)
    at async invokeRender (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:237:21)
    at async handleRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:428:24)
    at async requestHandlerImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:452:13)
[2025-08-01T10:07:21.372Z] error: <dockerStatsService> TypeError: Cannot read properties of null (reading 'conn')
    at l (/app/.next/server/pages/api/docker/stats/[...service].js:1:3008)
    at X (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:18447)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async z.render (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:19237)
    at async NextNodeServer.runApi (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:671:9)
    at async NextNodeServer.handleCatchallRenderRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:302:37)
    at async NextNodeServer.handleRequestImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/base-server.js:899:17)
    at async invokeRender (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:237:21)
    at async handleRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:428:24)
    at async requestHandlerImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:452:13)
[2025-08-01T10:07:21.385Z] error: <dockerStatusService> TypeError: Cannot read properties of null (reading 'conn')
    at l (/app/.next/server/pages/api/docker/status/[...service].js:1:1051)
    at X (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:18447)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async z.render (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:19237)
    at async NextNodeServer.runApi (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:671:9)
    at async NextNodeServer.handleCatchallRenderRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:302:37)
    at async NextNodeServer.handleRequestImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/base-server.js:899:17)
    at async invokeRender (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:237:21)
    at async handleRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:428:24)
    at async requestHandlerImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:452:13)
[2025-08-01T10:07:21.399Z] error: <dockerStatsService> TypeError: Cannot read properties of null (reading 'conn')
    at l (/app/.next/server/pages/api/docker/stats/[...service].js:1:3008)
    at X (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:18447)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async z.render (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:19237)
    at async NextNodeServer.runApi (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:671:9)
    at async NextNodeServer.handleCatchallRenderRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:302:37)
    at async NextNodeServer.handleRequestImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/base-server.js:899:17)
    at async invokeRender (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:237:21)
    at async handleRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:428:24)
    at async requestHandlerImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:452:13)
[2025-08-01T10:07:21.437Z] error: <dockerStatusService> TypeError: Cannot read properties of null (reading 'conn')
    at l (/app/.next/server/pages/api/docker/status/[...service].js:1:1051)
    at X (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:18447)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async z.render (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:19237)
    at async NextNodeServer.runApi (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:671:9)
    at async NextNodeServer.handleCatchallRenderRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:302:37)
    at async NextNodeServer.handleRequestImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/base-server.js:899:17)
    at async invokeRender (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:237:21)
    at async handleRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:428:24)
    at async requestHandlerImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:452:13)
[2025-08-01T10:07:21.478Z] error: <dockerStatusService> TypeError: Cannot read properties of null (reading 'conn')
    at l (/app/.next/server/pages/api/docker/status/[...service].js:1:1051)
    at X (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:18447)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async z.render (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:19237)
    at async NextNodeServer.runApi (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:671:9)
    at async NextNodeServer.handleCatchallRenderRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:302:37)
    at async NextNodeServer.handleRequestImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/base-server.js:899:17)
    at async invokeRender (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:237:21)
    at async handleRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:428:24)
    at async requestHandlerImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:452:13)
[2025-08-01T10:07:21.509Z] error: <dockerStatusService> TypeError: Cannot read properties of null (reading 'conn')
    at l (/app/.next/server/pages/api/docker/status/[...service].js:1:1051)
    at X (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:18447)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async z.render (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:19237)
    at async NextNodeServer.runApi (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:671:9)
    at async NextNodeServer.handleCatchallRenderRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:302:37)
    at async NextNodeServer.handleRequestImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/base-server.js:899:17)
    at async invokeRender (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:237:21)
    at async handleRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:428:24)
    at async requestHandlerImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:452:13)
[2025-08-01T10:07:21.533Z] error: <dockerStatsService> TypeError: Cannot read properties of null (reading 'conn')
    at l (/app/.next/server/pages/api/docker/stats/[...service].js:1:3008)
    at X (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:18447)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async z.render (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:19237)
    at async NextNodeServer.runApi (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:671:9)
    at async NextNodeServer.handleCatchallRenderRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:302:37)
    at async NextNodeServer.handleRequestImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/base-server.js:899:17)
    at async invokeRender (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:237:21)
    at async handleRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:428:24)
    at async requestHandlerImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:452:13)
[2025-08-01T10:07:22.092Z] error: <qbittorrentProxyHandler> Error logging in to qBittorrent: Data: Fails.
[2025-08-01T10:07:22.144Z] error: <credentialedProxyHandler> HTTP Error 403 calling http://host.docker.internal:5055/api/v1/request/count
[2025-08-01T10:07:22.198Z] error: <httpProxy> Error calling http://host.docker.internal:2019/reverse_proxy/upstreams...
[2025-08-01T10:07:22.202Z] error: <httpProxy> [
  500,
  Error: connect ECONNREFUSED **************:2019
      at <unknown> (Error: connect ECONNREFUSED **************:2019) {
    errno: -111,
    code: 'ECONNREFUSED',
    syscall: 'connect',
    address: '**************',
    port: 2019
  }
]
[2025-08-01T10:07:22.306Z] error: <credentialedProxyHandler> HTTP Error 401 calling http://host.docker.internal:9000/api/endpoints/1/docker/containers/json?all=1
[2025-08-01T10:08:42.512Z] error: <dockerStatsService> TypeError: Cannot read properties of null (reading 'conn')
    at l (/app/.next/server/pages/api/docker/stats/[...service].js:1:3008)
    at X (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:18447)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async z.render (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:19237)
    at async NextNodeServer.runApi (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:671:9)
    at async NextNodeServer.handleCatchallRenderRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:302:37)
    at async NextNodeServer.handleRequestImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/base-server.js:899:17)
    at async invokeRender (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:237:21)
    at async handleRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:428:24)
    at async requestHandlerImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:452:13)
[2025-08-01T10:08:42.680Z] error: <service-helpers> Error getting services from Docker server 'showStopped': TypeError: Cannot read properties of null (reading 'conn')
    at <unknown> (.next/server/chunks/7325.js:1:5936)
    at Array.map (<anonymous>)
    at v (.next/server/chunks/7325.js:1:5878)
    at async h (.next/server/pages/api/bookmarks.js:1:2633)
    at async n (.next/server/pages/api/services.js:1:5141)
[2025-08-01T10:08:42.751Z] error: <service-helpers> Error getting services from Docker server 'my-docker': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:42.804Z] error: <dockerStatsService> TypeError: Cannot read properties of null (reading 'conn')
    at l (/app/.next/server/pages/api/docker/stats/[...service].js:1:3008)
    at X (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:18447)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async z.render (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:19237)
    at async NextNodeServer.runApi (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:671:9)
    at async NextNodeServer.handleCatchallRenderRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:302:37)
    at async NextNodeServer.handleRequestImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/base-server.js:899:17)
    at async invokeRender (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:237:21)
    at async handleRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:428:24)
    at async requestHandlerImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:452:13)
[2025-08-01T10:08:42.805Z] error: <service-helpers> Error getting services from Docker server 'jellyfin': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:42.805Z] error: <service-helpers> Error getting services from Docker server 'jellyseerr': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:42.806Z] error: <service-helpers> Error getting services from Docker server 'qbittorrent': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:42.806Z] error: <service-helpers> Error getting services from Docker server 'adguard': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:42.807Z] error: <service-helpers> Error getting services from Docker server 'sonarr': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:42.807Z] error: <service-helpers> Error getting services from Docker server 'radarr': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:42.814Z] error: <service-helpers> Error getting services from Docker server 'jackett': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:42.819Z] error: <service-helpers> Error getting services from Docker server 'flaresolverr': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:42.820Z] error: <service-helpers> Error getting services from Docker server 'portainer': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:42.821Z] error: <service-helpers> Error getting services from Docker server 'caddy_proxy': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:42.822Z] error: <service-helpers> Error getting services from Docker server 'watchtower': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:42.823Z] error: <service-helpers> Error getting services from Docker server 'homepage': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:42.824Z] error: <service-helpers> Error getting services from Docker server 'duckdns_jucago705': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:42.824Z] error: <service-helpers> Error getting services from Docker server 'duckdns_jucago706': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:42.825Z] error: <service-helpers> Error getting services from Docker server 'telegram-bot': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:42.825Z] error: <service-helpers> Error getting services from Docker server 'media-stack': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:42.826Z] error: <service-helpers> Error getting services from Docker server 'arr-stack': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:42.826Z] error: <service-helpers> Error getting services from Docker server 'system-stack': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:42.827Z] error: <service-helpers> Error getting services from Docker server 'infrastructure-stack': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:42.828Z] error: <service-helpers> Error getting services from Docker server 'showStats': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:42.828Z] error: <service-helpers> Error getting services from Docker server 'showLogs': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:42.829Z] error: <service-helpers> Error getting services from Docker server 'labelFilter': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:42.830Z] error: <service-helpers> Error getting services from Docker server 'refreshInterval': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:42.830Z] error: <service-helpers> Error getting services from Docker server 'readOnly': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:42.831Z] error: <service-helpers> Error getting services from Docker server 'timeout': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:42.832Z] error: <service-helpers> Error getting services from Docker server 'cache': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:42.833Z] error: <service-helpers> Error getting services from Docker server 'logs': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:42.833Z] error: <service-helpers> Error getting services from Docker server 'stats': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:42.834Z] error: <service-helpers> Error getting services from Docker server 'events': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:42.835Z] error: <service-helpers> Error getting services from Docker server 'images': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:42.835Z] error: <service-helpers> Error getting services from Docker server 'networks': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:42.836Z] error: <service-helpers> Error getting services from Docker server 'volumes': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:42.873Z] error: <dockerStatusService> TypeError: Cannot read properties of null (reading 'conn')
    at l (/app/.next/server/pages/api/docker/status/[...service].js:1:1051)
    at X (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:18447)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async z.render (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:19237)
    at async NextNodeServer.runApi (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:671:9)
    at async NextNodeServer.handleCatchallRenderRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:302:37)
    at async NextNodeServer.handleRequestImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/base-server.js:899:17)
    at async invokeRender (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:237:21)
    at async handleRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:428:24)
    at async requestHandlerImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:452:13)
[2025-08-01T10:08:42.874Z] error: <httpProxy> Error calling http://host.docker.internal:61208/api/3/cpu...
[2025-08-01T10:08:42.875Z] error: <httpProxy> [
  500,
  Error: connect ECONNREFUSED **************:61208
      at <unknown> (Error: connect ECONNREFUSED **************:61208) {
    errno: -111,
    code: 'ECONNREFUSED',
    syscall: 'connect',
    address: '**************',
    port: 61208
  }
]
[2025-08-01T10:08:42.876Z] error: <glances> HTTP 500 getting data from glances API. Data: [object Object]
[2025-08-01T10:08:42.885Z] error: <dockerStatusService> TypeError: Cannot read properties of null (reading 'conn')
    at l (/app/.next/server/pages/api/docker/status/[...service].js:1:1051)
    at X (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:18447)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async z.render (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:19237)
    at async NextNodeServer.runApi (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:671:9)
    at async NextNodeServer.handleCatchallRenderRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:302:37)
    at async NextNodeServer.handleRequestImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/base-server.js:899:17)
    at async invokeRender (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:237:21)
    at async handleRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:428:24)
    at async requestHandlerImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:452:13)
[2025-08-01T10:08:42.892Z] error: <dockerStatusService> TypeError: Cannot read properties of null (reading 'conn')
    at l (/app/.next/server/pages/api/docker/status/[...service].js:1:1051)
    at X (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:18447)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async z.render (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:19237)
    at async NextNodeServer.runApi (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:671:9)
    at async NextNodeServer.handleCatchallRenderRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:302:37)
    at async NextNodeServer.handleRequestImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/base-server.js:899:17)
    at async invokeRender (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:237:21)
    at async handleRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:428:24)
    at async requestHandlerImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:452:13)
[2025-08-01T10:08:42.901Z] error: <dockerStatusService> TypeError: Cannot read properties of null (reading 'conn')
    at l (/app/.next/server/pages/api/docker/status/[...service].js:1:1051)
    at X (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:18447)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async z.render (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:19237)
    at async NextNodeServer.runApi (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:671:9)
    at async NextNodeServer.handleCatchallRenderRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:302:37)
    at async NextNodeServer.handleRequestImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/base-server.js:899:17)
    at async invokeRender (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:237:21)
    at async handleRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:428:24)
    at async requestHandlerImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:452:13)
[2025-08-01T10:08:42.989Z] error: <service-helpers> Error getting services from Docker server 'showStopped': TypeError: Cannot read properties of null (reading 'conn')
    at <unknown> (.next/server/pages/index.js:12:60322)
    at Array.map (<anonymous>)
    at v (.next/server/pages/index.js:12:60264)
    at async g (.next/server/pages/index.js:1:15658)
    at async L (.next/server/pages/index.js:7:54090)
[2025-08-01T10:08:43.050Z] error: <service-helpers> Error getting services from Docker server 'my-docker': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:43.063Z] error: <service-helpers> Error getting services from Docker server 'jellyfin': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:43.063Z] error: <service-helpers> Error getting services from Docker server 'jellyseerr': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:43.063Z] error: <service-helpers> Error getting services from Docker server 'qbittorrent': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:43.064Z] error: <service-helpers> Error getting services from Docker server 'adguard': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:43.064Z] error: <service-helpers> Error getting services from Docker server 'sonarr': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:43.065Z] error: <service-helpers> Error getting services from Docker server 'radarr': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:43.065Z] error: <service-helpers> Error getting services from Docker server 'jackett': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:43.066Z] error: <service-helpers> Error getting services from Docker server 'flaresolverr': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:43.066Z] error: <service-helpers> Error getting services from Docker server 'portainer': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:43.067Z] error: <service-helpers> Error getting services from Docker server 'caddy_proxy': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:43.067Z] error: <service-helpers> Error getting services from Docker server 'watchtower': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:43.067Z] error: <service-helpers> Error getting services from Docker server 'homepage': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:43.068Z] error: <service-helpers> Error getting services from Docker server 'duckdns_jucago705': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:43.069Z] error: <service-helpers> Error getting services from Docker server 'duckdns_jucago706': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:43.070Z] error: <service-helpers> Error getting services from Docker server 'telegram-bot': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:43.071Z] error: <service-helpers> Error getting services from Docker server 'media-stack': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:43.072Z] error: <service-helpers> Error getting services from Docker server 'arr-stack': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:43.073Z] error: <service-helpers> Error getting services from Docker server 'system-stack': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:43.073Z] error: <service-helpers> Error getting services from Docker server 'infrastructure-stack': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:43.074Z] error: <service-helpers> Error getting services from Docker server 'showStats': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:43.074Z] error: <service-helpers> Error getting services from Docker server 'showLogs': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:43.075Z] error: <service-helpers> Error getting services from Docker server 'labelFilter': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:43.075Z] error: <service-helpers> Error getting services from Docker server 'refreshInterval': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:43.076Z] error: <service-helpers> Error getting services from Docker server 'readOnly': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:43.076Z] error: <service-helpers> Error getting services from Docker server 'timeout': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:43.076Z] error: <service-helpers> Error getting services from Docker server 'cache': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:43.077Z] error: <service-helpers> Error getting services from Docker server 'logs': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:43.077Z] error: <service-helpers> Error getting services from Docker server 'stats': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:43.079Z] error: <service-helpers> Error getting services from Docker server 'events': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:43.079Z] error: <service-helpers> Error getting services from Docker server 'images': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:43.079Z] error: <service-helpers> Error getting services from Docker server 'networks': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:43.080Z] error: <service-helpers> Error getting services from Docker server 'volumes': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:43.098Z] error: <dockerStatusService> TypeError: Cannot read properties of null (reading 'conn')
    at l (/app/.next/server/pages/api/docker/status/[...service].js:1:1051)
    at X (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:18447)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async z.render (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:19237)
    at async NextNodeServer.runApi (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:671:9)
    at async NextNodeServer.handleCatchallRenderRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:302:37)
    at async NextNodeServer.handleRequestImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/base-server.js:899:17)
    at async invokeRender (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:237:21)
    at async handleRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:428:24)
    at async requestHandlerImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:452:13)
[2025-08-01T10:08:43.107Z] error: <dockerStatusService> TypeError: Cannot read properties of null (reading 'conn')
    at l (/app/.next/server/pages/api/docker/status/[...service].js:1:1051)
    at X (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:18447)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async z.render (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:19237)
    at async NextNodeServer.runApi (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:671:9)
    at async NextNodeServer.handleCatchallRenderRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:302:37)
    at async NextNodeServer.handleRequestImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/base-server.js:899:17)
    at async invokeRender (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:237:21)
    at async handleRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:428:24)
    at async requestHandlerImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:452:13)
[2025-08-01T10:08:43.115Z] error: <dockerStatusService> TypeError: Cannot read properties of null (reading 'conn')
    at l (/app/.next/server/pages/api/docker/status/[...service].js:1:1051)
    at X (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:18447)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async z.render (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:19237)
    at async NextNodeServer.runApi (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:671:9)
    at async NextNodeServer.handleCatchallRenderRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:302:37)
    at async NextNodeServer.handleRequestImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/base-server.js:899:17)
    at async invokeRender (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:237:21)
    at async handleRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:428:24)
    at async requestHandlerImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:452:13)
[2025-08-01T10:08:43.138Z] error: <dockerStatsService> TypeError: Cannot read properties of null (reading 'conn')
    at l (/app/.next/server/pages/api/docker/stats/[...service].js:1:3008)
    at X (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:18447)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async z.render (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:19237)
    at async NextNodeServer.runApi (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:671:9)
    at async NextNodeServer.handleCatchallRenderRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:302:37)
    at async NextNodeServer.handleRequestImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/base-server.js:899:17)
    at async invokeRender (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:237:21)
    at async handleRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:428:24)
    at async requestHandlerImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:452:13)
[2025-08-01T10:08:43.147Z] error: <dockerStatsService> TypeError: Cannot read properties of null (reading 'conn')
    at l (/app/.next/server/pages/api/docker/stats/[...service].js:1:3008)
    at X (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:18447)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async z.render (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:19237)
    at async NextNodeServer.runApi (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:671:9)
    at async NextNodeServer.handleCatchallRenderRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:302:37)
    at async NextNodeServer.handleRequestImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/base-server.js:899:17)
    at async invokeRender (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:237:21)
    at async handleRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:428:24)
    at async requestHandlerImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:452:13)
[2025-08-01T10:08:43.176Z] error: <dockerStatsService> TypeError: Cannot read properties of null (reading 'conn')
    at l (/app/.next/server/pages/api/docker/stats/[...service].js:1:3008)
    at X (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:18447)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async z.render (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:19237)
    at async NextNodeServer.runApi (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:671:9)
    at async NextNodeServer.handleCatchallRenderRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:302:37)
    at async NextNodeServer.handleRequestImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/base-server.js:899:17)
    at async invokeRender (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:237:21)
    at async handleRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:428:24)
    at async requestHandlerImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:452:13)
[2025-08-01T10:08:43.196Z] error: <dockerStatsService> TypeError: Cannot read properties of null (reading 'conn')
    at l (/app/.next/server/pages/api/docker/stats/[...service].js:1:3008)
    at X (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:18447)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async z.render (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:19237)
    at async NextNodeServer.runApi (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:671:9)
    at async NextNodeServer.handleCatchallRenderRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:302:37)
    at async NextNodeServer.handleRequestImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/base-server.js:899:17)
    at async invokeRender (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:237:21)
    at async handleRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:428:24)
    at async requestHandlerImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:452:13)
[2025-08-01T10:08:43.207Z] error: <dockerStatsService> TypeError: Cannot read properties of null (reading 'conn')
    at l (/app/.next/server/pages/api/docker/stats/[...service].js:1:3008)
    at X (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:18447)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async z.render (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:19237)
    at async NextNodeServer.runApi (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:671:9)
    at async NextNodeServer.handleCatchallRenderRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:302:37)
    at async NextNodeServer.handleRequestImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/base-server.js:899:17)
    at async invokeRender (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:237:21)
    at async handleRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:428:24)
    at async requestHandlerImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:452:13)
[2025-08-01T10:08:43.221Z] error: <dockerStatusService> TypeError: Cannot read properties of null (reading 'conn')
    at l (/app/.next/server/pages/api/docker/status/[...service].js:1:1051)
    at X (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:18447)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async z.render (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:19237)
    at async NextNodeServer.runApi (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:671:9)
    at async NextNodeServer.handleCatchallRenderRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:302:37)
    at async NextNodeServer.handleRequestImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/base-server.js:899:17)
    at async invokeRender (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:237:21)
    at async handleRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:428:24)
    at async requestHandlerImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:452:13)
[2025-08-01T10:08:43.257Z] error: <dockerStatusService> TypeError: Cannot read properties of null (reading 'conn')
    at l (/app/.next/server/pages/api/docker/status/[...service].js:1:1051)
    at X (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:18447)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async z.render (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:19237)
    at async NextNodeServer.runApi (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:671:9)
    at async NextNodeServer.handleCatchallRenderRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:302:37)
    at async NextNodeServer.handleRequestImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/base-server.js:899:17)
    at async invokeRender (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:237:21)
    at async handleRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:428:24)
    at async requestHandlerImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:452:13)
[2025-08-01T10:08:43.283Z] error: <dockerStatsService> TypeError: Cannot read properties of null (reading 'conn')
    at l (/app/.next/server/pages/api/docker/stats/[...service].js:1:3008)
    at X (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:18447)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async z.render (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:19237)
    at async NextNodeServer.runApi (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:671:9)
    at async NextNodeServer.handleCatchallRenderRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:302:37)
    at async NextNodeServer.handleRequestImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/base-server.js:899:17)
    at async invokeRender (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:237:21)
    at async handleRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:428:24)
    at async requestHandlerImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:452:13)
[2025-08-01T10:08:43.308Z] error: <dockerStatsService> TypeError: Cannot read properties of null (reading 'conn')
    at l (/app/.next/server/pages/api/docker/stats/[...service].js:1:3008)
    at X (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:18447)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async z.render (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:19237)
    at async NextNodeServer.runApi (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:671:9)
    at async NextNodeServer.handleCatchallRenderRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:302:37)
    at async NextNodeServer.handleRequestImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/base-server.js:899:17)
    at async invokeRender (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:237:21)
    at async handleRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:428:24)
    at async requestHandlerImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:452:13)
[2025-08-01T10:08:43.337Z] error: <dockerStatsService> TypeError: Cannot read properties of null (reading 'conn')
    at l (/app/.next/server/pages/api/docker/stats/[...service].js:1:3008)
    at X (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:18447)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async z.render (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:19237)
    at async NextNodeServer.runApi (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:671:9)
    at async NextNodeServer.handleCatchallRenderRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:302:37)
    at async NextNodeServer.handleRequestImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/base-server.js:899:17)
    at async invokeRender (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:237:21)
    at async handleRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:428:24)
    at async requestHandlerImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:452:13)
[2025-08-01T10:08:43.349Z] error: <dockerStatusService> TypeError: Cannot read properties of null (reading 'conn')
    at l (/app/.next/server/pages/api/docker/status/[...service].js:1:1051)
    at X (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:18447)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async z.render (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:19237)
    at async NextNodeServer.runApi (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:671:9)
    at async NextNodeServer.handleCatchallRenderRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:302:37)
    at async NextNodeServer.handleRequestImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/base-server.js:899:17)
    at async invokeRender (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:237:21)
    at async handleRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:428:24)
    at async requestHandlerImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:452:13)
[2025-08-01T10:08:43.358Z] error: <dockerStatusService> TypeError: Cannot read properties of null (reading 'conn')
    at l (/app/.next/server/pages/api/docker/status/[...service].js:1:1051)
    at X (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:18447)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async z.render (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:19237)
    at async NextNodeServer.runApi (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:671:9)
    at async NextNodeServer.handleCatchallRenderRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:302:37)
    at async NextNodeServer.handleRequestImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/base-server.js:899:17)
    at async invokeRender (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:237:21)
    at async handleRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:428:24)
    at async requestHandlerImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:452:13)
[2025-08-01T10:08:43.373Z] error: <dockerStatsService> TypeError: Cannot read properties of null (reading 'conn')
    at l (/app/.next/server/pages/api/docker/stats/[...service].js:1:3008)
    at X (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:18447)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async z.render (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:19237)
    at async NextNodeServer.runApi (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:671:9)
    at async NextNodeServer.handleCatchallRenderRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:302:37)
    at async NextNodeServer.handleRequestImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/base-server.js:899:17)
    at async invokeRender (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:237:21)
    at async handleRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:428:24)
    at async requestHandlerImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:452:13)
[2025-08-01T10:08:43.382Z] error: <dockerStatusService> TypeError: Cannot read properties of null (reading 'conn')
    at l (/app/.next/server/pages/api/docker/status/[...service].js:1:1051)
    at X (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:18447)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async z.render (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:19237)
    at async NextNodeServer.runApi (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:671:9)
    at async NextNodeServer.handleCatchallRenderRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:302:37)
    at async NextNodeServer.handleRequestImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/base-server.js:899:17)
    at async invokeRender (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:237:21)
    at async handleRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:428:24)
    at async requestHandlerImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:452:13)
[2025-08-01T10:08:43.408Z] error: <dockerStatsService> TypeError: Cannot read properties of null (reading 'conn')
    at l (/app/.next/server/pages/api/docker/stats/[...service].js:1:3008)
    at X (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:18447)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async z.render (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:19237)
    at async NextNodeServer.runApi (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:671:9)
    at async NextNodeServer.handleCatchallRenderRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:302:37)
    at async NextNodeServer.handleRequestImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/base-server.js:899:17)
    at async invokeRender (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:237:21)
    at async handleRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:428:24)
    at async requestHandlerImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:452:13)
[2025-08-01T10:08:43.418Z] error: <dockerStatusService> TypeError: Cannot read properties of null (reading 'conn')
    at l (/app/.next/server/pages/api/docker/status/[...service].js:1:1051)
    at X (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:18447)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async z.render (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:19237)
    at async NextNodeServer.runApi (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:671:9)
    at async NextNodeServer.handleCatchallRenderRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:302:37)
    at async NextNodeServer.handleRequestImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/base-server.js:899:17)
    at async invokeRender (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:237:21)
    at async handleRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:428:24)
    at async requestHandlerImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:452:13)
[2025-08-01T10:08:43.429Z] error: <dockerStatsService> TypeError: Cannot read properties of null (reading 'conn')
    at l (/app/.next/server/pages/api/docker/stats/[...service].js:1:3008)
    at X (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:18447)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async z.render (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:19237)
    at async NextNodeServer.runApi (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:671:9)
    at async NextNodeServer.handleCatchallRenderRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:302:37)
    at async NextNodeServer.handleRequestImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/base-server.js:899:17)
    at async invokeRender (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:237:21)
    at async handleRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:428:24)
    at async requestHandlerImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:452:13)
[2025-08-01T10:08:43.441Z] error: <dockerStatusService> TypeError: Cannot read properties of null (reading 'conn')
    at l (/app/.next/server/pages/api/docker/status/[...service].js:1:1051)
    at X (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:18447)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async z.render (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:19237)
    at async NextNodeServer.runApi (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:671:9)
    at async NextNodeServer.handleCatchallRenderRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:302:37)
    at async NextNodeServer.handleRequestImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/base-server.js:899:17)
    at async invokeRender (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:237:21)
    at async handleRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:428:24)
    at async requestHandlerImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:452:13)
[2025-08-01T10:08:43.560Z] error: <dockerStatsService> TypeError: Cannot read properties of null (reading 'conn')
    at l (/app/.next/server/pages/api/docker/stats/[...service].js:1:3008)
    at X (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:18447)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async z.render (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:19237)
    at async NextNodeServer.runApi (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:671:9)
    at async NextNodeServer.handleCatchallRenderRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:302:37)
    at async NextNodeServer.handleRequestImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/base-server.js:899:17)
    at async invokeRender (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:237:21)
    at async handleRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:428:24)
    at async requestHandlerImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:452:13)
[2025-08-01T10:08:43.611Z] error: <httpProxy> Error calling http://host.docker.internal:61208/api/3/cpu...
[2025-08-01T10:08:43.611Z] error: <httpProxy> [
  500,
  Error: connect ECONNREFUSED **************:61208
      at <unknown> (Error: connect ECONNREFUSED **************:61208) {
    errno: -111,
    code: 'ECONNREFUSED',
    syscall: 'connect',
    address: '**************',
    port: 61208
  }
]
[2025-08-01T10:08:43.611Z] error: <glances> HTTP 500 getting data from glances API. Data: [object Object]
[2025-08-01T10:08:43.669Z] error: <service-helpers> Error getting services from Docker server 'showStopped': TypeError: Cannot read properties of null (reading 'conn')
    at <unknown> (.next/server/chunks/7325.js:1:5936)
    at Array.map (<anonymous>)
    at v (.next/server/chunks/7325.js:1:5878)
    at async b (.next/server/chunks/7325.js:1:7813)
    at async A (.next/server/chunks/7325.js:1:8010)
    at async u (.next/server/pages/api/services/proxy.js:25:31132)
[2025-08-01T10:08:43.702Z] error: <service-helpers> Error getting services from Docker server 'my-docker': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:43.711Z] error: <service-helpers> Error getting services from Docker server 'jellyfin': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:43.711Z] error: <service-helpers> Error getting services from Docker server 'jellyseerr': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:43.712Z] error: <service-helpers> Error getting services from Docker server 'qbittorrent': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:43.712Z] error: <service-helpers> Error getting services from Docker server 'adguard': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:43.713Z] error: <service-helpers> Error getting services from Docker server 'sonarr': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:43.714Z] error: <service-helpers> Error getting services from Docker server 'radarr': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:43.715Z] error: <service-helpers> Error getting services from Docker server 'jackett': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:43.715Z] error: <service-helpers> Error getting services from Docker server 'flaresolverr': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:43.715Z] error: <service-helpers> Error getting services from Docker server 'portainer': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:43.716Z] error: <service-helpers> Error getting services from Docker server 'caddy_proxy': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:43.716Z] error: <service-helpers> Error getting services from Docker server 'watchtower': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:43.717Z] error: <service-helpers> Error getting services from Docker server 'homepage': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:43.718Z] error: <service-helpers> Error getting services from Docker server 'duckdns_jucago705': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:43.718Z] error: <service-helpers> Error getting services from Docker server 'duckdns_jucago706': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:43.719Z] error: <service-helpers> Error getting services from Docker server 'telegram-bot': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:43.719Z] error: <service-helpers> Error getting services from Docker server 'media-stack': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:43.720Z] error: <service-helpers> Error getting services from Docker server 'arr-stack': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:43.720Z] error: <service-helpers> Error getting services from Docker server 'system-stack': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:43.720Z] error: <service-helpers> Error getting services from Docker server 'infrastructure-stack': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:43.721Z] error: <service-helpers> Error getting services from Docker server 'showStats': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:43.721Z] error: <service-helpers> Error getting services from Docker server 'showLogs': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:43.722Z] error: <service-helpers> Error getting services from Docker server 'labelFilter': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:43.722Z] error: <service-helpers> Error getting services from Docker server 'refreshInterval': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:43.723Z] error: <service-helpers> Error getting services from Docker server 'readOnly': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:43.723Z] error: <service-helpers> Error getting services from Docker server 'timeout': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:43.724Z] error: <service-helpers> Error getting services from Docker server 'cache': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:43.724Z] error: <service-helpers> Error getting services from Docker server 'logs': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:43.724Z] error: <service-helpers> Error getting services from Docker server 'stats': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:43.725Z] error: <service-helpers> Error getting services from Docker server 'events': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:43.725Z] error: <service-helpers> Error getting services from Docker server 'images': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:43.726Z] error: <service-helpers> Error getting services from Docker server 'networks': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:43.726Z] error: <service-helpers> Error getting services from Docker server 'volumes': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:43.887Z] error: <service-helpers> Error getting services from Docker server 'showStopped': TypeError: Cannot read properties of null (reading 'conn')
    at <unknown> (.next/server/chunks/7325.js:1:5936)
    at Array.map (<anonymous>)
    at v (.next/server/chunks/7325.js:1:5878)
    at async b (.next/server/chunks/7325.js:1:7813)
    at async A (.next/server/chunks/7325.js:1:8010)
    at async u (.next/server/pages/api/services/proxy.js:25:31132)
[2025-08-01T10:08:43.978Z] error: <service-helpers> Error getting services from Docker server 'my-docker': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:43.987Z] error: <service-helpers> Error getting services from Docker server 'jellyfin': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:43.987Z] error: <service-helpers> Error getting services from Docker server 'jellyseerr': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:43.988Z] error: <service-helpers> Error getting services from Docker server 'qbittorrent': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:43.988Z] error: <service-helpers> Error getting services from Docker server 'adguard': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:43.989Z] error: <service-helpers> Error getting services from Docker server 'sonarr': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:43.989Z] error: <service-helpers> Error getting services from Docker server 'radarr': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:43.990Z] error: <service-helpers> Error getting services from Docker server 'jackett': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:43.991Z] error: <service-helpers> Error getting services from Docker server 'flaresolverr': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:43.992Z] error: <service-helpers> Error getting services from Docker server 'portainer': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:43.992Z] error: <service-helpers> Error getting services from Docker server 'caddy_proxy': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:43.993Z] error: <service-helpers> Error getting services from Docker server 'watchtower': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:43.993Z] error: <service-helpers> Error getting services from Docker server 'homepage': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:43.994Z] error: <service-helpers> Error getting services from Docker server 'duckdns_jucago705': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:43.994Z] error: <service-helpers> Error getting services from Docker server 'duckdns_jucago706': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:43.994Z] error: <service-helpers> Error getting services from Docker server 'telegram-bot': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:43.995Z] error: <service-helpers> Error getting services from Docker server 'media-stack': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:43.995Z] error: <service-helpers> Error getting services from Docker server 'arr-stack': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:43.995Z] error: <service-helpers> Error getting services from Docker server 'system-stack': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:43.996Z] error: <service-helpers> Error getting services from Docker server 'infrastructure-stack': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:43.996Z] error: <service-helpers> Error getting services from Docker server 'showStats': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:44.001Z] error: <service-helpers> Error getting services from Docker server 'showLogs': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:44.001Z] error: <service-helpers> Error getting services from Docker server 'labelFilter': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:44.001Z] error: <service-helpers> Error getting services from Docker server 'refreshInterval': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:44.001Z] error: <service-helpers> Error getting services from Docker server 'readOnly': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:44.002Z] error: <service-helpers> Error getting services from Docker server 'timeout': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:44.002Z] error: <service-helpers> Error getting services from Docker server 'cache': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:44.003Z] error: <service-helpers> Error getting services from Docker server 'logs': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:44.004Z] error: <service-helpers> Error getting services from Docker server 'stats': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:44.005Z] error: <service-helpers> Error getting services from Docker server 'events': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:44.005Z] error: <service-helpers> Error getting services from Docker server 'images': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:44.006Z] error: <service-helpers> Error getting services from Docker server 'networks': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:08:44.006Z] error: <service-helpers> Error getting services from Docker server 'volumes': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:45.067Z] error: <service-helpers> Error getting services from Docker server 'showStopped': TypeError: Cannot read properties of null (reading 'conn')
    at <unknown> (.next/server/chunks/7325.js:1:5936)
    at Array.map (<anonymous>)
    at v (.next/server/chunks/7325.js:1:5878)
    at async h (.next/server/pages/api/bookmarks.js:1:2633)
    at async n (.next/server/pages/api/services.js:1:5141)
[2025-08-01T10:09:45.135Z] error: <service-helpers> Error getting services from Docker server 'my-docker': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:45.160Z] error: <service-helpers> Error getting services from Docker server 'jellyfin': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:45.162Z] error: <service-helpers> Error getting services from Docker server 'jellyseerr': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:45.162Z] error: <service-helpers> Error getting services from Docker server 'qbittorrent': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:45.163Z] error: <service-helpers> Error getting services from Docker server 'adguard': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:45.164Z] error: <service-helpers> Error getting services from Docker server 'sonarr': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:45.164Z] error: <service-helpers> Error getting services from Docker server 'radarr': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:45.165Z] error: <service-helpers> Error getting services from Docker server 'jackett': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:45.165Z] error: <service-helpers> Error getting services from Docker server 'flaresolverr': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:45.166Z] error: <service-helpers> Error getting services from Docker server 'portainer': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:45.166Z] error: <service-helpers> Error getting services from Docker server 'caddy_proxy': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:45.167Z] error: <service-helpers> Error getting services from Docker server 'watchtower': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:45.167Z] error: <service-helpers> Error getting services from Docker server 'homepage': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:45.168Z] error: <service-helpers> Error getting services from Docker server 'duckdns_jucago705': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:45.168Z] error: <service-helpers> Error getting services from Docker server 'duckdns_jucago706': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:45.168Z] error: <service-helpers> Error getting services from Docker server 'telegram-bot': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:45.169Z] error: <service-helpers> Error getting services from Docker server 'media-stack': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:45.169Z] error: <service-helpers> Error getting services from Docker server 'arr-stack': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:45.170Z] error: <service-helpers> Error getting services from Docker server 'system-stack': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:45.170Z] error: <service-helpers> Error getting services from Docker server 'infrastructure-stack': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:45.170Z] error: <service-helpers> Error getting services from Docker server 'showStats': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:45.171Z] error: <service-helpers> Error getting services from Docker server 'showLogs': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:45.171Z] error: <service-helpers> Error getting services from Docker server 'labelFilter': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:45.171Z] error: <service-helpers> Error getting services from Docker server 'refreshInterval': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:45.172Z] error: <service-helpers> Error getting services from Docker server 'readOnly': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:45.172Z] error: <service-helpers> Error getting services from Docker server 'timeout': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:45.173Z] error: <service-helpers> Error getting services from Docker server 'cache': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:45.173Z] error: <service-helpers> Error getting services from Docker server 'logs': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:45.173Z] error: <service-helpers> Error getting services from Docker server 'stats': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:45.174Z] error: <service-helpers> Error getting services from Docker server 'events': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:45.174Z] error: <service-helpers> Error getting services from Docker server 'images': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:45.175Z] error: <service-helpers> Error getting services from Docker server 'networks': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:45.175Z] error: <service-helpers> Error getting services from Docker server 'volumes': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:45.393Z] error: undefined
[2025-08-01T10:09:45.394Z] error: TypeError: Cannot set properties of undefined (setting 'version')
    at l (/app/.next/server/pages/api/widgets/glances.js:1:2377)
    at async X (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:18441)
    at async z.render (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:19237)
    at async NextNodeServer.runApi (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:671:9)
    at async NextNodeServer.handleCatchallRenderRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:302:37)
    at async NextNodeServer.handleRequestImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/base-server.js:899:17)
    at async invokeRender (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:237:21)
    at async handleRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:428:24)
    at async requestHandlerImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:452:13)
    at async Server.requestListener (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/start-server.js:158:13)
[2025-08-01T10:09:45.408Z] error: undefined
[2025-08-01T10:09:45.410Z] error: TypeError: Cannot destructure property 'apiKey' of '(intermediate value)' as it is undefined.
    at c (/app/.next/server/pages/api/widgets/openweathermap.js:1:2521)
    at async X (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:18441)
    at async z.render (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/next-server/pages-api.runtime.prod.js:20:19237)
    at async NextNodeServer.runApi (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:671:9)
    at async NextNodeServer.handleCatchallRenderRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/next-server.js:302:37)
    at async NextNodeServer.handleRequestImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/base-server.js:899:17)
    at async invokeRender (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:237:21)
    at async handleRequest (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:428:24)
    at async requestHandlerImpl (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/router-server.js:452:13)
    at async Server.requestListener (/app/node_modules/.pnpm/next@15.3.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/start-server.js:158:13)
[2025-08-01T10:09:45.546Z] error: <service-helpers> Error getting services from Docker server 'showStopped': TypeError: Cannot read properties of null (reading 'conn')
    at <unknown> (.next/server/pages/index.js:12:60322)
    at Array.map (<anonymous>)
    at v (.next/server/pages/index.js:12:60264)
    at async g (.next/server/pages/index.js:1:15658)
    at async L (.next/server/pages/index.js:7:54090)
[2025-08-01T10:09:45.604Z] error: <service-helpers> Error getting services from Docker server 'my-docker': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:45.620Z] error: <service-helpers> Error getting services from Docker server 'jellyfin': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:45.621Z] error: <service-helpers> Error getting services from Docker server 'jellyseerr': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:45.621Z] error: <service-helpers> Error getting services from Docker server 'qbittorrent': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:45.622Z] error: <service-helpers> Error getting services from Docker server 'adguard': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:45.622Z] error: <service-helpers> Error getting services from Docker server 'sonarr': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:45.622Z] error: <service-helpers> Error getting services from Docker server 'radarr': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:45.623Z] error: <service-helpers> Error getting services from Docker server 'jackett': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:45.623Z] error: <service-helpers> Error getting services from Docker server 'flaresolverr': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:45.624Z] error: <service-helpers> Error getting services from Docker server 'portainer': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:45.624Z] error: <service-helpers> Error getting services from Docker server 'caddy_proxy': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:45.624Z] error: <service-helpers> Error getting services from Docker server 'watchtower': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:45.625Z] error: <service-helpers> Error getting services from Docker server 'homepage': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:45.629Z] error: <service-helpers> Error getting services from Docker server 'duckdns_jucago705': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:45.630Z] error: <service-helpers> Error getting services from Docker server 'duckdns_jucago706': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:45.630Z] error: <service-helpers> Error getting services from Docker server 'telegram-bot': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:45.630Z] error: <service-helpers> Error getting services from Docker server 'media-stack': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:45.631Z] error: <service-helpers> Error getting services from Docker server 'arr-stack': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:45.631Z] error: <service-helpers> Error getting services from Docker server 'system-stack': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:45.631Z] error: <service-helpers> Error getting services from Docker server 'infrastructure-stack': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:45.632Z] error: <service-helpers> Error getting services from Docker server 'showStats': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:45.632Z] error: <service-helpers> Error getting services from Docker server 'showLogs': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:45.633Z] error: <service-helpers> Error getting services from Docker server 'labelFilter': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:45.633Z] error: <service-helpers> Error getting services from Docker server 'refreshInterval': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:45.634Z] error: <service-helpers> Error getting services from Docker server 'readOnly': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:45.635Z] error: <service-helpers> Error getting services from Docker server 'timeout': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:45.635Z] error: <service-helpers> Error getting services from Docker server 'cache': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:45.636Z] error: <service-helpers> Error getting services from Docker server 'logs': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:45.636Z] error: <service-helpers> Error getting services from Docker server 'stats': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:45.637Z] error: <service-helpers> Error getting services from Docker server 'events': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:45.637Z] error: <service-helpers> Error getting services from Docker server 'images': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:45.637Z] error: <service-helpers> Error getting services from Docker server 'networks': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:45.638Z] error: <service-helpers> Error getting services from Docker server 'volumes': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:46.048Z] error: <service-helpers> Error getting services from Docker server 'showStopped': TypeError: Cannot read properties of null (reading 'conn')
    at <unknown> (.next/server/chunks/7325.js:1:5936)
    at Array.map (<anonymous>)
    at v (.next/server/chunks/7325.js:1:5878)
    at async h (.next/server/pages/api/bookmarks.js:1:2633)
    at async n (.next/server/pages/api/services.js:1:5141)
[2025-08-01T10:09:46.104Z] error: <service-helpers> Error getting services from Docker server 'my-docker': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:46.120Z] error: <service-helpers> Error getting services from Docker server 'jellyfin': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:46.120Z] error: <service-helpers> Error getting services from Docker server 'jellyseerr': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:46.121Z] error: <service-helpers> Error getting services from Docker server 'qbittorrent': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:46.121Z] error: <service-helpers> Error getting services from Docker server 'adguard': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:46.122Z] error: <service-helpers> Error getting services from Docker server 'sonarr': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:46.122Z] error: <service-helpers> Error getting services from Docker server 'radarr': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:46.122Z] error: <service-helpers> Error getting services from Docker server 'jackett': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:46.123Z] error: <service-helpers> Error getting services from Docker server 'flaresolverr': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:46.123Z] error: <service-helpers> Error getting services from Docker server 'portainer': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:46.124Z] error: <service-helpers> Error getting services from Docker server 'caddy_proxy': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:46.124Z] error: <service-helpers> Error getting services from Docker server 'watchtower': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:46.124Z] error: <service-helpers> Error getting services from Docker server 'homepage': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:46.125Z] error: <service-helpers> Error getting services from Docker server 'duckdns_jucago705': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:46.125Z] error: <service-helpers> Error getting services from Docker server 'duckdns_jucago706': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:46.125Z] error: <service-helpers> Error getting services from Docker server 'telegram-bot': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:46.126Z] error: <service-helpers> Error getting services from Docker server 'media-stack': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:46.126Z] error: <service-helpers> Error getting services from Docker server 'arr-stack': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:46.127Z] error: <service-helpers> Error getting services from Docker server 'system-stack': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:46.127Z] error: <service-helpers> Error getting services from Docker server 'infrastructure-stack': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:46.127Z] error: <service-helpers> Error getting services from Docker server 'showStats': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:46.128Z] error: <service-helpers> Error getting services from Docker server 'showLogs': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:46.128Z] error: <service-helpers> Error getting services from Docker server 'labelFilter': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:46.129Z] error: <service-helpers> Error getting services from Docker server 'refreshInterval': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:46.129Z] error: <service-helpers> Error getting services from Docker server 'readOnly': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:46.129Z] error: <service-helpers> Error getting services from Docker server 'timeout': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:46.130Z] error: <service-helpers> Error getting services from Docker server 'cache': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:46.130Z] error: <service-helpers> Error getting services from Docker server 'logs': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:46.131Z] error: <service-helpers> Error getting services from Docker server 'stats': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:46.131Z] error: <service-helpers> Error getting services from Docker server 'events': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:46.131Z] error: <service-helpers> Error getting services from Docker server 'images': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:46.132Z] error: <service-helpers> Error getting services from Docker server 'networks': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:46.132Z] error: <service-helpers> Error getting services from Docker server 'volumes': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:53.254Z] error: <service-helpers> Error getting services from Docker server 'showStopped': TypeError: Cannot read properties of null (reading 'conn')
    at <unknown> (.next/server/chunks/7325.js:1:5936)
    at Array.map (<anonymous>)
    at v (.next/server/chunks/7325.js:1:5878)
    at async h (.next/server/pages/api/bookmarks.js:1:2633)
    at async n (.next/server/pages/api/services.js:1:5141)
[2025-08-01T10:09:53.308Z] error: <service-helpers> Error getting services from Docker server 'my-docker': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:53.317Z] error: <service-helpers> Error getting services from Docker server 'jellyfin': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:53.318Z] error: <service-helpers> Error getting services from Docker server 'jellyseerr': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:53.318Z] error: <service-helpers> Error getting services from Docker server 'qbittorrent': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:53.319Z] error: <service-helpers> Error getting services from Docker server 'adguard': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:53.319Z] error: <service-helpers> Error getting services from Docker server 'sonarr': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:53.320Z] error: <service-helpers> Error getting services from Docker server 'radarr': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:53.320Z] error: <service-helpers> Error getting services from Docker server 'jackett': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:53.320Z] error: <service-helpers> Error getting services from Docker server 'flaresolverr': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:53.321Z] error: <service-helpers> Error getting services from Docker server 'portainer': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:53.321Z] error: <service-helpers> Error getting services from Docker server 'caddy_proxy': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:53.322Z] error: <service-helpers> Error getting services from Docker server 'watchtower': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:53.322Z] error: <service-helpers> Error getting services from Docker server 'homepage': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:53.323Z] error: <service-helpers> Error getting services from Docker server 'duckdns_jucago705': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:53.323Z] error: <service-helpers> Error getting services from Docker server 'duckdns_jucago706': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:53.324Z] error: <service-helpers> Error getting services from Docker server 'telegram-bot': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:53.324Z] error: <service-helpers> Error getting services from Docker server 'media-stack': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:53.324Z] error: <service-helpers> Error getting services from Docker server 'arr-stack': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:53.325Z] error: <service-helpers> Error getting services from Docker server 'system-stack': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:53.325Z] error: <service-helpers> Error getting services from Docker server 'infrastructure-stack': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:53.326Z] error: <service-helpers> Error getting services from Docker server 'showStats': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:53.326Z] error: <service-helpers> Error getting services from Docker server 'showLogs': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:53.327Z] error: <service-helpers> Error getting services from Docker server 'labelFilter': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:53.327Z] error: <service-helpers> Error getting services from Docker server 'refreshInterval': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:53.328Z] error: <service-helpers> Error getting services from Docker server 'readOnly': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:53.328Z] error: <service-helpers> Error getting services from Docker server 'timeout': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:53.328Z] error: <service-helpers> Error getting services from Docker server 'cache': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:53.329Z] error: <service-helpers> Error getting services from Docker server 'logs': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:53.329Z] error: <service-helpers> Error getting services from Docker server 'stats': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:53.330Z] error: <service-helpers> Error getting services from Docker server 'events': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:53.331Z] error: <service-helpers> Error getting services from Docker server 'images': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:53.331Z] error: <service-helpers> Error getting services from Docker server 'networks': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:09:53.332Z] error: <service-helpers> Error getting services from Docker server 'volumes': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:03.719Z] error: <service-helpers> Error getting services from Docker server 'showStopped': TypeError: Cannot read properties of null (reading 'conn')
    at <unknown> (.next/server/chunks/7325.js:1:5936)
    at Array.map (<anonymous>)
    at v (.next/server/chunks/7325.js:1:5878)
    at async h (.next/server/pages/api/bookmarks.js:1:2633)
    at async n (.next/server/pages/api/services.js:1:5141)
[2025-08-01T10:10:03.794Z] error: <service-helpers> Error getting services from Docker server 'my-docker': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:03.813Z] error: <service-helpers> Error getting services from Docker server 'jellyfin': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:03.814Z] error: <service-helpers> Error getting services from Docker server 'jellyseerr': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:03.814Z] error: <service-helpers> Error getting services from Docker server 'qbittorrent': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:03.815Z] error: <service-helpers> Error getting services from Docker server 'adguard': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:03.816Z] error: <service-helpers> Error getting services from Docker server 'sonarr': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:03.816Z] error: <service-helpers> Error getting services from Docker server 'radarr': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:03.817Z] error: <service-helpers> Error getting services from Docker server 'jackett': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:03.817Z] error: <service-helpers> Error getting services from Docker server 'flaresolverr': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:03.817Z] error: <service-helpers> Error getting services from Docker server 'portainer': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:03.818Z] error: <service-helpers> Error getting services from Docker server 'caddy_proxy': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:03.821Z] error: <service-helpers> Error getting services from Docker server 'watchtower': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:03.822Z] error: <service-helpers> Error getting services from Docker server 'homepage': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:03.823Z] error: <service-helpers> Error getting services from Docker server 'duckdns_jucago705': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:03.823Z] error: <service-helpers> Error getting services from Docker server 'duckdns_jucago706': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:03.824Z] error: <service-helpers> Error getting services from Docker server 'telegram-bot': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:03.827Z] error: <service-helpers> Error getting services from Docker server 'media-stack': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:03.827Z] error: <service-helpers> Error getting services from Docker server 'arr-stack': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:03.828Z] error: <service-helpers> Error getting services from Docker server 'system-stack': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:03.828Z] error: <service-helpers> Error getting services from Docker server 'infrastructure-stack': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:03.829Z] error: <service-helpers> Error getting services from Docker server 'showStats': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:03.829Z] error: <service-helpers> Error getting services from Docker server 'showLogs': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:03.830Z] error: <service-helpers> Error getting services from Docker server 'labelFilter': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:03.831Z] error: <service-helpers> Error getting services from Docker server 'refreshInterval': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:03.832Z] error: <service-helpers> Error getting services from Docker server 'readOnly': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:03.832Z] error: <service-helpers> Error getting services from Docker server 'timeout': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:03.832Z] error: <service-helpers> Error getting services from Docker server 'cache': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:03.833Z] error: <service-helpers> Error getting services from Docker server 'logs': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:03.833Z] error: <service-helpers> Error getting services from Docker server 'stats': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:03.834Z] error: <service-helpers> Error getting services from Docker server 'events': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:03.834Z] error: <service-helpers> Error getting services from Docker server 'images': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:03.834Z] error: <service-helpers> Error getting services from Docker server 'networks': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:03.835Z] error: <service-helpers> Error getting services from Docker server 'volumes': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:08.897Z] error: <service-helpers> Error getting services from Docker server 'showStopped': TypeError: Cannot read properties of null (reading 'conn')
    at <unknown> (.next/server/chunks/7325.js:1:5936)
    at Array.map (<anonymous>)
    at v (.next/server/chunks/7325.js:1:5878)
    at async h (.next/server/pages/api/bookmarks.js:1:2633)
    at async n (.next/server/pages/api/services.js:1:5141)
[2025-08-01T10:10:08.955Z] error: <service-helpers> Error getting services from Docker server 'my-docker': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:08.975Z] error: <service-helpers> Error getting services from Docker server 'jellyfin': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:08.975Z] error: <service-helpers> Error getting services from Docker server 'jellyseerr': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:08.976Z] error: <service-helpers> Error getting services from Docker server 'qbittorrent': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:08.977Z] error: <service-helpers> Error getting services from Docker server 'adguard': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:08.977Z] error: <service-helpers> Error getting services from Docker server 'sonarr': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:08.977Z] error: <service-helpers> Error getting services from Docker server 'radarr': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:08.978Z] error: <service-helpers> Error getting services from Docker server 'jackett': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:08.978Z] error: <service-helpers> Error getting services from Docker server 'flaresolverr': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:08.979Z] error: <service-helpers> Error getting services from Docker server 'portainer': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:08.980Z] error: <service-helpers> Error getting services from Docker server 'caddy_proxy': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:08.981Z] error: <service-helpers> Error getting services from Docker server 'watchtower': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:08.981Z] error: <service-helpers> Error getting services from Docker server 'homepage': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:08.981Z] error: <service-helpers> Error getting services from Docker server 'duckdns_jucago705': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:08.982Z] error: <service-helpers> Error getting services from Docker server 'duckdns_jucago706': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:08.982Z] error: <service-helpers> Error getting services from Docker server 'telegram-bot': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:08.983Z] error: <service-helpers> Error getting services from Docker server 'media-stack': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:08.983Z] error: <service-helpers> Error getting services from Docker server 'arr-stack': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:08.984Z] error: <service-helpers> Error getting services from Docker server 'system-stack': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:08.984Z] error: <service-helpers> Error getting services from Docker server 'infrastructure-stack': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:08.984Z] error: <service-helpers> Error getting services from Docker server 'showStats': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:08.985Z] error: <service-helpers> Error getting services from Docker server 'showLogs': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:08.985Z] error: <service-helpers> Error getting services from Docker server 'labelFilter': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:08.985Z] error: <service-helpers> Error getting services from Docker server 'refreshInterval': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:08.986Z] error: <service-helpers> Error getting services from Docker server 'readOnly': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:08.986Z] error: <service-helpers> Error getting services from Docker server 'timeout': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:08.987Z] error: <service-helpers> Error getting services from Docker server 'cache': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:08.987Z] error: <service-helpers> Error getting services from Docker server 'logs': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:08.987Z] error: <service-helpers> Error getting services from Docker server 'stats': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:08.988Z] error: <service-helpers> Error getting services from Docker server 'events': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:08.988Z] error: <service-helpers> Error getting services from Docker server 'images': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:08.989Z] error: <service-helpers> Error getting services from Docker server 'networks': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:08.989Z] error: <service-helpers> Error getting services from Docker server 'volumes': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:14.453Z] error: <service-helpers> Error getting services from Docker server 'showStopped': TypeError: Cannot read properties of null (reading 'conn')
    at <unknown> (.next/server/chunks/7325.js:1:5936)
    at Array.map (<anonymous>)
    at v (.next/server/chunks/7325.js:1:5878)
    at async h (.next/server/pages/api/bookmarks.js:1:2633)
    at async n (.next/server/pages/api/services.js:1:5141)
[2025-08-01T10:10:14.512Z] error: <service-helpers> Error getting services from Docker server 'my-docker': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:14.526Z] error: <service-helpers> Error getting services from Docker server 'jellyfin': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:14.526Z] error: <service-helpers> Error getting services from Docker server 'jellyseerr': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:14.527Z] error: <service-helpers> Error getting services from Docker server 'qbittorrent': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:14.527Z] error: <service-helpers> Error getting services from Docker server 'adguard': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:14.528Z] error: <service-helpers> Error getting services from Docker server 'sonarr': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:14.528Z] error: <service-helpers> Error getting services from Docker server 'radarr': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:14.529Z] error: <service-helpers> Error getting services from Docker server 'jackett': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:14.529Z] error: <service-helpers> Error getting services from Docker server 'flaresolverr': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:14.530Z] error: <service-helpers> Error getting services from Docker server 'portainer': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:14.530Z] error: <service-helpers> Error getting services from Docker server 'caddy_proxy': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:14.531Z] error: <service-helpers> Error getting services from Docker server 'watchtower': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:14.531Z] error: <service-helpers> Error getting services from Docker server 'homepage': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:14.532Z] error: <service-helpers> Error getting services from Docker server 'duckdns_jucago705': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:14.532Z] error: <service-helpers> Error getting services from Docker server 'duckdns_jucago706': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:14.532Z] error: <service-helpers> Error getting services from Docker server 'telegram-bot': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:14.534Z] error: <service-helpers> Error getting services from Docker server 'media-stack': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:14.535Z] error: <service-helpers> Error getting services from Docker server 'arr-stack': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:14.536Z] error: <service-helpers> Error getting services from Docker server 'system-stack': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:14.536Z] error: <service-helpers> Error getting services from Docker server 'infrastructure-stack': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:14.537Z] error: <service-helpers> Error getting services from Docker server 'showStats': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:14.537Z] error: <service-helpers> Error getting services from Docker server 'showLogs': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:14.538Z] error: <service-helpers> Error getting services from Docker server 'labelFilter': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:14.538Z] error: <service-helpers> Error getting services from Docker server 'refreshInterval': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:14.538Z] error: <service-helpers> Error getting services from Docker server 'readOnly': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:14.539Z] error: <service-helpers> Error getting services from Docker server 'timeout': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:14.539Z] error: <service-helpers> Error getting services from Docker server 'cache': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:14.540Z] error: <service-helpers> Error getting services from Docker server 'logs': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:14.541Z] error: <service-helpers> Error getting services from Docker server 'stats': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:14.541Z] error: <service-helpers> Error getting services from Docker server 'events': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:14.542Z] error: <service-helpers> Error getting services from Docker server 'images': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:14.542Z] error: <service-helpers> Error getting services from Docker server 'networks': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:14.543Z] error: <service-helpers> Error getting services from Docker server 'volumes': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:45.890Z] error: <service-helpers> Error getting services from Docker server 'showStopped': TypeError: Cannot read properties of null (reading 'conn')
    at <unknown> (.next/server/chunks/7325.js:1:5936)
    at Array.map (<anonymous>)
    at v (.next/server/chunks/7325.js:1:5878)
    at async h (.next/server/pages/api/bookmarks.js:1:2633)
    at async n (.next/server/pages/api/services.js:1:5141)
[2025-08-01T10:10:45.989Z] error: <service-helpers> Error getting services from Docker server 'jellyfin': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:45.989Z] error: <service-helpers> Error getting services from Docker server 'jellyseerr': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:45.990Z] error: <service-helpers> Error getting services from Docker server 'qbittorrent': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:45.990Z] error: <service-helpers> Error getting services from Docker server 'adguard': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:45.990Z] error: <service-helpers> Error getting services from Docker server 'sonarr': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:45.991Z] error: <service-helpers> Error getting services from Docker server 'radarr': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:45.991Z] error: <service-helpers> Error getting services from Docker server 'jackett': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:45.992Z] error: <service-helpers> Error getting services from Docker server 'flaresolverr': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:45.993Z] error: <service-helpers> Error getting services from Docker server 'portainer': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:45.993Z] error: <service-helpers> Error getting services from Docker server 'caddy_proxy': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:45.994Z] error: <service-helpers> Error getting services from Docker server 'watchtower': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:45.995Z] error: <service-helpers> Error getting services from Docker server 'homepage': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:45.995Z] error: <service-helpers> Error getting services from Docker server 'duckdns_jucago705': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:45.996Z] error: <service-helpers> Error getting services from Docker server 'duckdns_jucago706': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:45.996Z] error: <service-helpers> Error getting services from Docker server 'telegram-bot': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:45.998Z] error: <service-helpers> Error getting services from Docker server 'media-stack': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:45.998Z] error: <service-helpers> Error getting services from Docker server 'arr-stack': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:45.999Z] error: <service-helpers> Error getting services from Docker server 'system-stack': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:45.999Z] error: <service-helpers> Error getting services from Docker server 'infrastructure-stack': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:46.000Z] error: <service-helpers> Error getting services from Docker server 'showStats': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:46.000Z] error: <service-helpers> Error getting services from Docker server 'showLogs': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:46.002Z] error: <service-helpers> Error getting services from Docker server 'labelFilter': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:46.003Z] error: <service-helpers> Error getting services from Docker server 'refreshInterval': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:46.004Z] error: <service-helpers> Error getting services from Docker server 'readOnly': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:46.004Z] error: <service-helpers> Error getting services from Docker server 'timeout': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:46.004Z] error: <service-helpers> Error getting services from Docker server 'cache': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:46.005Z] error: <service-helpers> Error getting services from Docker server 'logs': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:46.006Z] error: <service-helpers> Error getting services from Docker server 'stats': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:46.006Z] error: <service-helpers> Error getting services from Docker server 'events': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:46.006Z] error: <service-helpers> Error getting services from Docker server 'images': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:46.007Z] error: <service-helpers> Error getting services from Docker server 'networks': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:46.007Z] error: <service-helpers> Error getting services from Docker server 'volumes': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:46.137Z] error: <service-helpers> Error getting services from Docker server 'showStopped': TypeError: Cannot read properties of null (reading 'conn')
    at <unknown> (.next/server/pages/index.js:12:60322)
    at Array.map (<anonymous>)
    at v (.next/server/pages/index.js:12:60264)
    at async g (.next/server/pages/index.js:1:15658)
    at async L (.next/server/pages/index.js:7:54090)
[2025-08-01T10:10:46.212Z] error: <service-helpers> Error getting services from Docker server 'jellyfin': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:46.212Z] error: <service-helpers> Error getting services from Docker server 'jellyseerr': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:46.213Z] error: <service-helpers> Error getting services from Docker server 'qbittorrent': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:46.213Z] error: <service-helpers> Error getting services from Docker server 'adguard': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:46.213Z] error: <service-helpers> Error getting services from Docker server 'sonarr': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:46.214Z] error: <service-helpers> Error getting services from Docker server 'radarr': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:46.214Z] error: <service-helpers> Error getting services from Docker server 'jackett': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:46.214Z] error: <service-helpers> Error getting services from Docker server 'flaresolverr': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:46.215Z] error: <service-helpers> Error getting services from Docker server 'portainer': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:46.215Z] error: <service-helpers> Error getting services from Docker server 'caddy_proxy': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:46.215Z] error: <service-helpers> Error getting services from Docker server 'watchtower': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:46.216Z] error: <service-helpers> Error getting services from Docker server 'homepage': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:46.216Z] error: <service-helpers> Error getting services from Docker server 'duckdns_jucago705': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:46.216Z] error: <service-helpers> Error getting services from Docker server 'duckdns_jucago706': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:46.217Z] error: <service-helpers> Error getting services from Docker server 'telegram-bot': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:46.217Z] error: <service-helpers> Error getting services from Docker server 'media-stack': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:46.218Z] error: <service-helpers> Error getting services from Docker server 'arr-stack': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:46.218Z] error: <service-helpers> Error getting services from Docker server 'system-stack': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:46.218Z] error: <service-helpers> Error getting services from Docker server 'infrastructure-stack': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:46.219Z] error: <service-helpers> Error getting services from Docker server 'showStats': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:46.219Z] error: <service-helpers> Error getting services from Docker server 'showLogs': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:46.219Z] error: <service-helpers> Error getting services from Docker server 'labelFilter': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:46.220Z] error: <service-helpers> Error getting services from Docker server 'refreshInterval': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:46.220Z] error: <service-helpers> Error getting services from Docker server 'readOnly': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:46.220Z] error: <service-helpers> Error getting services from Docker server 'timeout': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:46.221Z] error: <service-helpers> Error getting services from Docker server 'cache': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:46.221Z] error: <service-helpers> Error getting services from Docker server 'logs': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:46.221Z] error: <service-helpers> Error getting services from Docker server 'stats': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:46.222Z] error: <service-helpers> Error getting services from Docker server 'events': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:46.222Z] error: <service-helpers> Error getting services from Docker server 'images': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:46.223Z] error: <service-helpers> Error getting services from Docker server 'networks': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:46.224Z] error: <service-helpers> Error getting services from Docker server 'volumes': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:46.650Z] error: <service-helpers> Error getting services from Docker server 'showStopped': TypeError: Cannot read properties of null (reading 'conn')
    at <unknown> (.next/server/chunks/7325.js:1:5936)
    at Array.map (<anonymous>)
    at v (.next/server/chunks/7325.js:1:5878)
    at async h (.next/server/pages/api/bookmarks.js:1:2633)
    at async n (.next/server/pages/api/services.js:1:5141)
[2025-08-01T10:10:46.722Z] error: <service-helpers> Error getting services from Docker server 'jellyfin': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:46.723Z] error: <service-helpers> Error getting services from Docker server 'jellyseerr': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:46.723Z] error: <service-helpers> Error getting services from Docker server 'qbittorrent': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:46.724Z] error: <service-helpers> Error getting services from Docker server 'adguard': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:46.724Z] error: <service-helpers> Error getting services from Docker server 'sonarr': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:46.724Z] error: <service-helpers> Error getting services from Docker server 'radarr': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:46.725Z] error: <service-helpers> Error getting services from Docker server 'jackett': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:46.725Z] error: <service-helpers> Error getting services from Docker server 'flaresolverr': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:46.726Z] error: <service-helpers> Error getting services from Docker server 'portainer': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:46.726Z] error: <service-helpers> Error getting services from Docker server 'caddy_proxy': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:46.726Z] error: <service-helpers> Error getting services from Docker server 'watchtower': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:46.727Z] error: <service-helpers> Error getting services from Docker server 'homepage': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:46.727Z] error: <service-helpers> Error getting services from Docker server 'duckdns_jucago705': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:46.728Z] error: <service-helpers> Error getting services from Docker server 'duckdns_jucago706': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:46.728Z] error: <service-helpers> Error getting services from Docker server 'telegram-bot': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:46.728Z] error: <service-helpers> Error getting services from Docker server 'media-stack': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:46.729Z] error: <service-helpers> Error getting services from Docker server 'arr-stack': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:46.729Z] error: <service-helpers> Error getting services from Docker server 'system-stack': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:46.729Z] error: <service-helpers> Error getting services from Docker server 'infrastructure-stack': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:46.730Z] error: <service-helpers> Error getting services from Docker server 'showStats': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:46.730Z] error: <service-helpers> Error getting services from Docker server 'showLogs': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:46.731Z] error: <service-helpers> Error getting services from Docker server 'labelFilter': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:46.731Z] error: <service-helpers> Error getting services from Docker server 'refreshInterval': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:46.732Z] error: <service-helpers> Error getting services from Docker server 'readOnly': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:46.733Z] error: <service-helpers> Error getting services from Docker server 'timeout': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:46.733Z] error: <service-helpers> Error getting services from Docker server 'cache': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:46.734Z] error: <service-helpers> Error getting services from Docker server 'logs': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:46.734Z] error: <service-helpers> Error getting services from Docker server 'stats': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:46.735Z] error: <service-helpers> Error getting services from Docker server 'events': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:46.735Z] error: <service-helpers> Error getting services from Docker server 'images': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:46.736Z] error: <service-helpers> Error getting services from Docker server 'networks': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:46.736Z] error: <service-helpers> Error getting services from Docker server 'volumes': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:55.860Z] error: <service-helpers> Error getting services from Docker server 'showStopped': TypeError: Cannot read properties of null (reading 'conn')
    at <unknown> (.next/server/chunks/7325.js:1:5936)
    at Array.map (<anonymous>)
    at v (.next/server/chunks/7325.js:1:5878)
    at async h (.next/server/pages/api/bookmarks.js:1:2633)
    at async n (.next/server/pages/api/services.js:1:5141)
[2025-08-01T10:10:56.026Z] error: <service-helpers> Error getting services from Docker server 'jellyfin': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:56.027Z] error: <service-helpers> Error getting services from Docker server 'jellyseerr': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:56.027Z] error: <service-helpers> Error getting services from Docker server 'qbittorrent': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:56.027Z] error: <service-helpers> Error getting services from Docker server 'adguard': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:56.028Z] error: <service-helpers> Error getting services from Docker server 'sonarr': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:56.028Z] error: <service-helpers> Error getting services from Docker server 'radarr': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:56.028Z] error: <service-helpers> Error getting services from Docker server 'jackett': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:56.029Z] error: <service-helpers> Error getting services from Docker server 'flaresolverr': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:56.030Z] error: <service-helpers> Error getting services from Docker server 'portainer': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:56.031Z] error: <service-helpers> Error getting services from Docker server 'caddy_proxy': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:56.031Z] error: <service-helpers> Error getting services from Docker server 'watchtower': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:56.032Z] error: <service-helpers> Error getting services from Docker server 'homepage': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:56.032Z] error: <service-helpers> Error getting services from Docker server 'duckdns_jucago705': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:56.032Z] error: <service-helpers> Error getting services from Docker server 'duckdns_jucago706': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:56.033Z] error: <service-helpers> Error getting services from Docker server 'telegram-bot': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:56.033Z] error: <service-helpers> Error getting services from Docker server 'media-stack': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:56.033Z] error: <service-helpers> Error getting services from Docker server 'arr-stack': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:56.035Z] error: <service-helpers> Error getting services from Docker server 'system-stack': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:56.036Z] error: <service-helpers> Error getting services from Docker server 'infrastructure-stack': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:56.037Z] error: <service-helpers> Error getting services from Docker server 'showStats': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:56.037Z] error: <service-helpers> Error getting services from Docker server 'showLogs': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:56.038Z] error: <service-helpers> Error getting services from Docker server 'labelFilter': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:56.038Z] error: <service-helpers> Error getting services from Docker server 'refreshInterval': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:56.038Z] error: <service-helpers> Error getting services from Docker server 'readOnly': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:56.039Z] error: <service-helpers> Error getting services from Docker server 'timeout': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:56.039Z] error: <service-helpers> Error getting services from Docker server 'cache': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:56.039Z] error: <service-helpers> Error getting services from Docker server 'logs': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:56.040Z] error: <service-helpers> Error getting services from Docker server 'stats': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:56.040Z] error: <service-helpers> Error getting services from Docker server 'events': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:56.041Z] error: <service-helpers> Error getting services from Docker server 'images': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:56.041Z] error: <service-helpers> Error getting services from Docker server 'networks': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:10:56.042Z] error: <service-helpers> Error getting services from Docker server 'volumes': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:02.280Z] error: <service-helpers> Error getting services from Docker server 'showStopped': TypeError: Cannot read properties of null (reading 'conn')
    at <unknown> (.next/server/chunks/7325.js:1:5936)
    at Array.map (<anonymous>)
    at v (.next/server/chunks/7325.js:1:5878)
    at async h (.next/server/pages/api/bookmarks.js:1:2633)
    at async n (.next/server/pages/api/services.js:1:5141)
[2025-08-01T10:11:02.367Z] error: <service-helpers> Error getting services from Docker server 'jellyfin': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:02.368Z] error: <service-helpers> Error getting services from Docker server 'jellyseerr': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:02.368Z] error: <service-helpers> Error getting services from Docker server 'qbittorrent': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:02.369Z] error: <service-helpers> Error getting services from Docker server 'adguard': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:02.369Z] error: <service-helpers> Error getting services from Docker server 'sonarr': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:02.370Z] error: <service-helpers> Error getting services from Docker server 'radarr': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:02.370Z] error: <service-helpers> Error getting services from Docker server 'jackett': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:02.371Z] error: <service-helpers> Error getting services from Docker server 'flaresolverr': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:02.371Z] error: <service-helpers> Error getting services from Docker server 'portainer': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:02.371Z] error: <service-helpers> Error getting services from Docker server 'caddy_proxy': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:02.372Z] error: <service-helpers> Error getting services from Docker server 'watchtower': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:02.372Z] error: <service-helpers> Error getting services from Docker server 'homepage': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:02.372Z] error: <service-helpers> Error getting services from Docker server 'duckdns_jucago705': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:02.373Z] error: <service-helpers> Error getting services from Docker server 'duckdns_jucago706': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:02.373Z] error: <service-helpers> Error getting services from Docker server 'telegram-bot': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:02.374Z] error: <service-helpers> Error getting services from Docker server 'media-stack': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:02.375Z] error: <service-helpers> Error getting services from Docker server 'arr-stack': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:02.375Z] error: <service-helpers> Error getting services from Docker server 'system-stack': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:02.376Z] error: <service-helpers> Error getting services from Docker server 'infrastructure-stack': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:02.376Z] error: <service-helpers> Error getting services from Docker server 'showStats': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:02.376Z] error: <service-helpers> Error getting services from Docker server 'showLogs': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:02.377Z] error: <service-helpers> Error getting services from Docker server 'labelFilter': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:02.377Z] error: <service-helpers> Error getting services from Docker server 'refreshInterval': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:02.378Z] error: <service-helpers> Error getting services from Docker server 'readOnly': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:02.378Z] error: <service-helpers> Error getting services from Docker server 'timeout': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:02.378Z] error: <service-helpers> Error getting services from Docker server 'cache': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:02.379Z] error: <service-helpers> Error getting services from Docker server 'logs': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:02.379Z] error: <service-helpers> Error getting services from Docker server 'stats': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:02.379Z] error: <service-helpers> Error getting services from Docker server 'events': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:02.380Z] error: <service-helpers> Error getting services from Docker server 'images': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:02.380Z] error: <service-helpers> Error getting services from Docker server 'networks': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:02.381Z] error: <service-helpers> Error getting services from Docker server 'volumes': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:23.746Z] error: <service-helpers> Error getting services from Docker server 'showStopped': TypeError: Cannot read properties of null (reading 'conn')
    at <unknown> (.next/server/pages/index.js:12:60322)
    at Array.map (<anonymous>)
    at v (.next/server/pages/index.js:12:60264)
    at async g (.next/server/pages/index.js:1:15658)
    at async L (.next/server/pages/index.js:7:54090)
[2025-08-01T10:11:23.809Z] error: <service-helpers> Error getting services from Docker server 'jellyfin': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:23.810Z] error: <service-helpers> Error getting services from Docker server 'jellyseerr': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:23.811Z] error: <service-helpers> Error getting services from Docker server 'qbittorrent': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:23.812Z] error: <service-helpers> Error getting services from Docker server 'adguard': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:23.812Z] error: <service-helpers> Error getting services from Docker server 'sonarr': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:23.813Z] error: <service-helpers> Error getting services from Docker server 'radarr': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:23.813Z] error: <service-helpers> Error getting services from Docker server 'jackett': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:23.814Z] error: <service-helpers> Error getting services from Docker server 'flaresolverr': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:23.814Z] error: <service-helpers> Error getting services from Docker server 'portainer': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:23.815Z] error: <service-helpers> Error getting services from Docker server 'caddy_proxy': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:23.815Z] error: <service-helpers> Error getting services from Docker server 'watchtower': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:23.816Z] error: <service-helpers> Error getting services from Docker server 'homepage': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:23.816Z] error: <service-helpers> Error getting services from Docker server 'duckdns_jucago705': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:23.817Z] error: <service-helpers> Error getting services from Docker server 'duckdns_jucago706': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:23.817Z] error: <service-helpers> Error getting services from Docker server 'telegram-bot': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:23.817Z] error: <service-helpers> Error getting services from Docker server 'media-stack': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:23.818Z] error: <service-helpers> Error getting services from Docker server 'arr-stack': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:23.818Z] error: <service-helpers> Error getting services from Docker server 'system-stack': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:23.818Z] error: <service-helpers> Error getting services from Docker server 'infrastructure-stack': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:23.819Z] error: <service-helpers> Error getting services from Docker server 'showStats': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:23.819Z] error: <service-helpers> Error getting services from Docker server 'showLogs': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:23.820Z] error: <service-helpers> Error getting services from Docker server 'labelFilter': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:23.820Z] error: <service-helpers> Error getting services from Docker server 'refreshInterval': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:23.820Z] error: <service-helpers> Error getting services from Docker server 'readOnly': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:23.821Z] error: <service-helpers> Error getting services from Docker server 'timeout': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:23.821Z] error: <service-helpers> Error getting services from Docker server 'cache': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:23.821Z] error: <service-helpers> Error getting services from Docker server 'logs': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:23.822Z] error: <service-helpers> Error getting services from Docker server 'stats': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:23.822Z] error: <service-helpers> Error getting services from Docker server 'events': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:23.822Z] error: <service-helpers> Error getting services from Docker server 'images': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:23.823Z] error: <service-helpers> Error getting services from Docker server 'networks': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:23.823Z] error: <service-helpers> Error getting services from Docker server 'volumes': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:27.512Z] error: <service-helpers> Error getting services from Docker server 'showStopped': TypeError: Cannot read properties of null (reading 'conn')
    at <unknown> (.next/server/chunks/7325.js:1:5936)
    at Array.map (<anonymous>)
    at v (.next/server/chunks/7325.js:1:5878)
    at async h (.next/server/pages/api/bookmarks.js:1:2633)
    at async n (.next/server/pages/api/services.js:1:5141)
[2025-08-01T10:11:27.574Z] error: <service-helpers> Error getting services from Docker server 'jellyfin': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:27.574Z] error: <service-helpers> Error getting services from Docker server 'jellyseerr': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:27.575Z] error: <service-helpers> Error getting services from Docker server 'qbittorrent': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:27.575Z] error: <service-helpers> Error getting services from Docker server 'adguard': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:27.576Z] error: <service-helpers> Error getting services from Docker server 'sonarr': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:27.576Z] error: <service-helpers> Error getting services from Docker server 'radarr': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:27.576Z] error: <service-helpers> Error getting services from Docker server 'jackett': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:27.577Z] error: <service-helpers> Error getting services from Docker server 'flaresolverr': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:27.577Z] error: <service-helpers> Error getting services from Docker server 'portainer': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:27.578Z] error: <service-helpers> Error getting services from Docker server 'caddy_proxy': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:27.578Z] error: <service-helpers> Error getting services from Docker server 'watchtower': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:27.579Z] error: <service-helpers> Error getting services from Docker server 'homepage': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:27.579Z] error: <service-helpers> Error getting services from Docker server 'duckdns_jucago705': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:27.579Z] error: <service-helpers> Error getting services from Docker server 'duckdns_jucago706': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:27.580Z] error: <service-helpers> Error getting services from Docker server 'telegram-bot': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:27.580Z] error: <service-helpers> Error getting services from Docker server 'media-stack': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:27.581Z] error: <service-helpers> Error getting services from Docker server 'arr-stack': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:27.581Z] error: <service-helpers> Error getting services from Docker server 'system-stack': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:27.582Z] error: <service-helpers> Error getting services from Docker server 'infrastructure-stack': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:27.583Z] error: <service-helpers> Error getting services from Docker server 'showStats': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:27.584Z] error: <service-helpers> Error getting services from Docker server 'showLogs': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:27.584Z] error: <service-helpers> Error getting services from Docker server 'labelFilter': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:27.585Z] error: <service-helpers> Error getting services from Docker server 'refreshInterval': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:27.585Z] error: <service-helpers> Error getting services from Docker server 'readOnly': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:27.585Z] error: <service-helpers> Error getting services from Docker server 'timeout': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:27.586Z] error: <service-helpers> Error getting services from Docker server 'cache': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:27.586Z] error: <service-helpers> Error getting services from Docker server 'logs': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:27.586Z] error: <service-helpers> Error getting services from Docker server 'stats': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:27.586Z] error: <service-helpers> Error getting services from Docker server 'events': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:27.587Z] error: <service-helpers> Error getting services from Docker server 'images': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:27.587Z] error: <service-helpers> Error getting services from Docker server 'networks': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:27.587Z] error: <service-helpers> Error getting services from Docker server 'volumes': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:35.670Z] error: <service-helpers> Error getting services from Docker server 'showStopped': TypeError: Cannot read properties of null (reading 'conn')
    at <unknown> (.next/server/chunks/7325.js:1:5936)
    at Array.map (<anonymous>)
    at v (.next/server/chunks/7325.js:1:5878)
    at async h (.next/server/pages/api/bookmarks.js:1:2633)
    at async n (.next/server/pages/api/services.js:1:5141)
[2025-08-01T10:11:35.762Z] error: <service-helpers> Error getting services from Docker server 'jellyfin': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:35.762Z] error: <service-helpers> Error getting services from Docker server 'jellyseerr': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:35.763Z] error: <service-helpers> Error getting services from Docker server 'qbittorrent': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:35.763Z] error: <service-helpers> Error getting services from Docker server 'adguard': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:35.768Z] error: <service-helpers> Error getting services from Docker server 'sonarr': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:35.768Z] error: <service-helpers> Error getting services from Docker server 'radarr': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:35.769Z] error: <service-helpers> Error getting services from Docker server 'jackett': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:35.772Z] error: <service-helpers> Error getting services from Docker server 'flaresolverr': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:35.773Z] error: <service-helpers> Error getting services from Docker server 'portainer': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:35.775Z] error: <service-helpers> Error getting services from Docker server 'caddy_proxy': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:35.776Z] error: <service-helpers> Error getting services from Docker server 'watchtower': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:35.776Z] error: <service-helpers> Error getting services from Docker server 'homepage': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:35.780Z] error: <service-helpers> Error getting services from Docker server 'duckdns_jucago705': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:35.781Z] error: <service-helpers> Error getting services from Docker server 'duckdns_jucago706': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:35.782Z] error: <service-helpers> Error getting services from Docker server 'telegram-bot': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:35.782Z] error: <service-helpers> Error getting services from Docker server 'media-stack': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:35.783Z] error: <service-helpers> Error getting services from Docker server 'arr-stack': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:35.783Z] error: <service-helpers> Error getting services from Docker server 'system-stack': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:35.783Z] error: <service-helpers> Error getting services from Docker server 'infrastructure-stack': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:35.784Z] error: <service-helpers> Error getting services from Docker server 'showStats': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:35.784Z] error: <service-helpers> Error getting services from Docker server 'showLogs': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:35.787Z] error: <service-helpers> Error getting services from Docker server 'labelFilter': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:35.788Z] error: <service-helpers> Error getting services from Docker server 'refreshInterval': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:35.788Z] error: <service-helpers> Error getting services from Docker server 'readOnly': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:35.789Z] error: <service-helpers> Error getting services from Docker server 'timeout': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:35.789Z] error: <service-helpers> Error getting services from Docker server 'cache': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:35.791Z] error: <service-helpers> Error getting services from Docker server 'logs': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:35.793Z] error: <service-helpers> Error getting services from Docker server 'stats': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:35.794Z] error: <service-helpers> Error getting services from Docker server 'events': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:35.795Z] error: <service-helpers> Error getting services from Docker server 'images': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:35.797Z] error: <service-helpers> Error getting services from Docker server 'networks': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:35.797Z] error: <service-helpers> Error getting services from Docker server 'volumes': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:40.931Z] error: <service-helpers> Error getting services from Docker server 'showStopped': TypeError: Cannot read properties of null (reading 'conn')
    at <unknown> (.next/server/chunks/7325.js:1:5936)
    at Array.map (<anonymous>)
    at v (.next/server/chunks/7325.js:1:5878)
    at async h (.next/server/pages/api/bookmarks.js:1:2633)
    at async n (.next/server/pages/api/services.js:1:5141)
[2025-08-01T10:11:41.027Z] error: <service-helpers> Error getting services from Docker server 'jellyfin': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:41.029Z] error: <service-helpers> Error getting services from Docker server 'jellyseerr': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:41.030Z] error: <service-helpers> Error getting services from Docker server 'qbittorrent': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:41.032Z] error: <service-helpers> Error getting services from Docker server 'adguard': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:41.033Z] error: <service-helpers> Error getting services from Docker server 'sonarr': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:41.037Z] error: <service-helpers> Error getting services from Docker server 'radarr': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:41.037Z] error: <service-helpers> Error getting services from Docker server 'jackett': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:41.038Z] error: <service-helpers> Error getting services from Docker server 'flaresolverr': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:41.039Z] error: <service-helpers> Error getting services from Docker server 'portainer': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:41.040Z] error: <service-helpers> Error getting services from Docker server 'caddy_proxy': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:41.042Z] error: <service-helpers> Error getting services from Docker server 'watchtower': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:41.043Z] error: <service-helpers> Error getting services from Docker server 'homepage': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:41.043Z] error: <service-helpers> Error getting services from Docker server 'duckdns_jucago705': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:41.044Z] error: <service-helpers> Error getting services from Docker server 'duckdns_jucago706': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:41.045Z] error: <service-helpers> Error getting services from Docker server 'telegram-bot': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:41.046Z] error: <service-helpers> Error getting services from Docker server 'media-stack': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:41.049Z] error: <service-helpers> Error getting services from Docker server 'arr-stack': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:41.050Z] error: <service-helpers> Error getting services from Docker server 'system-stack': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:41.051Z] error: <service-helpers> Error getting services from Docker server 'infrastructure-stack': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:41.052Z] error: <service-helpers> Error getting services from Docker server 'showStats': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:41.053Z] error: <service-helpers> Error getting services from Docker server 'showLogs': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:41.054Z] error: <service-helpers> Error getting services from Docker server 'labelFilter': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:41.055Z] error: <service-helpers> Error getting services from Docker server 'refreshInterval': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:41.056Z] error: <service-helpers> Error getting services from Docker server 'readOnly': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:41.056Z] error: <service-helpers> Error getting services from Docker server 'timeout': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:41.057Z] error: <service-helpers> Error getting services from Docker server 'cache': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:41.057Z] error: <service-helpers> Error getting services from Docker server 'logs': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:41.057Z] error: <service-helpers> Error getting services from Docker server 'stats': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:41.059Z] error: <service-helpers> Error getting services from Docker server 'images': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:41.059Z] error: <service-helpers> Error getting services from Docker server 'events': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:41.060Z] error: <service-helpers> Error getting services from Docker server 'networks': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:41.060Z] error: <service-helpers> Error getting services from Docker server 'volumes': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:59.816Z] error: <service-helpers> Error getting services from Docker server 'showStopped': TypeError: Cannot read properties of null (reading 'conn')
    at <unknown> (.next/server/chunks/7325.js:1:5936)
    at Array.map (<anonymous>)
    at v (.next/server/chunks/7325.js:1:5878)
    at async h (.next/server/pages/api/bookmarks.js:1:2633)
    at async n (.next/server/pages/api/services.js:1:5141)
[2025-08-01T10:11:59.886Z] error: <service-helpers> Error getting services from Docker server 'jellyfin': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:59.887Z] error: <service-helpers> Error getting services from Docker server 'jellyseerr': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:59.887Z] error: <service-helpers> Error getting services from Docker server 'qbittorrent': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:59.888Z] error: <service-helpers> Error getting services from Docker server 'adguard': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:59.888Z] error: <service-helpers> Error getting services from Docker server 'sonarr': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:59.889Z] error: <service-helpers> Error getting services from Docker server 'radarr': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:59.890Z] error: <service-helpers> Error getting services from Docker server 'jackett': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:59.890Z] error: <service-helpers> Error getting services from Docker server 'flaresolverr': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:59.891Z] error: <service-helpers> Error getting services from Docker server 'portainer': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:59.891Z] error: <service-helpers> Error getting services from Docker server 'caddy_proxy': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:59.892Z] error: <service-helpers> Error getting services from Docker server 'watchtower': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:59.892Z] error: <service-helpers> Error getting services from Docker server 'homepage': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:59.892Z] error: <service-helpers> Error getting services from Docker server 'duckdns_jucago705': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:59.893Z] error: <service-helpers> Error getting services from Docker server 'duckdns_jucago706': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:59.893Z] error: <service-helpers> Error getting services from Docker server 'telegram-bot': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:59.894Z] error: <service-helpers> Error getting services from Docker server 'media-stack': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:59.895Z] error: <service-helpers> Error getting services from Docker server 'arr-stack': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:59.896Z] error: <service-helpers> Error getting services from Docker server 'system-stack': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:59.897Z] error: <service-helpers> Error getting services from Docker server 'infrastructure-stack': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:59.897Z] error: <service-helpers> Error getting services from Docker server 'showStats': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:59.898Z] error: <service-helpers> Error getting services from Docker server 'showLogs': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:59.899Z] error: <service-helpers> Error getting services from Docker server 'labelFilter': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:59.899Z] error: <service-helpers> Error getting services from Docker server 'refreshInterval': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:59.900Z] error: <service-helpers> Error getting services from Docker server 'readOnly': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:59.901Z] error: <service-helpers> Error getting services from Docker server 'timeout': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:59.901Z] error: <service-helpers> Error getting services from Docker server 'cache': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:59.902Z] error: <service-helpers> Error getting services from Docker server 'logs': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:59.902Z] error: <service-helpers> Error getting services from Docker server 'stats': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:59.902Z] error: <service-helpers> Error getting services from Docker server 'events': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:59.904Z] error: <service-helpers> Error getting services from Docker server 'images': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:59.904Z] error: <service-helpers> Error getting services from Docker server 'networks': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:11:59.904Z] error: <service-helpers> Error getting services from Docker server 'volumes': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:12:22.581Z] error: <service-helpers> Error getting services from Docker server 'showStopped': TypeError: Cannot read properties of null (reading 'conn')
    at <unknown> (.next/server/chunks/7325.js:1:5936)
    at Array.map (<anonymous>)
    at v (.next/server/chunks/7325.js:1:5878)
    at async h (.next/server/pages/api/bookmarks.js:1:2633)
    at async n (.next/server/pages/api/services.js:1:5141)
[2025-08-01T10:12:22.655Z] error: <service-helpers> Error getting services from Docker server 'jellyfin': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:12:22.656Z] error: <service-helpers> Error getting services from Docker server 'jellyseerr': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:12:22.656Z] error: <service-helpers> Error getting services from Docker server 'qbittorrent': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:12:22.657Z] error: <service-helpers> Error getting services from Docker server 'adguard': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:12:22.657Z] error: <service-helpers> Error getting services from Docker server 'sonarr': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:12:22.657Z] error: <service-helpers> Error getting services from Docker server 'radarr': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:12:22.658Z] error: <service-helpers> Error getting services from Docker server 'jackett': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:12:22.658Z] error: <service-helpers> Error getting services from Docker server 'flaresolverr': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:12:22.658Z] error: <service-helpers> Error getting services from Docker server 'portainer': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:12:22.659Z] error: <service-helpers> Error getting services from Docker server 'caddy_proxy': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:12:22.659Z] error: <service-helpers> Error getting services from Docker server 'watchtower': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:12:22.659Z] error: <service-helpers> Error getting services from Docker server 'homepage': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:12:22.659Z] error: <service-helpers> Error getting services from Docker server 'duckdns_jucago705': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:12:22.660Z] error: <service-helpers> Error getting services from Docker server 'duckdns_jucago706': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:12:22.660Z] error: <service-helpers> Error getting services from Docker server 'telegram-bot': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:12:22.661Z] error: <service-helpers> Error getting services from Docker server 'media-stack': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:12:22.661Z] error: <service-helpers> Error getting services from Docker server 'arr-stack': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:12:22.661Z] error: <service-helpers> Error getting services from Docker server 'system-stack': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:12:22.662Z] error: <service-helpers> Error getting services from Docker server 'infrastructure-stack': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:12:22.662Z] error: <service-helpers> Error getting services from Docker server 'showStats': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:12:22.663Z] error: <service-helpers> Error getting services from Docker server 'showLogs': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:12:22.663Z] error: <service-helpers> Error getting services from Docker server 'labelFilter': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:12:22.663Z] error: <service-helpers> Error getting services from Docker server 'refreshInterval': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:12:22.664Z] error: <service-helpers> Error getting services from Docker server 'readOnly': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:12:22.665Z] error: <service-helpers> Error getting services from Docker server 'timeout': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:12:22.665Z] error: <service-helpers> Error getting services from Docker server 'cache': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:12:22.666Z] error: <service-helpers> Error getting services from Docker server 'logs': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:12:22.666Z] error: <service-helpers> Error getting services from Docker server 'stats': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:12:22.666Z] error: <service-helpers> Error getting services from Docker server 'events': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:12:22.667Z] error: <service-helpers> Error getting services from Docker server 'images': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:12:22.667Z] error: <service-helpers> Error getting services from Docker server 'networks': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:12:22.668Z] error: <service-helpers> Error getting services from Docker server 'volumes': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:12:33.988Z] error: <service-helpers> Error getting services from Docker server 'showStopped': TypeError: Cannot read properties of null (reading 'conn')
    at <unknown> (.next/server/chunks/7325.js:1:5936)
    at Array.map (<anonymous>)
    at v (.next/server/chunks/7325.js:1:5878)
    at async h (.next/server/pages/api/bookmarks.js:1:2633)
    at async n (.next/server/pages/api/services.js:1:5141)
[2025-08-01T10:12:34.118Z] error: <service-helpers> Error getting services from Docker server 'jellyfin': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:12:34.120Z] error: <service-helpers> Error getting services from Docker server 'jellyseerr': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:12:34.121Z] error: <service-helpers> Error getting services from Docker server 'qbittorrent': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:12:34.125Z] error: <service-helpers> Error getting services from Docker server 'adguard': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:12:34.126Z] error: <service-helpers> Error getting services from Docker server 'sonarr': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:12:34.126Z] error: <service-helpers> Error getting services from Docker server 'radarr': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:12:34.127Z] error: <service-helpers> Error getting services from Docker server 'jackett': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:12:34.128Z] error: <service-helpers> Error getting services from Docker server 'flaresolverr': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:12:34.129Z] error: <service-helpers> Error getting services from Docker server 'portainer': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:12:34.130Z] error: <service-helpers> Error getting services from Docker server 'caddy_proxy': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:12:34.130Z] error: <service-helpers> Error getting services from Docker server 'watchtower': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:12:34.131Z] error: <service-helpers> Error getting services from Docker server 'homepage': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:12:34.134Z] error: <service-helpers> Error getting services from Docker server 'duckdns_jucago705': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:12:34.135Z] error: <service-helpers> Error getting services from Docker server 'duckdns_jucago706': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:12:34.135Z] error: <service-helpers> Error getting services from Docker server 'telegram-bot': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:12:34.136Z] error: <service-helpers> Error getting services from Docker server 'media-stack': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:12:34.136Z] error: <service-helpers> Error getting services from Docker server 'arr-stack': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:12:34.137Z] error: <service-helpers> Error getting services from Docker server 'system-stack': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:12:34.137Z] error: <service-helpers> Error getting services from Docker server 'infrastructure-stack': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:12:34.138Z] error: <service-helpers> Error getting services from Docker server 'showStats': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:12:34.138Z] error: <service-helpers> Error getting services from Docker server 'showLogs': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:12:34.139Z] error: <service-helpers> Error getting services from Docker server 'labelFilter': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:12:34.139Z] error: <service-helpers> Error getting services from Docker server 'refreshInterval': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:12:34.140Z] error: <service-helpers> Error getting services from Docker server 'readOnly': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:12:34.140Z] error: <service-helpers> Error getting services from Docker server 'timeout': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:12:34.141Z] error: <service-helpers> Error getting services from Docker server 'cache': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:12:34.141Z] error: <service-helpers> Error getting services from Docker server 'logs': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:12:34.141Z] error: <service-helpers> Error getting services from Docker server 'stats': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:12:34.142Z] error: <service-helpers> Error getting services from Docker server 'events': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:12:34.142Z] error: <service-helpers> Error getting services from Docker server 'images': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:12:34.142Z] error: <service-helpers> Error getting services from Docker server 'networks': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:12:34.146Z] error: <service-helpers> Error getting services from Docker server 'volumes': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:13:12.554Z] error: <service-helpers> Error getting services from Docker server 'showStopped': TypeError: Cannot read properties of null (reading 'conn')
    at <unknown> (.next/server/chunks/7325.js:1:5936)
    at Array.map (<anonymous>)
    at v (.next/server/chunks/7325.js:1:5878)
    at async h (.next/server/pages/api/bookmarks.js:1:2633)
    at async n (.next/server/pages/api/services.js:1:5141)
[2025-08-01T10:13:12.650Z] error: <service-helpers> Error getting services from Docker server 'jellyfin': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:13:12.651Z] error: <service-helpers> Error getting services from Docker server 'jellyseerr': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:13:12.651Z] error: <service-helpers> Error getting services from Docker server 'qbittorrent': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:13:12.652Z] error: <service-helpers> Error getting services from Docker server 'adguard': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:13:12.652Z] error: <service-helpers> Error getting services from Docker server 'sonarr': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:13:12.652Z] error: <service-helpers> Error getting services from Docker server 'radarr': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:13:12.653Z] error: <service-helpers> Error getting services from Docker server 'jackett': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:13:12.653Z] error: <service-helpers> Error getting services from Docker server 'flaresolverr': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:13:12.653Z] error: <service-helpers> Error getting services from Docker server 'portainer': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:13:12.654Z] error: <service-helpers> Error getting services from Docker server 'caddy_proxy': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:13:12.654Z] error: <service-helpers> Error getting services from Docker server 'watchtower': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:13:12.654Z] error: <service-helpers> Error getting services from Docker server 'homepage': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:13:12.655Z] error: <service-helpers> Error getting services from Docker server 'duckdns_jucago705': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:13:12.655Z] error: <service-helpers> Error getting services from Docker server 'duckdns_jucago706': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:13:12.655Z] error: <service-helpers> Error getting services from Docker server 'telegram-bot': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:13:12.656Z] error: <service-helpers> Error getting services from Docker server 'media-stack': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:13:12.656Z] error: <service-helpers> Error getting services from Docker server 'arr-stack': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:13:12.656Z] error: <service-helpers> Error getting services from Docker server 'system-stack': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:13:12.657Z] error: <service-helpers> Error getting services from Docker server 'infrastructure-stack': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:13:12.657Z] error: <service-helpers> Error getting services from Docker server 'showStats': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:13:12.658Z] error: <service-helpers> Error getting services from Docker server 'showLogs': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:13:12.658Z] error: <service-helpers> Error getting services from Docker server 'labelFilter': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:13:12.659Z] error: <service-helpers> Error getting services from Docker server 'refreshInterval': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:13:12.659Z] error: <service-helpers> Error getting services from Docker server 'readOnly': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:13:12.660Z] error: <service-helpers> Error getting services from Docker server 'timeout': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:13:12.661Z] error: <service-helpers> Error getting services from Docker server 'cache': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:13:12.661Z] error: <service-helpers> Error getting services from Docker server 'logs': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:13:12.662Z] error: <service-helpers> Error getting services from Docker server 'stats': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:13:12.662Z] error: <service-helpers> Error getting services from Docker server 'events': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:13:12.663Z] error: <service-helpers> Error getting services from Docker server 'images': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:13:12.663Z] error: <service-helpers> Error getting services from Docker server 'networks': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:13:12.663Z] error: <service-helpers> Error getting services from Docker server 'volumes': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:13:12.785Z] error: <service-helpers> Error getting services from Docker server 'showStopped': TypeError: Cannot read properties of null (reading 'conn')
    at <unknown> (.next/server/pages/index.js:12:60322)
    at Array.map (<anonymous>)
    at v (.next/server/pages/index.js:12:60264)
    at async g (.next/server/pages/index.js:1:15658)
    at async L (.next/server/pages/index.js:7:54090)
[2025-08-01T10:13:12.846Z] error: <service-helpers> Error getting services from Docker server 'jellyfin': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:13:12.847Z] error: <service-helpers> Error getting services from Docker server 'jellyseerr': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:13:12.848Z] error: <service-helpers> Error getting services from Docker server 'qbittorrent': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:13:12.848Z] error: <service-helpers> Error getting services from Docker server 'adguard': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:13:12.848Z] error: <service-helpers> Error getting services from Docker server 'sonarr': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:13:12.849Z] error: <service-helpers> Error getting services from Docker server 'radarr': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:13:12.849Z] error: <service-helpers> Error getting services from Docker server 'jackett': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:13:12.849Z] error: <service-helpers> Error getting services from Docker server 'flaresolverr': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:13:12.850Z] error: <service-helpers> Error getting services from Docker server 'portainer': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:13:12.850Z] error: <service-helpers> Error getting services from Docker server 'caddy_proxy': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:13:12.850Z] error: <service-helpers> Error getting services from Docker server 'watchtower': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:13:12.851Z] error: <service-helpers> Error getting services from Docker server 'homepage': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:13:12.851Z] error: <service-helpers> Error getting services from Docker server 'duckdns_jucago705': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:13:12.851Z] error: <service-helpers> Error getting services from Docker server 'duckdns_jucago706': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:13:12.852Z] error: <service-helpers> Error getting services from Docker server 'telegram-bot': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:13:12.852Z] error: <service-helpers> Error getting services from Docker server 'media-stack': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:13:12.852Z] error: <service-helpers> Error getting services from Docker server 'arr-stack': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:13:12.853Z] error: <service-helpers> Error getting services from Docker server 'system-stack': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:13:12.853Z] error: <service-helpers> Error getting services from Docker server 'infrastructure-stack': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:13:12.854Z] error: <service-helpers> Error getting services from Docker server 'showStats': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:13:12.854Z] error: <service-helpers> Error getting services from Docker server 'showLogs': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:13:12.854Z] error: <service-helpers> Error getting services from Docker server 'labelFilter': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:13:12.855Z] error: <service-helpers> Error getting services from Docker server 'refreshInterval': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:13:12.855Z] error: <service-helpers> Error getting services from Docker server 'readOnly': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:13:12.855Z] error: <service-helpers> Error getting services from Docker server 'timeout': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:13:12.856Z] error: <service-helpers> Error getting services from Docker server 'cache': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:13:12.856Z] error: <service-helpers> Error getting services from Docker server 'logs': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:13:12.856Z] error: <service-helpers> Error getting services from Docker server 'stats': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:13:12.857Z] error: <service-helpers> Error getting services from Docker server 'events': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:13:12.857Z] error: <service-helpers> Error getting services from Docker server 'images': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:13:12.857Z] error: <service-helpers> Error getting services from Docker server 'networks': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:13:12.858Z] error: <service-helpers> Error getting services from Docker server 'volumes': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:13:13.292Z] error: <service-helpers> Error getting services from Docker server 'showStopped': TypeError: Cannot read properties of null (reading 'conn')
    at <unknown> (.next/server/chunks/7325.js:1:5936)
    at Array.map (<anonymous>)
    at v (.next/server/chunks/7325.js:1:5878)
    at async h (.next/server/pages/api/bookmarks.js:1:2633)
    at async n (.next/server/pages/api/services.js:1:5141)
[2025-08-01T10:13:13.366Z] error: <service-helpers> Error getting services from Docker server 'jellyfin': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:13:13.367Z] error: <service-helpers> Error getting services from Docker server 'jellyseerr': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:13:13.367Z] error: <service-helpers> Error getting services from Docker server 'qbittorrent': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:13:13.368Z] error: <service-helpers> Error getting services from Docker server 'adguard': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:13:13.368Z] error: <service-helpers> Error getting services from Docker server 'sonarr': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:13:13.369Z] error: <service-helpers> Error getting services from Docker server 'radarr': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:13:13.369Z] error: <service-helpers> Error getting services from Docker server 'jackett': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:13:13.369Z] error: <service-helpers> Error getting services from Docker server 'flaresolverr': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:13:13.370Z] error: <service-helpers> Error getting services from Docker server 'portainer': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:13:13.370Z] error: <service-helpers> Error getting services from Docker server 'caddy_proxy': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:13:13.371Z] error: <service-helpers> Error getting services from Docker server 'watchtower': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:13:13.371Z] error: <service-helpers> Error getting services from Docker server 'homepage': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:13:13.372Z] error: <service-helpers> Error getting services from Docker server 'duckdns_jucago705': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:13:13.372Z] error: <service-helpers> Error getting services from Docker server 'duckdns_jucago706': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:13:13.373Z] error: <service-helpers> Error getting services from Docker server 'telegram-bot': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:13:13.373Z] error: <service-helpers> Error getting services from Docker server 'media-stack': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:13:13.373Z] error: <service-helpers> Error getting services from Docker server 'arr-stack': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:13:13.374Z] error: <service-helpers> Error getting services from Docker server 'system-stack': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:13:13.375Z] error: <service-helpers> Error getting services from Docker server 'infrastructure-stack': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:13:13.375Z] error: <service-helpers> Error getting services from Docker server 'showStats': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:13:13.375Z] error: <service-helpers> Error getting services from Docker server 'showLogs': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:13:13.376Z] error: <service-helpers> Error getting services from Docker server 'labelFilter': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:13:13.376Z] error: <service-helpers> Error getting services from Docker server 'refreshInterval': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:13:13.377Z] error: <service-helpers> Error getting services from Docker server 'readOnly': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:13:13.377Z] error: <service-helpers> Error getting services from Docker server 'timeout': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:13:13.377Z] error: <service-helpers> Error getting services from Docker server 'cache': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:13:13.378Z] error: <service-helpers> Error getting services from Docker server 'logs': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:13:13.378Z] error: <service-helpers> Error getting services from Docker server 'stats': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:13:13.379Z] error: <service-helpers> Error getting services from Docker server 'events': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:13:13.379Z] error: <service-helpers> Error getting services from Docker server 'images': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:13:13.380Z] error: <service-helpers> Error getting services from Docker server 'networks': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:13:13.380Z] error: <service-helpers> Error getting services from Docker server 'volumes': [Error: connect EACCES /var/run/docker.sock] {
  errno: -13,
  code: 'EACCES',
  syscall: 'connect',
  address: '/var/run/docker.sock'
}
[2025-08-01T10:21:10.765Z] error: <credentialedProxyHandler> HTTP Error 404 calling http://host.docker.internal:9000/api/endpoints/1/docker/containers/json?all=1
[2025-08-01T10:22:21.964Z] error: <credentialedProxyHandler> HTTP Error 404 calling http://host.docker.internal:9000/api/endpoints/1/docker/containers/json?all=1
[2025-08-01T10:22:29.461Z] error: <credentialedProxyHandler> HTTP Error 404 calling http://host.docker.internal:9000/api/endpoints/1/docker/containers/json?all=1
[2025-08-01T10:22:51.212Z] error: <credentialedProxyHandler> HTTP Error 404 calling http://host.docker.internal:9000/api/endpoints/1/docker/containers/json?all=1
[2025-08-01T10:22:56.547Z] error: <credentialedProxyHandler> HTTP Error 404 calling http://host.docker.internal:9000/api/endpoints/1/docker/containers/json?all=1
[2025-08-01T10:22:58.901Z] error: <credentialedProxyHandler> HTTP Error 404 calling http://host.docker.internal:9000/api/endpoints/1/docker/containers/json?all=1
[2025-08-01T10:23:10.911Z] error: <credentialedProxyHandler> HTTP Error 404 calling http://host.docker.internal:9000/api/endpoints/2/docker/containers/json?all=1
[2025-08-01T10:23:11.555Z] error: <credentialedProxyHandler> HTTP Error 404 calling http://host.docker.internal:9000/api/endpoints/2/docker/containers/json?all=1
[2025-08-01T10:24:00.632Z] error: <credentialedProxyHandler> HTTP Error 404 calling http://host.docker.internal:9000/api/endpoints/2/docker/containers/json?all=1
[2025-08-01T10:24:07.177Z] error: <credentialedProxyHandler> HTTP Error 404 calling http://host.docker.internal:9000/api/endpoints/2/docker/containers/json?all=1
[2025-08-01T10:24:30.804Z] error: <credentialedProxyHandler> HTTP Error 404 calling http://host.docker.internal:9000/api/endpoints/2/docker/containers/json?all=1
[2025-08-01T10:24:44.897Z] error: <credentialedProxyHandler> HTTP Error 404 calling http://host.docker.internal:9000/api/endpoints/2/docker/containers/json?all=1
[2025-08-01T10:24:45.955Z] error: <credentialedProxyHandler> HTTP Error 404 calling http://host.docker.internal:9000/api/endpoints/2/docker/containers/json?all=1
[2025-08-01T10:38:57.319Z] error: incomplete explicit mapping pair; a key node is missed; or followed by a non-tabulated empty line (21:3)

 18 | 
 19 | 
 20 | 
 21 | - :
--------^
 22 |     - Sonarr:
 23 |         - icon: "sonarr.png"
[2025-08-01T10:38:57.335Z] error: YAMLException: incomplete explicit mapping pair; a key node is missed; or followed by a non-tabulated empty line (21:3)

 18 | 
 19 | 
 20 | 
 21 | - :
--------^
 22 |     - Sonarr:
 23 |         - icon: "sonarr.png"
    at generateError (file:///app/node_modules/.pnpm/js-yaml@4.1.0/node_modules/js-yaml/dist/js-yaml.mjs:1273:10)
    at throwError (file:///app/node_modules/.pnpm/js-yaml@4.1.0/node_modules/js-yaml/dist/js-yaml.mjs:1277:9)
    at readBlockMapping (file:///app/node_modules/.pnpm/js-yaml@4.1.0/node_modules/js-yaml/dist/js-yaml.mjs:2180:9)
    at composeNode (file:///app/node_modules/.pnpm/js-yaml@4.1.0/node_modules/js-yaml/dist/js-yaml.mjs:2531:12)
    at readBlockSequence (file:///app/node_modules/.pnpm/js-yaml@4.1.0/node_modules/js-yaml/dist/js-yaml.mjs:2098:5)
    at composeNode (file:///app/node_modules/.pnpm/js-yaml@4.1.0/node_modules/js-yaml/dist/js-yaml.mjs:2530:12)
    at readDocument (file:///app/node_modules/.pnpm/js-yaml@4.1.0/node_modules/js-yaml/dist/js-yaml.mjs:2715:3)
    at loadDocuments (file:///app/node_modules/.pnpm/js-yaml@4.1.0/node_modules/js-yaml/dist/js-yaml.mjs:2778:5)
    at Object.load$1 [as load] (file:///app/node_modules/.pnpm/js-yaml@4.1.0/node_modules/js-yaml/dist/js-yaml.mjs:2804:19)
    at m (/app/.next/server/pages/api/bookmarks.js:1:1817)
[2025-08-01T10:38:57.768Z] error: <index> YAMLException: incomplete explicit mapping pair; a key node is missed; or followed by a non-tabulated empty line (21:3)

 18 | 
 19 | 
 20 | 
 21 | - :
--------^
 22 |     - Sonarr:
 23 |         - icon: "sonarr.png"
    at generateError (file:///app/node_modules/.pnpm/js-yaml@4.1.0/node_modules/js-yaml/dist/js-yaml.mjs:1273:10)
    at throwError (file:///app/node_modules/.pnpm/js-yaml@4.1.0/node_modules/js-yaml/dist/js-yaml.mjs:1277:9)
    at readBlockMapping (file:///app/node_modules/.pnpm/js-yaml@4.1.0/node_modules/js-yaml/dist/js-yaml.mjs:2180:9)
    at composeNode (file:///app/node_modules/.pnpm/js-yaml@4.1.0/node_modules/js-yaml/dist/js-yaml.mjs:2531:12)
    at readBlockSequence (file:///app/node_modules/.pnpm/js-yaml@4.1.0/node_modules/js-yaml/dist/js-yaml.mjs:2098:5)
    at composeNode (file:///app/node_modules/.pnpm/js-yaml@4.1.0/node_modules/js-yaml/dist/js-yaml.mjs:2530:12)
    at readDocument (file:///app/node_modules/.pnpm/js-yaml@4.1.0/node_modules/js-yaml/dist/js-yaml.mjs:2715:3)
    at loadDocuments (file:///app/node_modules/.pnpm/js-yaml@4.1.0/node_modules/js-yaml/dist/js-yaml.mjs:2778:5)
    at Object.load$1 [as load] (file:///app/node_modules/.pnpm/js-yaml@4.1.0/node_modules/js-yaml/dist/js-yaml.mjs:2804:19)
    at f (/app/.next/server/pages/index.js:1:14842)
[2025-08-01T10:38:58.056Z] error: incomplete explicit mapping pair; a key node is missed; or followed by a non-tabulated empty line (21:3)

 18 | 
 19 | 
 20 | 
 21 | - :
--------^
 22 |     - Sonarr:
 23 |         - icon: "sonarr.png"
[2025-08-01T10:38:58.059Z] error: YAMLException: incomplete explicit mapping pair; a key node is missed; or followed by a non-tabulated empty line (21:3)

 18 | 
 19 | 
 20 | 
 21 | - :
--------^
 22 |     - Sonarr:
 23 |         - icon: "sonarr.png"
    at generateError (file:///app/node_modules/.pnpm/js-yaml@4.1.0/node_modules/js-yaml/dist/js-yaml.mjs:1273:10)
    at throwError (file:///app/node_modules/.pnpm/js-yaml@4.1.0/node_modules/js-yaml/dist/js-yaml.mjs:1277:9)
    at readBlockMapping (file:///app/node_modules/.pnpm/js-yaml@4.1.0/node_modules/js-yaml/dist/js-yaml.mjs:2180:9)
    at composeNode (file:///app/node_modules/.pnpm/js-yaml@4.1.0/node_modules/js-yaml/dist/js-yaml.mjs:2531:12)
    at readBlockSequence (file:///app/node_modules/.pnpm/js-yaml@4.1.0/node_modules/js-yaml/dist/js-yaml.mjs:2098:5)
    at composeNode (file:///app/node_modules/.pnpm/js-yaml@4.1.0/node_modules/js-yaml/dist/js-yaml.mjs:2530:12)
    at readDocument (file:///app/node_modules/.pnpm/js-yaml@4.1.0/node_modules/js-yaml/dist/js-yaml.mjs:2715:3)
    at loadDocuments (file:///app/node_modules/.pnpm/js-yaml@4.1.0/node_modules/js-yaml/dist/js-yaml.mjs:2778:5)
    at Object.load$1 [as load] (file:///app/node_modules/.pnpm/js-yaml@4.1.0/node_modules/js-yaml/dist/js-yaml.mjs:2804:19)
    at m (/app/.next/server/pages/api/bookmarks.js:1:1817)
[2025-08-01T10:39:34.587Z] error: bad indentation of a mapping entry (17:41)

 14 |  ... torrent:
 15 |  ... icon: "qbittorrent.png"
 16 |  ... href: "https://tanketorrent.duckdns.org"
 17 |  ... description: "Cliente torrent"''
-----------------------------------------^
 18 |  ... yseerr:
 19 |  ... icon: "jellyseerr.png"
[2025-08-01T10:39:34.589Z] error: YAMLException: bad indentation of a mapping entry (17:41)

 14 |  ... torrent:
 15 |  ... icon: "qbittorrent.png"
 16 |  ... href: "https://tanketorrent.duckdns.org"
 17 |  ... description: "Cliente torrent"''
-----------------------------------------^
 18 |  ... yseerr:
 19 |  ... icon: "jellyseerr.png"
    at generateError (file:///app/node_modules/.pnpm/js-yaml@4.1.0/node_modules/js-yaml/dist/js-yaml.mjs:1273:10)
    at throwError (file:///app/node_modules/.pnpm/js-yaml@4.1.0/node_modules/js-yaml/dist/js-yaml.mjs:1277:9)
    at readBlockMapping (file:///app/node_modules/.pnpm/js-yaml@4.1.0/node_modules/js-yaml/dist/js-yaml.mjs:2272:7)
    at composeNode (file:///app/node_modules/.pnpm/js-yaml@4.1.0/node_modules/js-yaml/dist/js-yaml.mjs:2531:12)
    at readBlockSequence (file:///app/node_modules/.pnpm/js-yaml@4.1.0/node_modules/js-yaml/dist/js-yaml.mjs:2098:5)
    at composeNode (file:///app/node_modules/.pnpm/js-yaml@4.1.0/node_modules/js-yaml/dist/js-yaml.mjs:2530:12)
    at readBlockMapping (file:///app/node_modules/.pnpm/js-yaml@4.1.0/node_modules/js-yaml/dist/js-yaml.mjs:2254:11)
    at composeNode (file:///app/node_modules/.pnpm/js-yaml@4.1.0/node_modules/js-yaml/dist/js-yaml.mjs:2531:12)
    at readBlockSequence (file:///app/node_modules/.pnpm/js-yaml@4.1.0/node_modules/js-yaml/dist/js-yaml.mjs:2098:5)
    at composeNode (file:///app/node_modules/.pnpm/js-yaml@4.1.0/node_modules/js-yaml/dist/js-yaml.mjs:2530:12)
[2025-08-01T10:39:34.913Z] error: <index> YAMLException: bad indentation of a mapping entry (17:41)

 14 |  ... torrent:
 15 |  ... icon: "qbittorrent.png"
 16 |  ... href: "https://tanketorrent.duckdns.org"
 17 |  ... description: "Cliente torrent"''
-----------------------------------------^
 18 |  ... yseerr:
 19 |  ... icon: "jellyseerr.png"
    at generateError (file:///app/node_modules/.pnpm/js-yaml@4.1.0/node_modules/js-yaml/dist/js-yaml.mjs:1273:10)
    at throwError (file:///app/node_modules/.pnpm/js-yaml@4.1.0/node_modules/js-yaml/dist/js-yaml.mjs:1277:9)
    at readBlockMapping (file:///app/node_modules/.pnpm/js-yaml@4.1.0/node_modules/js-yaml/dist/js-yaml.mjs:2272:7)
    at composeNode (file:///app/node_modules/.pnpm/js-yaml@4.1.0/node_modules/js-yaml/dist/js-yaml.mjs:2531:12)
    at readBlockSequence (file:///app/node_modules/.pnpm/js-yaml@4.1.0/node_modules/js-yaml/dist/js-yaml.mjs:2098:5)
    at composeNode (file:///app/node_modules/.pnpm/js-yaml@4.1.0/node_modules/js-yaml/dist/js-yaml.mjs:2530:12)
    at readBlockMapping (file:///app/node_modules/.pnpm/js-yaml@4.1.0/node_modules/js-yaml/dist/js-yaml.mjs:2254:11)
    at composeNode (file:///app/node_modules/.pnpm/js-yaml@4.1.0/node_modules/js-yaml/dist/js-yaml.mjs:2531:12)
    at readBlockSequence (file:///app/node_modules/.pnpm/js-yaml@4.1.0/node_modules/js-yaml/dist/js-yaml.mjs:2098:5)
    at composeNode (file:///app/node_modules/.pnpm/js-yaml@4.1.0/node_modules/js-yaml/dist/js-yaml.mjs:2530:12)
[2025-08-01T10:39:35.173Z] error: bad indentation of a mapping entry (17:41)

 14 |  ... torrent:
 15 |  ... icon: "qbittorrent.png"
 16 |  ... href: "https://tanketorrent.duckdns.org"
 17 |  ... description: "Cliente torrent"''
-----------------------------------------^
 18 |  ... yseerr:
 19 |  ... icon: "jellyseerr.png"
[2025-08-01T10:39:35.174Z] error: YAMLException: bad indentation of a mapping entry (17:41)

 14 |  ... torrent:
 15 |  ... icon: "qbittorrent.png"
 16 |  ... href: "https://tanketorrent.duckdns.org"
 17 |  ... description: "Cliente torrent"''
-----------------------------------------^
 18 |  ... yseerr:
 19 |  ... icon: "jellyseerr.png"
    at generateError (file:///app/node_modules/.pnpm/js-yaml@4.1.0/node_modules/js-yaml/dist/js-yaml.mjs:1273:10)
    at throwError (file:///app/node_modules/.pnpm/js-yaml@4.1.0/node_modules/js-yaml/dist/js-yaml.mjs:1277:9)
    at readBlockMapping (file:///app/node_modules/.pnpm/js-yaml@4.1.0/node_modules/js-yaml/dist/js-yaml.mjs:2272:7)
    at composeNode (file:///app/node_modules/.pnpm/js-yaml@4.1.0/node_modules/js-yaml/dist/js-yaml.mjs:2531:12)
    at readBlockSequence (file:///app/node_modules/.pnpm/js-yaml@4.1.0/node_modules/js-yaml/dist/js-yaml.mjs:2098:5)
    at composeNode (file:///app/node_modules/.pnpm/js-yaml@4.1.0/node_modules/js-yaml/dist/js-yaml.mjs:2530:12)
    at readBlockMapping (file:///app/node_modules/.pnpm/js-yaml@4.1.0/node_modules/js-yaml/dist/js-yaml.mjs:2254:11)
    at composeNode (file:///app/node_modules/.pnpm/js-yaml@4.1.0/node_modules/js-yaml/dist/js-yaml.mjs:2531:12)
    at readBlockSequence (file:///app/node_modules/.pnpm/js-yaml@4.1.0/node_modules/js-yaml/dist/js-yaml.mjs:2098:5)
    at composeNode (file:///app/node_modules/.pnpm/js-yaml@4.1.0/node_modules/js-yaml/dist/js-yaml.mjs:2530:12)
[2025-08-01T10:55:17.241Z] error: <httpProxy> Error calling http://**********:9117/api/v2.0/indexers...
[2025-08-01T10:55:17.294Z] error: <httpProxy> [
  500,
  Error: connect ECONNREFUSED **********:9117
      at <unknown> (Error: connect ECONNREFUSED **********:9117) {
    errno: -111,
    code: 'ECONNREFUSED',
    syscall: 'connect',
    address: '**********',
    port: 9117
  }
]
[2025-08-01T10:55:17.313Z] error: <jackettProxyHandler> Error calling Jackett API: 500. Data: { error: [Object] }
[2025-08-01T10:55:46.241Z] error: <httpProxy> Error calling http://**********:9117/api/v2.0/indexers...
[2025-08-01T10:55:46.246Z] error: <httpProxy> [
  500,
  Error: connect ECONNREFUSED **********:9117
      at <unknown> (Error: connect ECONNREFUSED **********:9117) {
    errno: -111,
    code: 'ECONNREFUSED',
    syscall: 'connect',
    address: '**********',
    port: 9117
  }
]
[2025-08-01T10:55:46.251Z] error: <jackettProxyHandler> Error calling Jackett API: 500. Data: { error: [Object] }
[2025-08-01T10:55:47.238Z] error: <httpProxy> Error calling http://**********:9117/api/v2.0/indexers...
[2025-08-01T10:55:47.241Z] error: <httpProxy> [
  500,
  Error: connect ECONNREFUSED **********:9117
      at <unknown> (Error: connect ECONNREFUSED **********:9117) {
    errno: -111,
    code: 'ECONNREFUSED',
    syscall: 'connect',
    address: '**********',
    port: 9117
  }
]
[2025-08-01T10:55:47.244Z] error: <jackettProxyHandler> Error calling Jackett API: 500. Data: { error: [Object] }
[2025-08-01T10:55:49.412Z] error: <httpProxy> Error calling http://**********:9117/api/v2.0/indexers...
[2025-08-01T10:55:49.436Z] error: <httpProxy> [
  500,
  Error: connect ECONNREFUSED **********:9117
      at <unknown> (Error: connect ECONNREFUSED **********:9117) {
    errno: -111,
    code: 'ECONNREFUSED',
    syscall: 'connect',
    address: '**********',
    port: 9117
  }
]
[2025-08-01T10:55:49.441Z] error: <jackettProxyHandler> Error calling Jackett API: 500. Data: { error: [Object] }
[2025-08-01T10:55:49.683Z] error: <httpProxy> Error calling http://**********:9117/api/v2.0/indexers...
[2025-08-01T10:55:49.684Z] error: <httpProxy> [
  500,
  Error: connect ECONNREFUSED **********:9117
      at <unknown> (Error: connect ECONNREFUSED **********:9117) {
    errno: -111,
    code: 'ECONNREFUSED',
    syscall: 'connect',
    address: '**********',
    port: 9117
  }
]
[2025-08-01T10:55:49.684Z] error: <jackettProxyHandler> Error calling Jackett API: 500. Data: { error: [Object] }
[2025-08-01T10:58:20.236Z] error: <httpProxy> Error calling http://**********:9117/api/v2.0/indexers...
[2025-08-01T10:58:20.242Z] error: <httpProxy> [
  500,
  Error: connect ECONNREFUSED **********:9117
      at <unknown> (Error: connect ECONNREFUSED **********:9117) {
    errno: -111,
    code: 'ECONNREFUSED',
    syscall: 'connect',
    address: '**********',
    port: 9117
  }
]
[2025-08-01T10:58:20.242Z] error: <jackettProxyHandler> Error calling Jackett API: 500. Data: { error: [Object] }
[2025-08-01T10:58:30.387Z] error: <httpProxy> Error calling http://**********:9117/api/v2.0/indexers...
[2025-08-01T10:58:30.389Z] error: <httpProxy> [
  500,
  Error: connect ECONNREFUSED **********:9117
      at <unknown> (Error: connect ECONNREFUSED **********:9117) {
    errno: -111,
    code: 'ECONNREFUSED',
    syscall: 'connect',
    address: '**********',
    port: 9117
  }
]
[2025-08-01T10:58:30.389Z] error: <jackettProxyHandler> Error calling Jackett API: 500. Data: { error: [Object] }
[2025-08-01T10:58:41.421Z] error: <httpProxy> Error calling http://**********:9117/api/v2.0/indexers...
[2025-08-01T10:58:41.423Z] error: <httpProxy> [
  500,
  Error: connect ECONNREFUSED **********:9117
      at <unknown> (Error: connect ECONNREFUSED **********:9117) {
    errno: -111,
    code: 'ECONNREFUSED',
    syscall: 'connect',
    address: '**********',
    port: 9117
  }
]
[2025-08-01T10:58:41.424Z] error: <jackettProxyHandler> Error calling Jackett API: 500. Data: { error: [Object] }
[2025-08-01T10:59:08.966Z] error: <httpProxy> Error calling http://**********:9117/api/v2.0/indexers...
[2025-08-01T10:59:08.967Z] error: <httpProxy> [
  500,
  Error: connect ECONNREFUSED **********:9117
      at <unknown> (Error: connect ECONNREFUSED **********:9117) {
    errno: -111,
    code: 'ECONNREFUSED',
    syscall: 'connect',
    address: '**********',
    port: 9117
  }
]
[2025-08-01T10:59:08.967Z] error: <jackettProxyHandler> Error calling Jackett API: 500. Data: { error: [Object] }
[2025-08-01T11:02:52.577Z] error: <httpProxy> Error calling http://**********:9117/api/v2.0/indexers...
[2025-08-01T11:02:52.577Z] error: <httpProxy> [
  500,
  Error: connect ECONNREFUSED **********:9117
      at <unknown> (Error: connect ECONNREFUSED **********:9117) {
    errno: -111,
    code: 'ECONNREFUSED',
    syscall: 'connect',
    address: '**********',
    port: 9117
  }
]
[2025-08-01T11:02:52.577Z] error: <jackettProxyHandler> Error calling Jackett API: 500. Data: { error: [Object] }
[2025-08-01T11:02:58.107Z] error: <httpProxy> Error calling http://**********:9117/api/v2.0/indexers...
[2025-08-01T11:02:58.109Z] error: <httpProxy> [
  500,
  Error: connect ECONNREFUSED **********:9117
      at <unknown> (Error: connect ECONNREFUSED **********:9117) {
    errno: -111,
    code: 'ECONNREFUSED',
    syscall: 'connect',
    address: '**********',
    port: 9117
  }
]
[2025-08-01T11:02:58.109Z] error: <jackettProxyHandler> Error calling Jackett API: 500. Data: { error: [Object] }
[2025-08-01T11:05:06.544Z] error: <httpProxy> Error calling http://**********:9117/api/v2.0/indexers...
[2025-08-01T11:05:06.582Z] error: <httpProxy> [
  500,
  Error: connect ECONNREFUSED **********:9117
      at <unknown> (Error: connect ECONNREFUSED **********:9117) {
    errno: -111,
    code: 'ECONNREFUSED',
    syscall: 'connect',
    address: '**********',
    port: 9117
  }
]
[2025-08-01T11:05:06.583Z] error: <jackettProxyHandler> Error calling Jackett API: 500. Data: { error: [Object] }
[2025-08-01T11:05:09.728Z] error: <httpProxy> Error calling http://**********:9117/api/v2.0/indexers...
[2025-08-01T11:05:09.729Z] error: <httpProxy> [
  500,
  Error: connect ECONNREFUSED **********:9117
      at <unknown> (Error: connect ECONNREFUSED **********:9117) {
    errno: -111,
    code: 'ECONNREFUSED',
    syscall: 'connect',
    address: '**********',
    port: 9117
  }
]
[2025-08-01T11:05:09.730Z] error: <jackettProxyHandler> Error calling Jackett API: 500. Data: { error: [Object] }
[2025-08-01T15:49:36.350Z] error: <httpProxy> Error calling http://**********:9117/api/v2.0/indexers...
[2025-08-01T15:49:37.314Z] error: <httpProxy> [
  500,
  Error: connect ECONNREFUSED **********:9117
      at <unknown> (Error: connect ECONNREFUSED **********:9117) {
    errno: -111,
    code: 'ECONNREFUSED',
    syscall: 'connect',
    address: '**********',
    port: 9117
  }
]
[2025-08-01T15:49:37.358Z] error: <jackettProxyHandler> Error calling Jackett API: 500. Data: { error: [Object] }
[2025-08-01T15:49:55.507Z] error: <httpProxy> Error calling http://**********:9117/api/v2.0/indexers...
[2025-08-01T15:49:55.508Z] error: <httpProxy> [
  500,
  Error: connect ECONNREFUSED **********:9117
      at <unknown> (Error: connect ECONNREFUSED **********:9117) {
    errno: -111,
    code: 'ECONNREFUSED',
    syscall: 'connect',
    address: '**********',
    port: 9117
  }
]
[2025-08-01T15:49:55.509Z] error: <jackettProxyHandler> Error calling Jackett API: 500. Data: { error: [Object] }
[2025-08-01T15:51:11.372Z] error: <httpProxy> Error calling http://**********:9117/api/v2.0/indexers...
[2025-08-01T15:51:11.484Z] error: <httpProxy> [
  500,
  Error: connect ECONNREFUSED **********:9117
      at <unknown> (Error: connect ECONNREFUSED **********:9117) {
    errno: -111,
    code: 'ECONNREFUSED',
    syscall: 'connect',
    address: '**********',
    port: 9117
  }
]
[2025-08-01T15:51:11.599Z] error: <jackettProxyHandler> Error calling Jackett API: 500. Data: { error: [Object] }
[2025-08-01T15:51:30.385Z] error: <httpProxy> Error calling http://**********:9117/api/v2.0/indexers...
[2025-08-01T15:51:30.386Z] error: <httpProxy> [
  500,
  Error: connect ECONNREFUSED **********:9117
      at <unknown> (Error: connect ECONNREFUSED **********:9117) {
    errno: -111,
    code: 'ECONNREFUSED',
    syscall: 'connect',
    address: '**********',
    port: 9117
  }
]
[2025-08-01T15:51:30.386Z] error: <jackettProxyHandler> Error calling Jackett API: 500. Data: { error: [Object] }
[2025-08-01T15:51:31.313Z] error: <httpProxy> Error calling http://**********:9117/api/v2.0/indexers...
[2025-08-01T15:51:31.314Z] error: <httpProxy> [
  500,
  Error: connect ECONNREFUSED **********:9117
      at <unknown> (Error: connect ECONNREFUSED **********:9117) {
    errno: -111,
    code: 'ECONNREFUSED',
    syscall: 'connect',
    address: '**********',
    port: 9117
  }
]
[2025-08-01T15:51:31.315Z] error: <jackettProxyHandler> Error calling Jackett API: 500. Data: { error: [Object] }
[2025-08-01T15:51:31.640Z] error: <httpProxy> Error calling http://**********:9117/api/v2.0/indexers...
[2025-08-01T15:51:31.646Z] error: <httpProxy> [
  500,
  Error: connect ECONNREFUSED **********:9117
      at <unknown> (Error: connect ECONNREFUSED **********:9117) {
    errno: -111,
    code: 'ECONNREFUSED',
    syscall: 'connect',
    address: '**********',
    port: 9117
  }
]
[2025-08-01T15:51:31.672Z] error: <jackettProxyHandler> Error calling Jackett API: 500. Data: { error: [Object] }
[2025-08-01T15:51:31.725Z] error: <httpProxy> Error calling http://**********:9117/api/v2.0/indexers...
[2025-08-01T15:51:31.725Z] error: <httpProxy> [
  500,
  Error: connect ECONNREFUSED **********:9117
      at <unknown> (Error: connect ECONNREFUSED **********:9117) {
    errno: -111,
    code: 'ECONNREFUSED',
    syscall: 'connect',
    address: '**********',
    port: 9117
  }
]
[2025-08-01T15:51:31.726Z] error: <jackettProxyHandler> Error calling Jackett API: 500. Data: { error: [Object] }
[2025-08-01T15:51:52.472Z] error: <httpProxy> Error calling http://**********:9117/api/v2.0/indexers...
[2025-08-01T15:51:52.472Z] error: <httpProxy> [
  500,
  Error: connect ECONNREFUSED **********:9117
      at <unknown> (Error: connect ECONNREFUSED **********:9117) {
    errno: -111,
    code: 'ECONNREFUSED',
    syscall: 'connect',
    address: '**********',
    port: 9117
  }
]
[2025-08-01T15:51:52.472Z] error: <jackettProxyHandler> Error calling Jackett API: 500. Data: { error: [Object] }
[2025-08-01T15:51:59.732Z] error: <httpProxy> Error calling http://**********:9117/api/v2.0/indexers...
[2025-08-01T15:51:59.733Z] error: <httpProxy> [
  500,
  Error: connect ECONNREFUSED **********:9117
      at <unknown> (Error: connect ECONNREFUSED **********:9117) {
    errno: -111,
    code: 'ECONNREFUSED',
    syscall: 'connect',
    address: '**********',
    port: 9117
  }
]
[2025-08-01T15:51:59.733Z] error: <jackettProxyHandler> Error calling Jackett API: 500. Data: { error: [Object] }
[2025-08-01T15:52:02.679Z] error: <httpProxy> Error calling http://**********:9117/api/v2.0/indexers...
[2025-08-01T15:52:02.680Z] error: <httpProxy> [
  500,
  Error: connect ECONNREFUSED **********:9117
      at <unknown> (Error: connect ECONNREFUSED **********:9117) {
    errno: -111,
    code: 'ECONNREFUSED',
    syscall: 'connect',
    address: '**********',
    port: 9117
  }
]
[2025-08-01T15:52:02.680Z] error: <jackettProxyHandler> Error calling Jackett API: 500. Data: { error: [Object] }
[2025-08-01T15:53:36.206Z] error: <httpProxy> Error calling http://**********:9117/api/v2.0/indexers...
[2025-08-01T15:53:36.207Z] error: <httpProxy> [
  500,
  Error: connect ECONNREFUSED **********:9117
      at <unknown> (Error: connect ECONNREFUSED **********:9117) {
    errno: -111,
    code: 'ECONNREFUSED',
    syscall: 'connect',
    address: '**********',
    port: 9117
  }
]
[2025-08-01T15:53:36.208Z] error: <jackettProxyHandler> Error calling Jackett API: 500. Data: { error: [Object] }
[2025-08-01T15:53:36.224Z] error: <httpProxy> Error calling http://host.docker.internal:8080/control/stats...
[2025-08-01T15:53:36.258Z] error: <httpProxy> [ 500, [Error: socket hang up] { code: 'ECONNRESET' } ]
[2025-08-01T15:54:40.227Z] error: <httpProxy> Error calling http://**********:9117/api/v2.0/indexers...
[2025-08-01T15:54:40.230Z] error: <httpProxy> [
  500,
  Error: connect ECONNREFUSED **********:9117
      at <unknown> (Error: connect ECONNREFUSED **********:9117) {
    errno: -111,
    code: 'ECONNREFUSED',
    syscall: 'connect',
    address: '**********',
    port: 9117
  }
]
[2025-08-01T15:54:40.230Z] error: <jackettProxyHandler> Error calling Jackett API: 500. Data: { error: [Object] }
[2025-08-01T15:54:40.245Z] error: <httpProxy> Error calling http://host.docker.internal:8080/control/stats...
[2025-08-01T15:54:40.249Z] error: <httpProxy> [
  500,
  Error: connect ECONNREFUSED **************:8080
      at <unknown> (Error: connect ECONNREFUSED **************:8080) {
    errno: -111,
    code: 'ECONNREFUSED',
    syscall: 'connect',
    address: '**************',
    port: 8080
  }
]
[2025-08-01T15:57:12.681Z] error: <httpProxy> Error calling http://**********:9117/api/v2.0/indexers...
[2025-08-01T15:57:12.684Z] error: <httpProxy> [
  500,
  Error: connect ECONNREFUSED **********:9117
      at <unknown> (Error: connect ECONNREFUSED **********:9117) {
    errno: -111,
    code: 'ECONNREFUSED',
    syscall: 'connect',
    address: '**********',
    port: 9117
  }
]
[2025-08-01T15:57:12.684Z] error: <jackettProxyHandler> Error calling Jackett API: 500. Data: { error: [Object] }
[2025-08-01T15:57:23.064Z] error: <httpProxy> Error calling http://host.docker.internal:8080/control/stats...
[2025-08-01T15:57:23.065Z] error: <httpProxy> [
  500,
  Error: connect ECONNREFUSED **************:8080
      at <unknown> (Error: connect ECONNREFUSED **************:8080) {
    errno: -111,
    code: 'ECONNREFUSED',
    syscall: 'connect',
    address: '**************',
    port: 8080
  }
]
[2025-08-01T15:58:04.799Z] error: <httpProxy> Error calling http://**********:9117/api/v2.0/indexers...
[2025-08-01T15:58:04.799Z] error: <httpProxy> [
  500,
  Error: connect ECONNREFUSED **********:9117
      at <unknown> (Error: connect ECONNREFUSED **********:9117) {
    errno: -111,
    code: 'ECONNREFUSED',
    syscall: 'connect',
    address: '**********',
    port: 9117
  }
]
[2025-08-01T15:58:04.800Z] error: <jackettProxyHandler> Error calling Jackett API: 500. Data: { error: [Object] }
[2025-08-01T15:58:05.715Z] error: <httpProxy> Error calling http://host.docker.internal:8080/control/stats...
[2025-08-01T15:58:05.716Z] error: <httpProxy> [
  500,
  Error: connect ECONNREFUSED **************:8080
      at <unknown> (Error: connect ECONNREFUSED **************:8080) {
    errno: -111,
    code: 'ECONNREFUSED',
    syscall: 'connect',
    address: '**************',
    port: 8080
  }
]
[2025-08-01T15:59:01.467Z] error: <httpProxy> Error calling http://**********:9117/api/v2.0/indexers...
[2025-08-01T15:59:01.469Z] error: <httpProxy> [
  500,
  Error: connect ECONNREFUSED **********:9117
      at <unknown> (Error: connect ECONNREFUSED **********:9117) {
    errno: -111,
    code: 'ECONNREFUSED',
    syscall: 'connect',
    address: '**********',
    port: 9117
  }
]
[2025-08-01T15:59:01.470Z] error: <jackettProxyHandler> Error calling Jackett API: 500. Data: { error: [Object] }
[2025-08-01T15:59:02.143Z] error: Host validation failed for: *************:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-08-01T15:59:07.813Z] error: <httpProxy> Error calling http://host.docker.internal:8080/control/stats...
[2025-08-01T15:59:07.814Z] error: <httpProxy> [
  500,
  Error: connect ECONNREFUSED **************:8080
      at <unknown> (Error: connect ECONNREFUSED **************:8080) {
    errno: -111,
    code: 'ECONNREFUSED',
    syscall: 'connect',
    address: '**************',
    port: 8080
  }
]
[2025-08-01T15:59:07.993Z] error: Host validation failed for: *************:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-08-01T15:59:08.034Z] error: Host validation failed for: *************:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-08-01T15:59:09.902Z] error: Host validation failed for: *************:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-08-01T15:59:09.931Z] error: Host validation failed for: *************:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-08-01T15:59:09.973Z] error: Host validation failed for: *************:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-08-01T15:59:15.791Z] error: <httpProxy> Error calling http://**********:9117/api/v2.0/indexers...
[2025-08-01T15:59:15.792Z] error: <httpProxy> [
  500,
  Error: connect ECONNREFUSED **********:9117
      at <unknown> (Error: connect ECONNREFUSED **********:9117) {
    errno: -111,
    code: 'ECONNREFUSED',
    syscall: 'connect',
    address: '**********',
    port: 9117
  }
]
[2025-08-01T15:59:15.792Z] error: <jackettProxyHandler> Error calling Jackett API: 500. Data: { error: [Object] }
[2025-08-01T15:59:43.551Z] error: <httpProxy> Error calling http://**********:9117/api/v2.0/indexers...
[2025-08-01T15:59:43.553Z] error: <httpProxy> [
  500,
  Error: connect ECONNREFUSED **********:9117
      at <unknown> (Error: connect ECONNREFUSED **********:9117) {
    errno: -111,
    code: 'ECONNREFUSED',
    syscall: 'connect',
    address: '**********',
    port: 9117
  }
]
[2025-08-01T15:59:43.554Z] error: <jackettProxyHandler> Error calling Jackett API: 500. Data: { error: [Object] }
[2025-08-01T15:59:44.354Z] error: <httpProxy> Error calling http://host.docker.internal:8080/control/stats...
[2025-08-01T15:59:44.355Z] error: <httpProxy> [
  500,
  Error: connect ECONNREFUSED **************:8080
      at <unknown> (Error: connect ECONNREFUSED **************:8080) {
    errno: -111,
    code: 'ECONNREFUSED',
    syscall: 'connect',
    address: '**************',
    port: 8080
  }
]
[2025-08-01T16:00:07.168Z] error: Host validation failed for: *************:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-08-01T16:00:07.274Z] error: Host validation failed for: *************:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-08-01T16:00:07.280Z] error: Host validation failed for: *************:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-08-01T16:00:07.286Z] error: Host validation failed for: *************:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-08-01T16:00:07.293Z] error: Host validation failed for: *************:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-08-01T16:00:07.296Z] error: Host validation failed for: *************:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-08-01T16:00:07.324Z] error: Host validation failed for: *************:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-08-01T16:00:07.339Z] error: Host validation failed for: *************:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-08-01T16:00:07.674Z] error: Host validation failed for: *************:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-08-01T16:00:09.834Z] error: Host validation failed for: *************:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-08-01T16:00:09.861Z] error: Host validation failed for: *************:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-08-01T16:00:09.866Z] error: Host validation failed for: *************:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-08-01T16:00:09.870Z] error: Host validation failed for: *************:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-08-01T16:00:09.874Z] error: Host validation failed for: *************:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-08-01T16:00:09.884Z] error: Host validation failed for: *************:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-08-01T16:00:09.898Z] error: Host validation failed for: *************:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-08-01T16:00:09.931Z] error: Host validation failed for: *************:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-08-01T16:00:09.946Z] error: Host validation failed for: *************:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-08-01T16:00:09.956Z] error: Host validation failed for: *************:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-08-01T16:00:09.964Z] error: Host validation failed for: *************:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-08-01T16:00:09.968Z] error: Host validation failed for: *************:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-08-01T16:00:09.984Z] error: Host validation failed for: *************:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-08-01T16:00:09.996Z] error: Host validation failed for: *************:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-08-01T16:00:10.000Z] error: Host validation failed for: *************:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-08-01T16:00:10.002Z] error: Host validation failed for: *************:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-08-01T16:00:10.008Z] error: Host validation failed for: *************:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-08-01T16:00:10.015Z] error: Host validation failed for: *************:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-08-01T16:00:10.025Z] error: Host validation failed for: *************:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-08-01T16:00:10.041Z] error: Host validation failed for: *************:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-08-01T16:00:10.054Z] error: Host validation failed for: *************:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-08-01T16:00:10.072Z] error: Host validation failed for: *************:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-08-01T16:00:10.074Z] error: Host validation failed for: *************:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-08-01T16:00:10.545Z] error: Host validation failed for: *************:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-08-01T16:00:10.582Z] error: Host validation failed for: *************:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-08-01T16:00:10.595Z] error: Host validation failed for: *************:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-08-01T16:00:10.599Z] error: Host validation failed for: *************:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-08-01T16:00:10.603Z] error: Host validation failed for: *************:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-08-01T16:00:10.632Z] error: Host validation failed for: *************:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-08-01T16:00:10.634Z] error: Host validation failed for: *************:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-08-01T16:00:10.665Z] error: Host validation failed for: *************:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-08-01T16:00:10.702Z] error: Host validation failed for: *************:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-08-01T16:00:10.704Z] error: Host validation failed for: *************:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-08-01T16:00:10.706Z] error: Host validation failed for: *************:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-08-01T16:00:10.708Z] error: Host validation failed for: *************:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-08-01T16:00:10.712Z] error: Host validation failed for: *************:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-08-01T16:00:10.772Z] error: Host validation failed for: *************:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-08-01T16:00:10.774Z] error: Host validation failed for: *************:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-08-01T16:00:10.777Z] error: Host validation failed for: *************:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-08-01T16:00:10.800Z] error: Host validation failed for: *************:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-08-01T16:00:10.803Z] error: Host validation failed for: *************:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-08-01T16:00:10.806Z] error: Host validation failed for: *************:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-08-01T16:00:10.843Z] error: Host validation failed for: *************:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-08-01T16:00:10.846Z] error: Host validation failed for: *************:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-08-01T16:00:10.931Z] error: Host validation failed for: *************:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-08-01T16:00:10.974Z] error: Host validation failed for: *************:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-08-01T16:00:11.027Z] error: Host validation failed for: *************:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-08-01T16:00:11.156Z] error: Host validation failed for: *************:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-08-01T16:00:11.312Z] error: Host validation failed for: *************:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-08-01T16:00:11.318Z] error: Host validation failed for: *************:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-08-01T16:00:11.321Z] error: Host validation failed for: *************:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-08-01T16:00:11.324Z] error: Host validation failed for: *************:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-08-01T16:00:11.331Z] error: Host validation failed for: *************:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-08-01T16:00:11.337Z] error: Host validation failed for: *************:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-08-01T16:00:11.339Z] error: Host validation failed for: *************:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-08-01T16:00:11.341Z] error: Host validation failed for: *************:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-08-01T16:00:11.345Z] error: Host validation failed for: *************:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-08-01T16:00:11.347Z] error: Host validation failed for: *************:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-08-01T16:00:11.375Z] error: Host validation failed for: *************:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-08-01T16:00:11.380Z] error: Host validation failed for: *************:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-08-01T16:00:11.386Z] error: Host validation failed for: *************:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-08-01T16:00:11.389Z] error: Host validation failed for: *************:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-08-01T16:00:11.391Z] error: Host validation failed for: *************:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-08-01T16:00:11.393Z] error: Host validation failed for: *************:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-08-01T16:00:11.417Z] error: Host validation failed for: *************:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-08-01T16:00:11.423Z] error: Host validation failed for: *************:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-08-01T16:00:14.563Z] error: <httpProxy> Error calling http://**********:9117/api/v2.0/indexers...
[2025-08-01T16:00:14.565Z] error: <httpProxy> [
  500,
  Error: connect ECONNREFUSED **********:9117
      at <unknown> (Error: connect ECONNREFUSED **********:9117) {
    errno: -111,
    code: 'ECONNREFUSED',
    syscall: 'connect',
    address: '**********',
    port: 9117
  }
]
[2025-08-01T16:00:14.566Z] error: <jackettProxyHandler> Error calling Jackett API: 500. Data: { error: [Object] }
[2025-08-01T16:00:15.286Z] error: <httpProxy> Error calling http://host.docker.internal:8080/control/stats...
[2025-08-01T16:00:15.288Z] error: <httpProxy> [
  500,
  Error: connect ECONNREFUSED **************:8080
      at <unknown> (Error: connect ECONNREFUSED **************:8080) {
    errno: -111,
    code: 'ECONNREFUSED',
    syscall: 'connect',
    address: '**************',
    port: 8080
  }
]
[2025-08-01T16:00:33.624Z] error: <httpProxy> Error calling http://**********:9117/api/v2.0/indexers...
[2025-08-01T16:00:33.625Z] error: <httpProxy> [
  500,
  Error: connect ECONNREFUSED **********:9117
      at <unknown> (Error: connect ECONNREFUSED **********:9117) {
    errno: -111,
    code: 'ECONNREFUSED',
    syscall: 'connect',
    address: '**********',
    port: 9117
  }
]
[2025-08-01T16:00:33.625Z] error: <jackettProxyHandler> Error calling Jackett API: 500. Data: { error: [Object] }
[2025-08-01T16:00:33.648Z] error: <httpProxy> Error calling http://host.docker.internal:8080/control/stats...
[2025-08-01T16:00:33.650Z] error: <httpProxy> [
  500,
  Error: connect ECONNREFUSED **************:8080
      at <unknown> (Error: connect ECONNREFUSED **************:8080) {
    errno: -111,
    code: 'ECONNREFUSED',
    syscall: 'connect',
    address: '**************',
    port: 8080
  }
]
[2025-08-01T16:00:35.258Z] error: <httpProxy> Error calling http://**********:9117/api/v2.0/indexers...
[2025-08-01T16:00:35.282Z] error: <httpProxy> [
  500,
  Error: connect ECONNREFUSED **********:9117
      at <unknown> (Error: connect ECONNREFUSED **********:9117) {
    errno: -111,
    code: 'ECONNREFUSED',
    syscall: 'connect',
    address: '**********',
    port: 9117
  }
]
[2025-08-01T16:00:35.282Z] error: <jackettProxyHandler> Error calling Jackett API: 500. Data: { error: [Object] }
[2025-08-01T16:00:35.352Z] error: <httpProxy> Error calling http://host.docker.internal:8080/control/stats...
[2025-08-01T16:00:35.353Z] error: <httpProxy> [
  500,
  Error: connect ECONNREFUSED **************:8080
      at <unknown> (Error: connect ECONNREFUSED **************:8080) {
    errno: -111,
    code: 'ECONNREFUSED',
    syscall: 'connect',
    address: '**************',
    port: 8080
  }
]
[2025-08-01T16:00:36.560Z] error: <httpProxy> Error calling http://**********:9117/api/v2.0/indexers...
[2025-08-01T16:00:36.562Z] error: <httpProxy> [
  500,
  Error: connect ECONNREFUSED **********:9117
      at <unknown> (Error: connect ECONNREFUSED **********:9117) {
    errno: -111,
    code: 'ECONNREFUSED',
    syscall: 'connect',
    address: '**********',
    port: 9117
  }
]
[2025-08-01T16:00:36.562Z] error: <jackettProxyHandler> Error calling Jackett API: 500. Data: { error: [Object] }
[2025-08-01T16:00:36.608Z] error: <httpProxy> Error calling http://host.docker.internal:8080/control/stats...
[2025-08-01T16:00:36.608Z] error: <httpProxy> [
  500,
  Error: connect ECONNREFUSED **************:8080
      at <unknown> (Error: connect ECONNREFUSED **************:8080) {
    errno: -111,
    code: 'ECONNREFUSED',
    syscall: 'connect',
    address: '**************',
    port: 8080
  }
]
[2025-08-01T16:00:37.626Z] error: <httpProxy> Error calling http://**********:9117/api/v2.0/indexers...
[2025-08-01T16:00:37.627Z] error: <httpProxy> [
  500,
  Error: connect ECONNREFUSED **********:9117
      at <unknown> (Error: connect ECONNREFUSED **********:9117) {
    errno: -111,
    code: 'ECONNREFUSED',
    syscall: 'connect',
    address: '**********',
    port: 9117
  }
]
[2025-08-01T16:00:37.627Z] error: <jackettProxyHandler> Error calling Jackett API: 500. Data: { error: [Object] }
[2025-08-01T16:00:37.735Z] error: <httpProxy> Error calling http://host.docker.internal:8080/control/stats...
[2025-08-01T16:00:37.736Z] error: <httpProxy> [
  500,
  Error: connect ECONNREFUSED **************:8080
      at <unknown> (Error: connect ECONNREFUSED **************:8080) {
    errno: -111,
    code: 'ECONNREFUSED',
    syscall: 'connect',
    address: '**************',
    port: 8080
  }
]
[2025-08-01T16:01:23.144Z] error: <httpProxy> Error calling http://**********:9117/api/v2.0/indexers...
[2025-08-01T16:01:23.147Z] error: <httpProxy> [
  500,
  Error: connect ECONNREFUSED **********:9117
      at <unknown> (Error: connect ECONNREFUSED **********:9117) {
    errno: -111,
    code: 'ECONNREFUSED',
    syscall: 'connect',
    address: '**********',
    port: 9117
  }
]
[2025-08-01T16:01:23.147Z] error: <jackettProxyHandler> Error calling Jackett API: 500. Data: { error: [Object] }
[2025-08-01T16:01:23.335Z] error: <httpProxy> Error calling http://host.docker.internal:8080/control/stats...
[2025-08-01T16:01:23.337Z] error: <httpProxy> [
  500,
  Error: connect ECONNREFUSED **************:8080
      at <unknown> (Error: connect ECONNREFUSED **************:8080) {
    errno: -111,
    code: 'ECONNREFUSED',
    syscall: 'connect',
    address: '**************',
    port: 8080
  }
]
[2025-08-01T16:01:49.835Z] error: <httpProxy> Error calling http://**********:9117/api/v2.0/indexers...
[2025-08-01T16:01:49.836Z] error: <httpProxy> [
  500,
  Error: connect ECONNREFUSED **********:9117
      at <unknown> (Error: connect ECONNREFUSED **********:9117) {
    errno: -111,
    code: 'ECONNREFUSED',
    syscall: 'connect',
    address: '**********',
    port: 9117
  }
]
[2025-08-01T16:01:49.836Z] error: <jackettProxyHandler> Error calling Jackett API: 500. Data: { error: [Object] }
[2025-08-01T16:01:49.903Z] error: <httpProxy> Error calling http://host.docker.internal:8080/control/stats...
[2025-08-01T16:01:49.904Z] error: <httpProxy> [
  500,
  Error: connect ECONNREFUSED **************:8080
      at <unknown> (Error: connect ECONNREFUSED **************:8080) {
    errno: -111,
    code: 'ECONNREFUSED',
    syscall: 'connect',
    address: '**************',
    port: 8080
  }
]
[2025-08-01T16:01:56.058Z] error: <httpProxy> Error calling http://**********:9117/api/v2.0/indexers...
[2025-08-01T16:01:56.059Z] error: <httpProxy> [
  500,
  Error: connect ECONNREFUSED **********:9117
      at <unknown> (Error: connect ECONNREFUSED **********:9117) {
    errno: -111,
    code: 'ECONNREFUSED',
    syscall: 'connect',
    address: '**********',
    port: 9117
  }
]
[2025-08-01T16:01:56.059Z] error: <jackettProxyHandler> Error calling Jackett API: 500. Data: { error: [Object] }
[2025-08-01T16:01:56.096Z] error: <httpProxy> Error calling http://host.docker.internal:8080/control/stats...
[2025-08-01T16:01:56.097Z] error: <httpProxy> [
  500,
  Error: connect ECONNREFUSED **************:8080
      at <unknown> (Error: connect ECONNREFUSED **************:8080) {
    errno: -111,
    code: 'ECONNREFUSED',
    syscall: 'connect',
    address: '**************',
    port: 8080
  }
]
[2025-08-01T16:02:16.926Z] error: <httpProxy> Error calling http://**********:9117/api/v2.0/indexers...
[2025-08-01T16:02:16.926Z] error: <httpProxy> [
  500,
  Error: connect ECONNREFUSED **********:9117
      at <unknown> (Error: connect ECONNREFUSED **********:9117) {
    errno: -111,
    code: 'ECONNREFUSED',
    syscall: 'connect',
    address: '**********',
    port: 9117
  }
]
[2025-08-01T16:02:16.927Z] error: <jackettProxyHandler> Error calling Jackett API: 500. Data: { error: [Object] }
[2025-08-01T16:02:17.638Z] error: <httpProxy> Error calling http://host.docker.internal:8080/control/stats...
[2025-08-01T16:02:17.639Z] error: <httpProxy> [
  500,
  Error: connect ECONNREFUSED **************:8080
      at <unknown> (Error: connect ECONNREFUSED **************:8080) {
    errno: -111,
    code: 'ECONNREFUSED',
    syscall: 'connect',
    address: '**************',
    port: 8080
  }
]
[2025-08-01T16:03:32.355Z] error: <httpProxy> Error calling http://**********:9117/api/v2.0/indexers...
[2025-08-01T16:03:32.383Z] error: <httpProxy> [
  500,
  Error: connect ECONNREFUSED **********:9117
      at <unknown> (Error: connect ECONNREFUSED **********:9117) {
    errno: -111,
    code: 'ECONNREFUSED',
    syscall: 'connect',
    address: '**********',
    port: 9117
  }
]
[2025-08-01T16:03:32.384Z] error: <jackettProxyHandler> Error calling Jackett API: 500. Data: { error: [Object] }
[2025-08-01T16:03:35.976Z] error: <httpProxy> Error calling http://host.docker.internal:8080/control/stats...
[2025-08-01T16:03:35.977Z] error: <httpProxy> [
  500,
  Error: connect ECONNREFUSED **************:8080
      at <unknown> (Error: connect ECONNREFUSED **************:8080) {
    errno: -111,
    code: 'ECONNREFUSED',
    syscall: 'connect',
    address: '**************',
    port: 8080
  }
]
