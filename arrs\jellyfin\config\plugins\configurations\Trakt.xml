<?xml version="1.0" encoding="utf-8"?>
<PluginConfiguration xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
  <TraktUsers>
    <TraktUser>
      <AccessToken>e6583553ceaa4444536bc21ecbdb778407ff2a3a640c0ab0368e2d2d4e74850e</AccessToken>
      <RefreshToken>07367357d20993fa59614c545c0db0330e20f62e41ba3937c9d576e92972fb85</RefreshToken>
      <LinkedMbUserId>a8d66ec8-8909-40ea-aa34-a5a7c5e10f69</LinkedMbUserId>
      <SkipUnwatchedImportFromTrakt>true</SkipUnwatchedImportFromTrakt>
      <SkipPlaybackProgressImportFromTrakt>false</SkipPlaybackProgressImportFromTrakt>
      <SkipWatchedImportFromTrakt>false</SkipWatchedImportFromTrakt>
      <PostWatchedHistory>true</PostWatchedHistory>
      <PostUnwatchedHistory>false</PostUnwatchedHistory>
      <PostSetWatched>true</PostSetWatched>
      <PostSetUnwatched>false</PostSetUnwatched>
      <ExtraLogging>false</ExtraLogging>
      <ExportMediaInfo>true</ExportMediaInfo>
      <SynchronizeCollections>true</SynchronizeCollections>
      <Scrobble>true</Scrobble>
      <LocationsExcluded />
      <AccessTokenExpiration>2025-08-02T09:04:07.3261964+02:00</AccessTokenExpiration>
      <DontRemoveItemFromTrakt>true</DontRemoveItemFromTrakt>
    </TraktUser>
  </TraktUsers>
</PluginConfiguration>