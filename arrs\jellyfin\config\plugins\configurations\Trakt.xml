<?xml version="1.0" encoding="utf-8"?>
<PluginConfiguration xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
  <TraktUsers>
    <TraktUser>
      <AccessToken>96012e6918e871fe7889b46da82b3c8257d0c2be4d96cb5af492a0a6354a4ad2</AccessToken>
      <RefreshToken>9d84108e95f18958d84098d3b38746430d6257352e10d3f43783cebb0765a23f</RefreshToken>
      <LinkedMbUserId>a8d66ec8-8909-40ea-aa34-a5a7c5e10f69</LinkedMbUserId>
      <SkipUnwatchedImportFromTrakt>true</SkipUnwatchedImportFromTrakt>
      <SkipPlaybackProgressImportFromTrakt>false</SkipPlaybackProgressImportFromTrakt>
      <SkipWatchedImportFromTrakt>false</SkipWatchedImportFromTrakt>
      <PostWatchedHistory>true</PostWatchedHistory>
      <PostUnwatchedHistory>false</PostUnwatchedHistory>
      <PostSetWatched>true</PostSetWatched>
      <PostSetUnwatched>false</PostSetUnwatched>
      <ExtraLogging>false</ExtraLogging>
      <ExportMediaInfo>true</ExportMediaInfo>
      <SynchronizeCollections>true</SynchronizeCollections>
      <Scrobble>true</Scrobble>
      <LocationsExcluded />
      <AccessTokenExpiration>2025-08-01T04:53:24.925131+02:00</AccessTokenExpiration>
      <DontRemoveItemFromTrakt>true</DontRemoveItemFromTrakt>
    </TraktUser>
  </TraktUsers>
</PluginConfiguration>