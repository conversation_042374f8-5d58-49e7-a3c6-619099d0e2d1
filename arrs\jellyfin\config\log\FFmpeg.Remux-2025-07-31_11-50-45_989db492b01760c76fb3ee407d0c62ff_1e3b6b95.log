{"Protocol":0,"Id":"989db492b01760c76fb3ee407d0c62ff","Path":"/CONTENIDO/SERIES/Dexter/Season 5/Dexter - S05E07 - Circle Us Bluray-1080p.mp4","EncoderPath":null,"EncoderProtocol":null,"Type":0,"Container":"mov,mp4,m4a,3gp,3g2,mj2","Size":1556052594,"Name":"Dexter - S05E07 - Circle Us Bluray-1080p","IsRemote":false,"ETag":"9d4fd484100698e30832bcac403d2139","RunTimeTicks":28968533330,"ReadAtNativeFramerate":false,"IgnoreDts":false,"IgnoreIndex":false,"GenPtsInput":false,"SupportsTranscoding":true,"SupportsDirectStream":true,"SupportsDirectPlay":true,"IsInfiniteStream":false,"UseMostCompatibleTranscodingProfile":false,"RequiresOpening":false,"OpenToken":null,"RequiresClosing":false,"LiveStreamId":null,"BufferMs":null,"RequiresLooping":false,"SupportsProbing":true,"VideoType":0,"IsoType":null,"Video3DFormat":null,"MediaStreams":[{"Codec":"h264","CodecTag":"avc1","Language":"und","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/90000","CodecTimeBase":null,"Title":null,"VideoRange":1,"VideoRangeType":1,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":null,"LocalizedDefault":null,"LocalizedForced":null,"LocalizedExternal":null,"LocalizedHearingImpaired":null,"DisplayTitle":"1080p H264 SDR","NalLengthSize":"4","IsInterlaced":false,"IsAVC":true,"ChannelLayout":null,"BitRate":4034120,"BitDepth":8,"RefFrames":1,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":true,"IsForced":false,"IsHearingImpaired":false,"Height":1080,"Width":1920,"AverageFrameRate":23.976025,"RealFrameRate":23.976025,"ReferenceFrameRate":23.976025,"Profile":"High","Type":1,"AspectRatio":"16:9","Index":0,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":false,"Path":null,"PixelFormat":"yuv420p","Level":40,"IsAnamorphic":false},{"Codec":"aac","CodecTag":"mp4a","Language":"eng","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/48000","CodecTimeBase":null,"Title":null,"VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":null,"LocalizedDefault":"Predeterminado","LocalizedForced":null,"LocalizedExternal":"Externo","LocalizedHearingImpaired":null,"DisplayTitle":"English - HE-AAC - Stereo - Predeterminado","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":"stereo","BitRate":127999,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":2,"SampleRate":48000,"IsDefault":true,"IsForced":false,"IsHearingImpaired":false,"Height":null,"Width":null,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":"HE-AAC","Type":0,"AspectRatio":null,"Index":1,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":false,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"aac","CodecTag":"mp4a","Language":"spa","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/48000","CodecTimeBase":null,"Title":null,"VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":null,"LocalizedDefault":"Predeterminado","LocalizedForced":null,"LocalizedExternal":"Externo","LocalizedHearingImpaired":null,"DisplayTitle":"Spanish - HE-AAC - Stereo","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":"stereo","BitRate":127999,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":2,"SampleRate":48000,"IsDefault":false,"IsForced":false,"IsHearingImpaired":false,"Height":null,"Width":null,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":"HE-AAC","Type":0,"AspectRatio":null,"Index":2,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":false,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"mov_text","CodecTag":"tx3g","Language":"spa","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/90000","CodecTimeBase":null,"Title":null,"VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":"Indefinido","LocalizedDefault":"Predeterminado","LocalizedForced":"Forzado","LocalizedExternal":"Externo","LocalizedHearingImpaired":"Discapacidad Auditiva","DisplayTitle":"Spanish - Predeterminado - MOV_TEXT","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":null,"BitRate":74,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":true,"IsForced":false,"IsHearingImpaired":false,"Height":0,"Width":0,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":2,"AspectRatio":null,"Index":3,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":true,"SupportsExternalStream":true,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"mov_text","CodecTag":"tx3g","Language":"eng","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/90000","CodecTimeBase":null,"Title":null,"VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":"Indefinido","LocalizedDefault":"Predeterminado","LocalizedForced":"Forzado","LocalizedExternal":"Externo","LocalizedHearingImpaired":"Discapacidad Auditiva","DisplayTitle":"English - MOV_TEXT","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":null,"BitRate":83,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":false,"IsForced":false,"IsHearingImpaired":false,"Height":0,"Width":0,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":2,"AspectRatio":null,"Index":4,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":true,"SupportsExternalStream":true,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"mjpeg","CodecTag":null,"Language":null,"ColorRange":null,"ColorSpace":"bt470bg","ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/90000","CodecTimeBase":null,"Title":null,"VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":null,"LocalizedDefault":null,"LocalizedForced":null,"LocalizedExternal":null,"LocalizedHearingImpaired":null,"DisplayTitle":null,"NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":null,"BitRate":null,"BitDepth":8,"RefFrames":1,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":false,"IsForced":false,"IsHearingImpaired":false,"Height":197,"Width":350,"AverageFrameRate":null,"RealFrameRate":90000,"ReferenceFrameRate":90000,"Profile":"Baseline","Type":3,"AspectRatio":"16:9","Index":5,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":false,"Path":null,"PixelFormat":"yuvj420p","Level":-99,"IsAnamorphic":false}],"MediaAttachments":[{"Codec":"mjpeg","CodecTag":"[0][0][0][0]","Comment":null,"Index":5,"FileName":null,"MimeType":null,"DeliveryUrl":null}],"Formats":[],"Bitrate":4297221,"FallbackMaxStreamingBitrate":null,"Timestamp":null,"RequiredHttpHeaders":{},"TranscodingUrl":null,"TranscodingSubProtocol":0,"TranscodingContainer":null,"AnalyzeDurationMs":null,"DefaultAudioStreamIndex":null,"DefaultSubtitleStreamIndex":null,"HasSegments":false}

/usr/lib/jellyfin-ffmpeg/ffmpeg -analyzeduration 200M -probesize 1G -ss 00:43:18.500 -noaccurate_seek -fflags +genpts -f mov,mp4,m4a,3gp,3g2,mj2  -i file:"/CONTENIDO/SERIES/Dexter/Season 5/Dexter - S05E07 - Circle Us Bluray-1080p.mp4" -map_metadata -1 -map_chapters -1 -threads 0 -map 0:0 -map 0:2 -map -0:s -codec:v:0 copy -bsf:v h264_mp4toannexb -start_at_zero -codec:a:0 copy -copyts -avoid_negative_ts disabled -max_muxing_queue_size 2048 -f hls -max_delay 5000000 -hls_time 6 -hls_segment_type fmp4 -hls_fmp4_init_filename "e354cdb4625abe476e952d4c978dbed8-1.mp4" -start_number 433 -hls_segment_filename "/cache/transcodes/e354cdb4625abe476e952d4c978dbed8%d.mp4" -hls_playlist_type vod -hls_list_size 0 -y "/cache/transcodes/e354cdb4625abe476e952d4c978dbed8.m3u8"


ffmpeg version 7.0.2-Jellyfin Copyright (c) 2000-2024 the FFmpeg developers
  built with gcc 12 (Debian 12.2.0-14)
  configuration: --prefix=/usr/lib/jellyfin-ffmpeg --target-os=linux --extra-version=Jellyfin --disable-doc --disable-ffplay --disable-ptx-compression --disable-static --disable-libxcb --disable-sdl2 --disable-xlib --enable-lto=auto --enable-gpl --enable-version3 --enable-shared --enable-gmp --enable-gnutls --enable-chromaprint --enable-opencl --enable-libdrm --enable-libxml2 --enable-libass --enable-libfreetype --enable-libfribidi --enable-libfontconfig --enable-libharfbuzz --enable-libbluray --enable-libmp3lame --enable-libopus --enable-libtheora --enable-libvorbis --enable-libopenmpt --enable-libdav1d --enable-libsvtav1 --enable-libwebp --enable-libvpx --enable-libx264 --enable-libx265 --enable-libzvbi --enable-libzimg --enable-libfdk-aac --arch=amd64 --enable-libshaderc --enable-libplacebo --enable-vulkan --enable-vaapi --enable-amf --enable-libvpl --enable-ffnvcodec --enable-cuda --enable-cuda-llvm --enable-cuvid --enable-nvdec --enable-nvenc
  libavutil      59.  8.100 / 59.  8.100
  libavcodec     61.  3.100 / 61.  3.100
  libavformat    61.  1.100 / 61.  1.100
  libavdevice    61.  1.100 / 61.  1.100
  libavfilter    10.  1.100 / 10.  1.100
  libswscale      8.  1.100 /  8.  1.100
  libswresample   5.  1.100 /  5.  1.100
  libpostproc    58.  1.100 / 58.  1.100
[mov,mp4,m4a,3gp,3g2,mj2 @ 0x65487a5d18c0] stream 0, timescale not set
Input #0, mov,mp4,m4a,3gp,3g2,mj2, from 'file:/CONTENIDO/SERIES/Dexter/Season 5/Dexter - S05E07 - Circle Us Bluray-1080p.mp4':
  Metadata:
    major_brand     : isom
    minor_version   : 512
    compatible_brands: isomiso2avc1mp41
    title           : Dexter [S05.E07] - Nuestro Círculo
    encoder         : Lavf60.16.100
    description     : Justo cuando está ayudando a Lumen, Dexter se ve envuelto en una encrucijada al recibir la llamada de los chicos de homicidio para investigar un espeluznante asesinato.
    episode_id      : 7/5
  Duration: 00:48:16.85, start: 0.000000, bitrate: 4297 kb/s
  Stream #0:0[0x1](und): Video: h264 (High) (avc1 / 0x31637661), yuv420p(progressive), 1920x1080, 4034 kb/s, 23.98 fps, 23.98 tbr, 90k tbn (default)
      Metadata:
        handler_name    : VideoHandler
        vendor_id       : [0][0][0][0]
  Stream #0:1[0x2](eng): Audio: aac (HE-AAC) (mp4a / 0x6134706D), 48000 Hz, stereo, fltp, 127 kb/s (default)
      Metadata:
        handler_name    : SoundHandler
        vendor_id       : [0][0][0][0]
  Stream #0:2[0x3](spa): Audio: aac (HE-AAC) (mp4a / 0x6134706D), 48000 Hz, stereo, fltp, 127 kb/s
      Metadata:
        handler_name    : SoundHandler
        vendor_id       : [0][0][0][0]
  Stream #0:3[0x4](spa): Subtitle: mov_text (tx3g / 0x67337874), 0 kb/s (default)
      Metadata:
        handler_name    : SubtitleHandler
  Stream #0:4[0x5](eng): Subtitle: mov_text (tx3g / 0x67337874), 0 kb/s
      Metadata:
        handler_name    : SubtitleHandler
  Stream #0:5[0x0]: Video: mjpeg (Baseline), yuvj420p(pc, bt470bg/unknown/unknown), 350x197, 90k tbr, 90k tbn (attached pic)
[out#0/hls @ 0x65487a654dc0] Codec AVOption threads (set the number of threads) has not been used for any stream. The most likely reason is either wrong type (e.g. a video option with no video streams) or that it is a private option of some encoder which was not actually used for any stream.
Stream mapping:
  Stream #0:0 -> #0:0 (copy)
  Stream #0:2 -> #0:1 (copy)
[hls @ 0x65487a5f0e00] Opening '/cache/transcodes/e354cdb4625abe476e952d4c978dbed8-1.mp4' for writing
Output #0, hls, to '/cache/transcodes/e354cdb4625abe476e952d4c978dbed8.m3u8':
  Metadata:
    encoder         : Lavf61.1.100
  Stream #0:0: Video: h264 (High) (avc1 / 0x31637661), yuv420p(progressive), 1920x1080, q=2-31, 4034 kb/s, 23.98 fps, 23.98 tbr, 90k tbn (default)
  Stream #0:1: Audio: aac (HE-AAC) (mp4a / 0x6134706D), 48000 Hz, stereo, fltp, 127 kb/s
Press [q] to stop, [?] for help
[hls @ 0x65487a5f0e00] Opening '/cache/transcodes/e354cdb4625abe476e952d4c978dbed8433.mp4' for writing
[hls @ 0x65487a5f0e00] Opening '/cache/transcodes/e354cdb4625abe476e952d4c978dbed8434.mp4' for writing
[hls @ 0x65487a5f0e00] Opening '/cache/transcodes/e354cdb4625abe476e952d4c978dbed8435.mp4' for writing
[hls @ 0x65487a5f0e00] Opening '/cache/transcodes/e354cdb4625abe476e952d4c978dbed8436.mp4' for writing
[hls @ 0x65487a5f0e00] Opening '/cache/transcodes/e354cdb4625abe476e952d4c978dbed8437.mp4' for writing
[hls @ 0x65487a5f0e00] Opening '/cache/transcodes/e354cdb4625abe476e952d4c978dbed8438.mp4' for writing
[hls @ 0x65487a5f0e00] Opening '/cache/transcodes/e354cdb4625abe476e952d4c978dbed8439.mp4' for writing
[hls @ 0x65487a5f0e00] Opening '/cache/transcodes/e354cdb4625abe476e952d4c978dbed8440.mp4' for writing
[hls @ 0x65487a5f0e00] Opening '/cache/transcodes/e354cdb4625abe476e952d4c978dbed8441.mp4' for writing
[hls @ 0x65487a5f0e00] Opening '/cache/transcodes/e354cdb4625abe476e952d4c978dbed8442.mp4' for writing
[hls @ 0x65487a5f0e00] Opening '/cache/transcodes/e354cdb4625abe476e952d4c978dbed8443.mp4' for writing
[hls @ 0x65487a5f0e00] Opening '/cache/transcodes/e354cdb4625abe476e952d4c978dbed8444.mp4' for writing
[hls @ 0x65487a5f0e00] Opening '/cache/transcodes/e354cdb4625abe476e952d4c978dbed8445.mp4' for writing
[hls @ 0x65487a5f0e00] Opening '/cache/transcodes/e354cdb4625abe476e952d4c978dbed8446.mp4' for writing
[hls @ 0x65487a5f0e00] Opening '/cache/transcodes/e354cdb4625abe476e952d4c978dbed8447.mp4' for writing
[hls @ 0x65487a5f0e00] Opening '/cache/transcodes/e354cdb4625abe476e952d4c978dbed8448.mp4' for writing
size=N/A time=00:00:00.00 bitrate=N/A speed=   0x    
[hls @ 0x65487a5f0e00] Opening '/cache/transcodes/e354cdb4625abe476e952d4c978dbed8449.mp4' for writing
[hls @ 0x65487a5f0e00] Opening '/cache/transcodes/e354cdb4625abe476e952d4c978dbed8450.mp4' for writing
[hls @ 0x65487a5f0e00] Opening '/cache/transcodes/e354cdb4625abe476e952d4c978dbed8451.mp4' for writing
[hls @ 0x65487a5f0e00] Opening '/cache/transcodes/e354cdb4625abe476e952d4c978dbed8452.mp4' for writing
[hls @ 0x65487a5f0e00] Opening '/cache/transcodes/e354cdb4625abe476e952d4c978dbed8453.mp4' for writing
[hls @ 0x65487a5f0e00] Opening '/cache/transcodes/e354cdb4625abe476e952d4c978dbed8454.mp4' for writing
[hls @ 0x65487a5f0e00] Opening '/cache/transcodes/e354cdb4625abe476e952d4c978dbed8455.mp4' for writing
[hls @ 0x65487a5f0e00] Opening '/cache/transcodes/e354cdb4625abe476e952d4c978dbed8456.mp4' for writing
[hls @ 0x65487a5f0e00] Opening '/cache/transcodes/e354cdb4625abe476e952d4c978dbed8457.mp4' for writing
size=N/A time=00:00:11.55 bitrate=N/A speed=11.5x    
[hls @ 0x65487a5f0e00] Opening '/cache/transcodes/e354cdb4625abe476e952d4c978dbed8458.mp4' for writing
[hls @ 0x65487a5f0e00] Opening '/cache/transcodes/e354cdb4625abe476e952d4c978dbed8459.mp4' for writing
[hls @ 0x65487a5f0e00] Opening '/cache/transcodes/e354cdb4625abe476e952d4c978dbed8460.mp4' for writing
[hls @ 0x65487a5f0e00] Opening '/cache/transcodes/e354cdb4625abe476e952d4c978dbed8461.mp4' for writing
[hls @ 0x65487a5f0e00] Opening '/cache/transcodes/e354cdb4625abe476e952d4c978dbed8462.mp4' for writing
[hls @ 0x65487a5f0e00] Opening '/cache/transcodes/e354cdb4625abe476e952d4c978dbed8463.mp4' for writing
[hls @ 0x65487a5f0e00] Opening '/cache/transcodes/e354cdb4625abe476e952d4c978dbed8464.mp4' for writing
[hls @ 0x65487a5f0e00] Opening '/cache/transcodes/e354cdb4625abe476e952d4c978dbed8465.mp4' for writing
[hls @ 0x65487a5f0e00] Opening '/cache/transcodes/e354cdb4625abe476e952d4c978dbed8466.mp4' for writing
[hls @ 0x65487a5f0e00] Opening '/cache/transcodes/e354cdb4625abe476e952d4c978dbed8467.mp4' for writing
size=N/A time=00:00:23.94 bitrate=N/A speed=  16x    
[hls @ 0x65487a5f0e00] Opening '/cache/transcodes/e354cdb4625abe476e952d4c978dbed8468.mp4' for writing
[hls @ 0x65487a5f0e00] Opening '/cache/transcodes/e354cdb4625abe476e952d4c978dbed8469.mp4' for writing
[hls @ 0x65487a5f0e00] Opening '/cache/transcodes/e354cdb4625abe476e952d4c978dbed8470.mp4' for writing
[hls @ 0x65487a5f0e00] Opening '/cache/transcodes/e354cdb4625abe476e952d4c978dbed8471.mp4' for writing
[hls @ 0x65487a5f0e00] Opening '/cache/transcodes/e354cdb4625abe476e952d4c978dbed8472.mp4' for writing
[hls @ 0x65487a5f0e00] Opening '/cache/transcodes/e354cdb4625abe476e952d4c978dbed8473.mp4' for writing
[hls @ 0x65487a5f0e00] Opening '/cache/transcodes/e354cdb4625abe476e952d4c978dbed8474.mp4' for writing
[hls @ 0x65487a5f0e00] Opening '/cache/transcodes/e354cdb4625abe476e952d4c978dbed8475.mp4' for writing
[hls @ 0x65487a5f0e00] Opening '/cache/transcodes/e354cdb4625abe476e952d4c978dbed8476.mp4' for writing
[hls @ 0x65487a5f0e00] Opening '/cache/transcodes/e354cdb4625abe476e952d4c978dbed8477.mp4' for writing
[hls @ 0x65487a5f0e00] Opening '/cache/transcodes/e354cdb4625abe476e952d4c978dbed8478.mp4' for writing
[hls @ 0x65487a5f0e00] Opening '/cache/transcodes/e354cdb4625abe476e952d4c978dbed8479.mp4' for writing
size=N/A time=00:00:51.17 bitrate=N/A speed=25.6x    
[hls @ 0x65487a5f0e00] Opening '/cache/transcodes/e354cdb4625abe476e952d4c978dbed8480.mp4' for writing
[hls @ 0x65487a5f0e00] Opening '/cache/transcodes/e354cdb4625abe476e952d4c978dbed8481.mp4' for writing
[hls @ 0x65487a5f0e00] Opening '/cache/transcodes/e354cdb4625abe476e952d4c978dbed8482.mp4' for writing
[hls @ 0x65487a5f0e00] Opening '/cache/transcodes/e354cdb4625abe476e952d4c978dbed8483.mp4' for writing
[hls @ 0x65487a5f0e00] Opening '/cache/transcodes/e354cdb4625abe476e952d4c978dbed8484.mp4' for writing
[hls @ 0x65487a5f0e00] Opening '/cache/transcodes/e354cdb4625abe476e952d4c978dbed8485.mp4' for writing
[hls @ 0x65487a5f0e00] Opening '/cache/transcodes/e354cdb4625abe476e952d4c978dbed8486.mp4' for writing
[hls @ 0x65487a5f0e00] Opening '/cache/transcodes/e354cdb4625abe476e952d4c978dbed8487.mp4' for writing
size=N/A time=00:01:25.04 bitrate=N/A speed=  34x    
[hls @ 0x65487a5f0e00] Opening '/cache/transcodes/e354cdb4625abe476e952d4c978dbed8488.mp4' for writing
[hls @ 0x65487a5f0e00] Opening '/cache/transcodes/e354cdb4625abe476e952d4c978dbed8489.mp4' for writing
[hls @ 0x65487a5f0e00] Opening '/cache/transcodes/e354cdb4625abe476e952d4c978dbed8490.mp4' for writing
[hls @ 0x65487a5f0e00] Opening '/cache/transcodes/e354cdb4625abe476e952d4c978dbed8491.mp4' for writing
[hls @ 0x65487a5f0e00] Opening '/cache/transcodes/e354cdb4625abe476e952d4c978dbed8492.mp4' for writing
[hls @ 0x65487a5f0e00] Opening '/cache/transcodes/e354cdb4625abe476e952d4c978dbed8493.mp4' for writing
[hls @ 0x65487a5f0e00] Opening '/cache/transcodes/e354cdb4625abe476e952d4c978dbed8494.mp4' for writing
[hls @ 0x65487a5f0e00] Opening '/cache/transcodes/e354cdb4625abe476e952d4c978dbed8495.mp4' for writing
[hls @ 0x65487a5f0e00] Opening '/cache/transcodes/e354cdb4625abe476e952d4c978dbed8496.mp4' for writing
[hls @ 0x65487a5f0e00] Opening '/cache/transcodes/e354cdb4625abe476e952d4c978dbed8497.mp4' for writing
[hls @ 0x65487a5f0e00] Opening '/cache/transcodes/e354cdb4625abe476e952d4c978dbed8498.mp4' for writing
[hls @ 0x65487a5f0e00] Opening '/cache/transcodes/e354cdb4625abe476e952d4c978dbed8499.mp4' for writing
size=N/A time=00:01:58.20 bitrate=N/A speed=39.4x    
[hls @ 0x65487a5f0e00] Opening '/cache/transcodes/e354cdb4625abe476e952d4c978dbed8500.mp4' for writing
[hls @ 0x65487a5f0e00] Opening '/cache/transcodes/e354cdb4625abe476e952d4c978dbed8501.mp4' for writing
[hls @ 0x65487a5f0e00] Opening '/cache/transcodes/e354cdb4625abe476e952d4c978dbed8502.mp4' for writing
[hls @ 0x65487a5f0e00] Opening '/cache/transcodes/e354cdb4625abe476e952d4c978dbed8503.mp4' for writing
[hls @ 0x65487a5f0e00] Opening '/cache/transcodes/e354cdb4625abe476e952d4c978dbed8504.mp4' for writing
size=N/A time=00:02:17.51 bitrate=N/A speed=39.3x    
[hls @ 0x65487a5f0e00] Opening '/cache/transcodes/e354cdb4625abe476e952d4c978dbed8505.mp4' for writing
[hls @ 0x65487a5f0e00] Opening '/cache/transcodes/e354cdb4625abe476e952d4c978dbed8506.mp4' for writing
[hls @ 0x65487a5f0e00] Opening '/cache/transcodes/e354cdb4625abe476e952d4c978dbed8507.mp4' for writing
[hls @ 0x65487a5f0e00] Opening '/cache/transcodes/e354cdb4625abe476e952d4c978dbed8508.mp4' for writing
[hls @ 0x65487a5f0e00] Opening '/cache/transcodes/e354cdb4625abe476e952d4c978dbed8509.mp4' for writing
[hls @ 0x65487a5f0e00] Opening '/cache/transcodes/e354cdb4625abe476e952d4c978dbed8510.mp4' for writing
[hls @ 0x65487a5f0e00] Opening '/cache/transcodes/e354cdb4625abe476e952d4c978dbed8511.mp4' for writing
size=N/A time=00:02:47.33 bitrate=N/A speed=41.8x    
[hls @ 0x65487a5f0e00] Opening '/cache/transcodes/e354cdb4625abe476e952d4c978dbed8512.mp4' for writing
[hls @ 0x65487a5f0e00] Opening '/cache/transcodes/e354cdb4625abe476e952d4c978dbed8513.mp4' for writing
[hls @ 0x65487a5f0e00] Opening '/cache/transcodes/e354cdb4625abe476e952d4c978dbed8514.mp4' for writing
[hls @ 0x65487a5f0e00] Opening '/cache/transcodes/e354cdb4625abe476e952d4c978dbed8515.mp4' for writing
[hls @ 0x65487a5f0e00] Opening '/cache/transcodes/e354cdb4625abe476e952d4c978dbed8516.mp4' for writing
[hls @ 0x65487a5f0e00] Opening '/cache/transcodes/e354cdb4625abe476e952d4c978dbed8517.mp4' for writing
[hls @ 0x65487a5f0e00] Opening '/cache/transcodes/e354cdb4625abe476e952d4c978dbed8518.mp4' for writing
[hls @ 0x65487a5f0e00] Opening '/cache/transcodes/e354cdb4625abe476e952d4c978dbed8519.mp4' for writing
[hls @ 0x65487a5f0e00] Opening '/cache/transcodes/e354cdb4625abe476e952d4c978dbed8520.mp4' for writing
size=N/A time=00:03:25.20 bitrate=N/A speed=45.6x    
[hls @ 0x65487a5f0e00] Opening '/cache/transcodes/e354cdb4625abe476e952d4c978dbed8521.mp4' for writing
[hls @ 0x65487a5f0e00] Opening '/cache/transcodes/e354cdb4625abe476e952d4c978dbed8522.mp4' for writing
[hls @ 0x65487a5f0e00] Opening '/cache/transcodes/e354cdb4625abe476e952d4c978dbed8523.mp4' for writing
[hls @ 0x65487a5f0e00] Opening '/cache/transcodes/e354cdb4625abe476e952d4c978dbed8524.mp4' for writing
[hls @ 0x65487a5f0e00] Opening '/cache/transcodes/e354cdb4625abe476e952d4c978dbed8525.mp4' for writing
[hls @ 0x65487a5f0e00] Opening '/cache/transcodes/e354cdb4625abe476e952d4c978dbed8526.mp4' for writing
[hls @ 0x65487a5f0e00] Opening '/cache/transcodes/e354cdb4625abe476e952d4c978dbed8527.mp4' for writing
[hls @ 0x65487a5f0e00] Opening '/cache/transcodes/e354cdb4625abe476e952d4c978dbed8528.mp4' for writing
[hls @ 0x65487a5f0e00] Opening '/cache/transcodes/e354cdb4625abe476e952d4c978dbed8529.mp4' for writing
[hls @ 0x65487a5f0e00] Opening '/cache/transcodes/e354cdb4625abe476e952d4c978dbed8530.mp4' for writing
size=N/A time=00:04:28.35 bitrate=N/A speed=53.6x    
[hls @ 0x65487a5f0e00] Opening '/cache/transcodes/e354cdb4625abe476e952d4c978dbed8531.mp4' for writing
[hls @ 0x65487a5f0e00] Opening '/cache/transcodes/e354cdb4625abe476e952d4c978dbed8532.mp4' for writing
[hls @ 0x65487a5f0e00] Opening '/cache/transcodes/e354cdb4625abe476e952d4c978dbed8533.mp4' for writing
[out#0/hls @ 0x65487a654dc0] video:122054KiB audio:4673KiB subtitle:0KiB other streams:0KiB global headers:0KiB muxing overhead: unknown
size=N/A time=00:04:33.02 bitrate=N/A speed=53.3x    