# Arr-Bot Configuration
# Bot de Telegram para gestionar Radarr y Sonarr
# Integrado con stack DNS DuckDNS

# ==================== TELEGRAM CONFIGURATION ====================
# Token del bot de Telegram (obtenido de @BotFather)
TELEGRAM_BOT_TOKEN=your_telegram_bot_token_here

# ID del chat donde enviar notificaciones (puede ser un chat privado o grupo)
# Para obtener tu chat ID, envía un mensaje a tu bot y visita:
# https://api.telegram.org/bot<YOUR_BOT_TOKEN>/getUpdates
TELEGRAM_CHAT_ID=your_chat_id_here

# ==================== RADARR CONFIGURATION ====================
# URL de Radarr (desde el contexto del stack DNS)
# Por defecto usa host.docker.internal para acceder al host desde el contenedor
RADARR_URL=http://host.docker.internal:7878

# Clave API de Radarr (obtenida desde Configuración > General > API Key)
RADARR_API_KEY=your_radarr_api_key_here

# ==================== SONARR CONFIGURATION ====================
# URL de Sonarr (desde el contexto del stack DNS)
# Por defecto usa host.docker.internal para acceder al host desde el contenedor
SONARR_URL=http://host.docker.internal:8989

# Clave API de Sonarr (obtenida desde Configuración > General > API Key)
SONARR_API_KEY=your_sonarr_api_key_here

# ==================== WEBHOOK CONFIGURATION ====================
# Puerto para el servidor webhook (debe coincidir con el puerto expuesto en Docker)
WEBHOOK_PORT=8082

# ==================== NOTAS DE CONFIGURACIÓN ====================
# 1. Copia este archivo como .env y completa los valores
# 2. Las URLs por defecto usan host.docker.internal para acceder a servicios del host
# 3. Si Radarr/Sonarr están en contenedores, ajusta las URLs según corresponda
# 4. El puerto 8082 se usa para evitar conflictos con AdGuard (8080)
# 5. Asegúrate de configurar los webhooks en Radarr/Sonarr apuntando a:
#    - Radarr: http://arr-bot:8082/webhook/radarr
#    - Sonarr: http://arr-bot:8082/webhook/sonarr
