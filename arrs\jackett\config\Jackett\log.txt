2025-08-01 13:14:13.0435 Warn Some of the categories provided are unsupported in Elitetorrent-wf: 5010,5020,5030,5040,5045,5050,5060,5070,5080
2025-08-01 13:14:15.4011 Info Torznab search in BitSearch => Found 20 releases [2359ms]
2025-08-01 13:14:15.4011 Info Torznab search in BitSearch => Found 20 releases [2227ms]
2025-08-01 13:14:15.4048 Info Torznab search in 0Magnet => Found 98 releases [3333ms]
2025-08-01 13:14:15.5130 Info Torznab search in ilCorSaRoNeRo => Found 15 releases [2745ms]
2025-08-01 13:14:15.8909 Info Torznab search in Elitetorrent-wf => Found 18 releases [2850ms]
2025-08-01 13:14:16.7805 Info Torznab search in EZTV => Found 100 releases [3837ms]
2025-08-01 13:14:16.7825 Info Torznab search in eMuwarez (API) => Found 100 releases [3820ms]
2025-08-01 13:14:16.7825 Info Torznab search in TorrentGalaxyClone => Found 50 releases [3217ms]
2025-08-01 13:14:17.2712 Info Torznab search in Frozen Layer => Found 47 releases [4425ms]
2025-08-01 13:14:19.4785 Info Torznab search in MejorTorrent => Found 6 releases [7315ms]
2025-08-01 13:14:19.6576 Info Torznab search in Wolfmax 4k => Found 100 releases [7467ms]
2025-08-01 13:14:19.6848 Info Torznab search in Wolfmax 4k => Found 0 releases [0ms]
2025-08-01 13:14:20.9951 Info Torznab search in 1337x => Found 80 releases [8923ms]
2025-08-01 13:21:06.5840 Error Jackett.Common.IndexerException: Exception (ilcorsaronero): Name does not resolve

[v0.22.2217.0] Jackett.Common.IndexerException: Exception (ilcorsaronero): Name does not resolve
 ---> System.Net.Http.HttpRequestException: Name does not resolve (ilcorsaronero.link:443)
 ---> System.Net.Sockets.SocketException (0xFFFDFFFF): Name does not resolve
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.DecompressionHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at FlareSolverrSharp.ClearanceHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
   at Jackett.Common.Utils.Clients.HttpWebClient2.Run(WebRequest webRequest) in ./Jackett.Common/Utils/Clients/HttpWebClient2.cs:line 180
   at Jackett.Common.Utils.Clients.WebClient.GetResultAsync(WebRequest request) in ./Jackett.Common/Utils/Clients/WebClient.cs:line 188
   at Jackett.Common.Indexers.BaseWebIndexer.RequestWithCookiesAsync(String url, String cookieOverride, RequestType method, String referer, IEnumerable`1 data, Dictionary`2 headers, String rawbody, Nullable`1 emulateBrowser) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 617
   at Jackett.Common.Indexers.Definitions.CardigannIndexer.PerformQuery(TorznabQuery query) in ./Jackett.Common/Indexers/Definitions/CardigannIndexer.cs:line 1563
   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 378
   --- End of inner exception stack trace ---
   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 403
   at Jackett.Common.Indexers.BaseWebIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 816
   at Jackett.Server.Controllers.ResultsController.Torznab(TorznabRequest request) in ./Jackett.Server/Controllers/ResultsController.cs:line 437

2025-08-01 13:21:06.6646 Error Jackett.Common.IndexerException: Exception (limetorrents): Name does not resolve

[v0.22.2217.0] Jackett.Common.IndexerException: Exception (limetorrents): Name does not resolve
 ---> System.Net.Http.HttpRequestException: Name does not resolve (www.limetorrents.lol:443)
 ---> System.Net.Sockets.SocketException (0xFFFDFFFF): Name does not resolve
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.DecompressionHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at FlareSolverrSharp.ClearanceHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
   at Jackett.Common.Utils.Clients.HttpWebClient2.Run(WebRequest webRequest) in ./Jackett.Common/Utils/Clients/HttpWebClient2.cs:line 180
   at Jackett.Common.Utils.Clients.WebClient.GetResultAsync(WebRequest request) in ./Jackett.Common/Utils/Clients/WebClient.cs:line 188
   at Jackett.Common.Indexers.BaseWebIndexer.RequestWithCookiesAsync(String url, String cookieOverride, RequestType method, String referer, IEnumerable`1 data, Dictionary`2 headers, String rawbody, Nullable`1 emulateBrowser) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 617
   at Jackett.Common.Indexers.Definitions.CardigannIndexer.PerformQuery(TorznabQuery query) in ./Jackett.Common/Indexers/Definitions/CardigannIndexer.cs:line 1563
   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 378
   --- End of inner exception stack trace ---
   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 403
   at Jackett.Common.Indexers.BaseWebIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 816
   at Jackett.Server.Controllers.ResultsController.Torznab(TorznabRequest request) in ./Jackett.Server/Controllers/ResultsController.cs:line 437

2025-08-01 13:21:06.8949 Info Torznab search in MoviesDVDR => Found 30 releases [302ms]
2025-08-01 13:21:06.8966 Info Torznab search in BitSearch => Found 20 releases [493ms]
2025-08-01 13:21:07.1747 Info Torznab search in Elitetorrent-wf => Found 30 releases [732ms]
2025-08-01 13:21:07.7151 Info Torznab search in EZTV => Found 100 releases [1227ms]
2025-08-01 13:21:07.7271 Info Torznab search in eMuwarez (API) => Found 100 releases [1263ms]
2025-08-01 13:21:08.9827 Info Torznab search in Frozen Layer => Found 47 releases [2476ms]
2025-08-01 13:21:09.5454 Info Torznab search in MejorTorrent => Found 6 releases [2969ms]
2025-08-01 13:21:10.0389 Info Torznab search in DivxTotal => Found 100 releases [3623ms]
2025-08-01 13:21:11.3475 Info Torznab search in Wolfmax 4k => Found 100 releases [4735ms]
2025-08-01 13:21:11.3908 Info Torznab search in Wolfmax 4k => Found 0 releases [0ms]
2025-08-01 13:21:13.1388 Info Torznab search in 1337x => Found 80 releases [6754ms]
