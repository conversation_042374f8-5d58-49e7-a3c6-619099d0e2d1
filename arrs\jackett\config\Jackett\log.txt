2025-08-01 13:14:13.0435 Warn Some of the categories provided are unsupported in Elitetorrent-wf: 5010,5020,5030,5040,5045,5050,5060,5070,5080
2025-08-01 13:14:15.4011 Info Torznab search in BitSearch => Found 20 releases [2359ms]
2025-08-01 13:14:15.4011 Info Torznab search in BitSearch => Found 20 releases [2227ms]
2025-08-01 13:14:15.4048 Info Torznab search in 0Magnet => Found 98 releases [3333ms]
2025-08-01 13:14:15.5130 Info Torznab search in ilCorSaRoNeRo => Found 15 releases [2745ms]
2025-08-01 13:14:15.8909 Info Torznab search in Elitetorrent-wf => Found 18 releases [2850ms]
2025-08-01 13:14:16.7805 Info Torznab search in EZTV => Found 100 releases [3837ms]
2025-08-01 13:14:16.7825 Info Torznab search in eMuwarez (API) => Found 100 releases [3820ms]
2025-08-01 13:14:16.7825 Info Torznab search in TorrentGalaxyClone => Found 50 releases [3217ms]
2025-08-01 13:14:17.2712 Info Torznab search in Frozen Layer => Found 47 releases [4425ms]
2025-08-01 13:14:19.4785 Info Torznab search in MejorTorrent => Found 6 releases [7315ms]
2025-08-01 13:14:19.6576 Info Torznab search in Wolfmax 4k => Found 100 releases [7467ms]
2025-08-01 13:14:19.6848 Info Torznab search in Wolfmax 4k => Found 0 releases [0ms]
2025-08-01 13:14:20.9951 Info Torznab search in 1337x => Found 80 releases [8923ms]
2025-08-01 13:21:06.5840 Error Jackett.Common.IndexerException: Exception (ilcorsaronero): Name does not resolve

[v0.22.2217.0] Jackett.Common.IndexerException: Exception (ilcorsaronero): Name does not resolve
 ---> System.Net.Http.HttpRequestException: Name does not resolve (ilcorsaronero.link:443)
 ---> System.Net.Sockets.SocketException (0xFFFDFFFF): Name does not resolve
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.DecompressionHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at FlareSolverrSharp.ClearanceHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
   at Jackett.Common.Utils.Clients.HttpWebClient2.Run(WebRequest webRequest) in ./Jackett.Common/Utils/Clients/HttpWebClient2.cs:line 180
   at Jackett.Common.Utils.Clients.WebClient.GetResultAsync(WebRequest request) in ./Jackett.Common/Utils/Clients/WebClient.cs:line 188
   at Jackett.Common.Indexers.BaseWebIndexer.RequestWithCookiesAsync(String url, String cookieOverride, RequestType method, String referer, IEnumerable`1 data, Dictionary`2 headers, String rawbody, Nullable`1 emulateBrowser) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 617
   at Jackett.Common.Indexers.Definitions.CardigannIndexer.PerformQuery(TorznabQuery query) in ./Jackett.Common/Indexers/Definitions/CardigannIndexer.cs:line 1563
   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 378
   --- End of inner exception stack trace ---
   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 403
   at Jackett.Common.Indexers.BaseWebIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 816
   at Jackett.Server.Controllers.ResultsController.Torznab(TorznabRequest request) in ./Jackett.Server/Controllers/ResultsController.cs:line 437

2025-08-01 13:21:06.6646 Error Jackett.Common.IndexerException: Exception (limetorrents): Name does not resolve

[v0.22.2217.0] Jackett.Common.IndexerException: Exception (limetorrents): Name does not resolve
 ---> System.Net.Http.HttpRequestException: Name does not resolve (www.limetorrents.lol:443)
 ---> System.Net.Sockets.SocketException (0xFFFDFFFF): Name does not resolve
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.DecompressionHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at FlareSolverrSharp.ClearanceHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
   at Jackett.Common.Utils.Clients.HttpWebClient2.Run(WebRequest webRequest) in ./Jackett.Common/Utils/Clients/HttpWebClient2.cs:line 180
   at Jackett.Common.Utils.Clients.WebClient.GetResultAsync(WebRequest request) in ./Jackett.Common/Utils/Clients/WebClient.cs:line 188
   at Jackett.Common.Indexers.BaseWebIndexer.RequestWithCookiesAsync(String url, String cookieOverride, RequestType method, String referer, IEnumerable`1 data, Dictionary`2 headers, String rawbody, Nullable`1 emulateBrowser) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 617
   at Jackett.Common.Indexers.Definitions.CardigannIndexer.PerformQuery(TorznabQuery query) in ./Jackett.Common/Indexers/Definitions/CardigannIndexer.cs:line 1563
   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 378
   --- End of inner exception stack trace ---
   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 403
   at Jackett.Common.Indexers.BaseWebIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 816
   at Jackett.Server.Controllers.ResultsController.Torznab(TorznabRequest request) in ./Jackett.Server/Controllers/ResultsController.cs:line 437

2025-08-01 13:21:06.8949 Info Torznab search in MoviesDVDR => Found 30 releases [302ms]
2025-08-01 13:21:06.8966 Info Torznab search in BitSearch => Found 20 releases [493ms]
2025-08-01 13:21:07.1747 Info Torznab search in Elitetorrent-wf => Found 30 releases [732ms]
2025-08-01 13:21:07.7151 Info Torznab search in EZTV => Found 100 releases [1227ms]
2025-08-01 13:21:07.7271 Info Torznab search in eMuwarez (API) => Found 100 releases [1263ms]
2025-08-01 13:21:08.9827 Info Torznab search in Frozen Layer => Found 47 releases [2476ms]
2025-08-01 13:21:09.5454 Info Torznab search in MejorTorrent => Found 6 releases [2969ms]
2025-08-01 13:21:10.0389 Info Torznab search in DivxTotal => Found 100 releases [3623ms]
2025-08-01 13:21:11.3475 Info Torznab search in Wolfmax 4k => Found 100 releases [4735ms]
2025-08-01 13:21:11.3908 Info Torznab search in Wolfmax 4k => Found 0 releases [0ms]
2025-08-01 13:21:13.1388 Info Torznab search in 1337x => Found 80 releases [6754ms]
2025-08-01 13:30:35.8250 Info Torznab search in 0Magnet => Found 98 releases (from cache) [412ms]
2025-08-01 13:30:51.8974 Info Torznab search in 1337x => Found 80 releases (from cache) [1523ms]
2025-08-01 13:30:57.1020 Info Torznab search in BitSearch => Found 20 releases (from cache) [82ms]
2025-08-01 13:30:59.5332 Info Torznab search in BitSearch => Found 20 releases (from cache) [1ms]
2025-08-01 13:30:59.5730 Warn Some of the categories provided are unsupported in Elitetorrent-wf: 5010,5020,5030,5040,5045,5050,5060,5070,5080
2025-08-01 13:30:59.8745 Info Torznab search in Elitetorrent-wf => Found 18 releases (from cache) [302ms]
2025-08-01 13:30:59.9052 Info Torznab search in eMuwarez (API) => Found 100 releases (from cache) [19ms]
2025-08-01 13:31:00.9878 Info Torznab search in EZTV => Found 100 releases (from cache) [0ms]
2025-08-01 13:31:02.3280 Info Torznab search in Frozen Layer => Found 47 releases (from cache) [1ms]
2025-08-01 13:31:02.3280 Info Torznab search in ilCorSaRoNeRo => Found 15 releases (from cache) [0ms]
2025-08-01 13:31:02.3574 Info Torznab search in MejorTorrent => Found 6 releases (from cache) [0ms]
2025-08-01 13:31:02.3574 Info Torznab search in Wolfmax 4k => Found 100 releases (from cache) [4ms]
2025-08-01 13:31:02.5485 Info Torznab search in TorrentGalaxyClone => Found 50 releases (from cache) [5ms]
2025-08-01 13:31:05.5732 Info Torznab search in Wolfmax 4k => Found 0 releases [0ms]
2025-08-01 13:47:57.6232 Info Torznab search in BitSearch => Found 20 releases (from cache) [8ms]
2025-08-01 13:47:57.6312 Info Torznab search in 1337x => Found 80 releases (from cache) [20ms]
2025-08-01 13:47:57.6304 Info Torznab search in 0Magnet => Found 98 releases (from cache) [20ms]
2025-08-01 13:47:57.6312 Info Torznab search in BitSearch => Found 20 releases (from cache) [2ms]
2025-08-01 13:47:57.6430 Warn Some of the categories provided are unsupported in Elitetorrent-wf: 5010,5020,5030,5040,5045,5050,5060,5070,5080
2025-08-01 13:47:57.7464 Info Torznab search in Wolfmax 4k => Found 100 releases (from cache) [11ms]
2025-08-01 13:47:57.6944 Info Torznab search in Frozen Layer => Found 47 releases (from cache) [3ms]
2025-08-01 13:47:57.6995 Info Torznab search in eMuwarez (API) => Found 100 releases (from cache) [33ms]
2025-08-01 13:47:57.7036 Info Torznab search in ilCorSaRoNeRo => Found 15 releases (from cache) [16ms]
2025-08-01 13:47:57.7331 Info Torznab search in MejorTorrent => Found 6 releases (from cache) [14ms]
2025-08-01 13:47:57.7480 Info Torznab search in Elitetorrent-wf => Found 18 releases (from cache) [107ms]
2025-08-01 13:47:57.6712 Info Torznab search in EZTV => Found 100 releases (from cache) [3ms]
2025-08-01 13:47:57.8401 Error Jackett.Common.IndexerException: Exception (limetorrents): Name does not resolve

[v0.22.2217.0] Jackett.Common.IndexerException: Exception (limetorrents): Name does not resolve
 ---> System.Net.Http.HttpRequestException: Name does not resolve (www.limetorrents.lol:443)
 ---> System.Net.Sockets.SocketException (0xFFFDFFFF): Name does not resolve
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.DecompressionHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at FlareSolverrSharp.ClearanceHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
   at Jackett.Common.Utils.Clients.HttpWebClient2.Run(WebRequest webRequest) in ./Jackett.Common/Utils/Clients/HttpWebClient2.cs:line 180
   at Jackett.Common.Utils.Clients.WebClient.GetResultAsync(WebRequest request) in ./Jackett.Common/Utils/Clients/WebClient.cs:line 188
   at Jackett.Common.Indexers.BaseWebIndexer.RequestWithCookiesAsync(String url, String cookieOverride, RequestType method, String referer, IEnumerable`1 data, Dictionary`2 headers, String rawbody, Nullable`1 emulateBrowser) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 617
   at Jackett.Common.Indexers.Definitions.CardigannIndexer.PerformQuery(TorznabQuery query) in ./Jackett.Common/Indexers/Definitions/CardigannIndexer.cs:line 1563
   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 378
   --- End of inner exception stack trace ---
   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 403
   at Jackett.Common.Indexers.BaseWebIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 816
   at Jackett.Server.Controllers.ResultsController.Torznab(TorznabRequest request) in ./Jackett.Server/Controllers/ResultsController.cs:line 437

2025-08-01 13:47:57.7579 Info Torznab search in TorrentGalaxyClone => Found 50 releases (from cache) [6ms]
2025-08-01 13:47:59.8412 Info Torznab search in Wolfmax 4k => Found 0 releases [0ms]
2025-08-01 13:51:59.0733 Info Torznab search in 1337x => Found 80 releases (from cache) [3ms]
2025-08-01 13:51:59.1049 Info Torznab search in BitSearch => Found 20 releases (from cache) [1ms]
2025-08-01 13:51:59.1149 Info Torznab search in DivxTotal => Found 100 releases (from cache) [3ms]
2025-08-01 13:51:59.1493 Info Torznab search in Elitetorrent-wf => Found 30 releases (from cache) [8ms]
2025-08-01 13:51:59.2242 Info Torznab search in eMuwarez (API) => Found 100 releases (from cache) [29ms]
2025-08-01 13:51:59.2707 Info Torznab search in EZTV => Found 100 releases (from cache) [0ms]
2025-08-01 13:51:59.3228 Info Torznab search in Frozen Layer => Found 47 releases (from cache) [1ms]
2025-08-01 13:51:59.8550 Info Torznab search in MejorTorrent => Found 6 releases (from cache) [1ms]
2025-08-01 13:51:59.9304 Info Torznab search in MoviesDVDR => Found 30 releases (from cache) [76ms]
2025-08-01 13:52:00.1069 Info Torznab search in Wolfmax 4k => Found 100 releases (from cache) [183ms]
2025-08-01 13:52:02.7921 Info Torznab search in Wolfmax 4k => Found 0 releases [0ms]
2025-08-01 13:52:04.4809 Info Torznab search in LimeTorrents => Found 100 releases [4955ms]
2025-08-01 13:52:04.5547 Info Torznab search in ilCorSaRoNeRo => Found 15 releases [5030ms]
2025-08-01 13:52:04.5558 Info Torznab search in LimeTorrents => Found 0 releases [0ms]
2025-08-01 13:59:51.2087 Info Checking for updates... Jackett variant: CoreLinuxMuslAmdx64
2025-08-01 13:59:51.2133 Info Updates are disabled via --NoUpdates.
2025-08-01 14:03:57.6773 Warn Some of the categories provided are unsupported in Elitetorrent-wf: 5010,5020,5030,5040,5045,5050,5060,5070,5080
2025-08-01 14:03:57.9929 Error Jackett.Common.IndexerException: Exception (ilcorsaronero): Name does not resolve

[v0.22.2217.0] Jackett.Common.IndexerException: Exception (ilcorsaronero): Name does not resolve
 ---> System.Net.Http.HttpRequestException: Name does not resolve (ilcorsaronero.link:443)
 ---> System.Net.Sockets.SocketException (0xFFFDFFFF): Name does not resolve
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.DecompressionHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at FlareSolverrSharp.ClearanceHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
   at Jackett.Common.Utils.Clients.HttpWebClient2.Run(WebRequest webRequest) in ./Jackett.Common/Utils/Clients/HttpWebClient2.cs:line 180
   at Jackett.Common.Utils.Clients.WebClient.GetResultAsync(WebRequest request) in ./Jackett.Common/Utils/Clients/WebClient.cs:line 188
   at Jackett.Common.Indexers.BaseWebIndexer.RequestWithCookiesAsync(String url, String cookieOverride, RequestType method, String referer, IEnumerable`1 data, Dictionary`2 headers, String rawbody, Nullable`1 emulateBrowser) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 617
   at Jackett.Common.Indexers.Definitions.CardigannIndexer.PerformQuery(TorznabQuery query) in ./Jackett.Common/Indexers/Definitions/CardigannIndexer.cs:line 1563
   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 378
   --- End of inner exception stack trace ---
   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 403
   at Jackett.Common.Indexers.BaseWebIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 816
   at Jackett.Server.Controllers.ResultsController.Torznab(TorznabRequest request) in ./Jackett.Server/Controllers/ResultsController.cs:line 437

2025-08-01 14:03:58.1752 Info Torznab search in BitSearch => Found 18 releases [505ms]
2025-08-01 14:03:58.2635 Info Torznab search in 0Magnet => Found 98 releases [651ms]
2025-08-01 14:03:58.2674 Info Torznab search in BitSearch => Found 18 releases [591ms]
2025-08-01 14:03:58.4509 Info Torznab search in TorrentGalaxyClone => Found 50 releases [380ms]
2025-08-01 14:03:58.4808 Info Torznab search in Elitetorrent-wf => Found 18 releases [803ms]
2025-08-01 14:03:58.7974 Info Torznab search in EZTV => Found 100 releases [934ms]
2025-08-01 14:03:58.9138 Info Torznab search in eMuwarez (API) => Found 100 releases [1068ms]
2025-08-01 14:04:00.4495 Info Torznab search in Frozen Layer => Found 47 releases [2540ms]
2025-08-01 14:04:00.9521 Info Torznab search in MejorTorrent => Found 6 releases [2936ms]
2025-08-01 14:04:03.0357 Info Torznab search in Wolfmax 4k => Found 100 releases [4965ms]
2025-08-01 14:04:03.0583 Info Torznab search in Wolfmax 4k => Found 0 releases [0ms]
2025-08-01 14:04:04.3876 Info Torznab search in 1337x => Found 80 releases [6763ms]
2025-08-01 14:19:58.2683 Info Torznab search in 0Magnet => Found 98 releases (from cache) [7ms]
2025-08-01 14:19:58.6570 Info Torznab search in 1337x => Found 80 releases (from cache) [4ms]
2025-08-01 14:19:59.2775 Info Torznab search in BitSearch => Found 18 releases (from cache) [0ms]
2025-08-01 14:19:59.4360 Info Torznab search in BitSearch => Found 18 releases (from cache) [1ms]
2025-08-01 14:19:59.4449 Warn Some of the categories provided are unsupported in Elitetorrent-wf: 5010,5020,5030,5040,5045,5050,5060,5070,5080
2025-08-01 14:19:59.4762 Info Torznab search in EZTV => Found 100 releases (from cache) [0ms]
2025-08-01 14:19:59.8528 Info Torznab search in MejorTorrent => Found 6 releases (from cache) [0ms]
2025-08-01 14:19:59.4975 Info Torznab search in eMuwarez (API) => Found 100 releases (from cache) [46ms]
2025-08-01 14:19:59.8459 Info Torznab search in Elitetorrent-wf => Found 18 releases (from cache) [401ms]
2025-08-01 14:19:59.4975 Info Torznab search in Frozen Layer => Found 47 releases (from cache) [13ms]
2025-08-01 14:19:59.9918 Info Torznab search in Wolfmax 4k => Found 100 releases (from cache) [3ms]
2025-08-01 14:20:00.2006 Info Torznab search in TorrentGalaxyClone => Found 50 releases (from cache) [1ms]
2025-08-01 14:20:00.5242 Info Torznab search in ilCorSaRoNeRo => Found 15 releases [828ms]
2025-08-01 14:20:01.9859 Info Torznab search in Wolfmax 4k => Found 0 releases [0ms]
2025-08-01 14:23:01.3254 Info Torznab search in ilCorSaRoNeRo => Found 15 releases (from cache) [0ms]
2025-08-01 14:23:01.3589 Info Torznab search in LimeTorrents => Found 100 releases (from cache) [8ms]
2025-08-01 14:23:01.9462 Info Torznab search in BitSearch => Found 18 releases [1271ms]
2025-08-01 14:23:01.9549 Info Torznab search in Elitetorrent-wf => Found 30 releases [1231ms]
2025-08-01 14:23:04.7219 Info Torznab search in MoviesDVDR => Found 30 releases [3367ms]
2025-08-01 14:23:05.1616 Info Torznab search in Frozen Layer => Found 47 releases [4380ms]
2025-08-01 14:23:08.4187 Info Torznab search in eMuwarez (API) => Found 100 releases [7662ms]
2025-08-01 14:23:08.5568 Info Torznab search in EZTV => Found 100 releases [7790ms]
2025-08-01 14:23:11.0645 Info Torznab search in Wolfmax 4k => Found 100 releases [9700ms]
2025-08-01 14:23:13.8224 Info Torznab search in DivxTotal => Found 100 releases [13137ms]
2025-08-01 14:23:15.5029 Info Torznab search in 1337x => Found 80 releases [14879ms]
2025-08-01 14:23:21.7559 Info Torznab search in MejorTorrent => Found 6 releases [20405ms]
2025-08-01 14:23:22.3850 Info Torznab search in Wolfmax 4k => Found 0 releases [0ms]
2025-08-01 14:36:29.9015 Info Torznab search in 0Magnet => Found 98 releases (from cache) [2ms]
2025-08-01 14:36:29.9123 Info Torznab search in 1337x => Found 80 releases (from cache) [1ms]
2025-08-01 14:36:30.1601 Info Torznab search in BitSearch => Found 18 releases (from cache) [0ms]
2025-08-01 14:36:30.1690 Info Torznab search in BitSearch => Found 18 releases (from cache) [0ms]
2025-08-01 14:36:30.1800 Warn Some of the categories provided are unsupported in Elitetorrent-wf: 5010,5020,5030,5040,5045,5050,5060,5070,5080
2025-08-01 14:36:30.1860 Info Torznab search in Elitetorrent-wf => Found 18 releases (from cache) [6ms]
2025-08-01 14:36:30.2042 Info Torznab search in eMuwarez (API) => Found 100 releases (from cache) [13ms]
2025-08-01 14:36:30.2087 Info Torznab search in EZTV => Found 100 releases (from cache) [0ms]
2025-08-01 14:36:30.3536 Info Torznab search in MejorTorrent => Found 6 releases (from cache) [1ms]
2025-08-01 14:36:30.3536 Info Torznab search in ilCorSaRoNeRo => Found 15 releases (from cache) [1ms]
2025-08-01 14:36:30.3692 Info Torznab search in TorrentGalaxyClone => Found 50 releases (from cache) [1ms]
2025-08-01 14:36:30.3835 Info Torznab search in Wolfmax 4k => Found 100 releases (from cache) [11ms]
2025-08-01 14:36:30.3835 Info Torznab search in Frozen Layer => Found 47 releases (from cache) [1ms]
2025-08-01 14:36:32.3199 Info Torznab search in Wolfmax 4k => Found 0 releases [0ms]
2025-08-01 14:52:29.9836 Warn Some of the categories provided are unsupported in Elitetorrent-wf: 5010,5020,5030,5040,5045,5050,5060,5070,5080
2025-08-01 14:52:30.0959 Info Torznab search in ilCorSaRoNeRo => Found 15 releases (from cache) [22ms]
2025-08-01 14:52:30.3923 Info Torznab search in BitSearch => Found 19 releases [425ms]
2025-08-01 14:52:30.4040 Info Torznab search in 0Magnet => Found 98 releases [457ms]
2025-08-01 14:52:30.4881 Info Torznab search in BitSearch => Found 19 releases [513ms]
2025-08-01 14:52:30.4976 Info Torznab search in LimeTorrents => Found 100 releases [302ms]
2025-08-01 14:52:31.0707 Info Torznab search in eMuwarez (API) => Found 100 releases [1079ms]
2025-08-01 14:52:31.1093 Info Torznab search in TorrentGalaxyClone => Found 50 releases [757ms]
2025-08-01 14:52:31.2117 Info Torznab search in EZTV => Found 100 releases [1200ms]
2025-08-01 14:52:31.2282 Info Torznab search in Elitetorrent-wf => Found 18 releases [1244ms]
2025-08-01 14:52:32.0930 Info Torznab search in LimeTorrents => Found 0 releases [0ms]
2025-08-01 14:52:33.1882 Info Torznab search in Frozen Layer => Found 47 releases [3133ms]
2025-08-01 14:52:34.2167 Info Torznab search in MejorTorrent => Found 6 releases [3981ms]
2025-08-01 14:52:35.2516 Info Torznab search in Wolfmax 4k => Found 100 releases [4974ms]
2025-08-01 14:52:35.2764 Info Torznab search in Wolfmax 4k => Found 0 releases [0ms]
2025-08-01 14:52:36.7104 Info Torznab search in 1337x => Found 80 releases [6757ms]
2025-08-01 14:54:01.0779 Info Torznab search in 1337x => Found 80 releases (from cache) [1ms]
2025-08-01 14:54:01.1204 Info Torznab search in BitSearch => Found 18 releases (from cache) [0ms]
2025-08-01 14:54:01.4778 Info Torznab search in DivxTotal => Found 100 releases (from cache) [2ms]
2025-08-01 14:54:01.6223 Info Torznab search in Elitetorrent-wf => Found 30 releases (from cache) [1ms]
2025-08-01 14:54:01.6391 Info Torznab search in eMuwarez (API) => Found 100 releases (from cache) [4ms]
2025-08-01 14:54:01.7729 Info Torznab search in EZTV => Found 100 releases (from cache) [0ms]
2025-08-01 14:54:01.7937 Info Torznab search in Frozen Layer => Found 47 releases (from cache) [1ms]
2025-08-01 14:54:02.0291 Info Torznab search in MejorTorrent => Found 6 releases (from cache) [0ms]
2025-08-01 14:54:02.0348 Info Torznab search in MoviesDVDR => Found 30 releases (from cache) [1ms]
2025-08-01 14:54:02.0715 Info Torznab search in Wolfmax 4k => Found 100 releases (from cache) [4ms]
2025-08-01 14:54:02.1616 Info Torznab search in LimeTorrents => Found 100 releases [181ms]
2025-08-01 14:54:02.2916 Info Torznab search in ilCorSaRoNeRo => Found 15 releases [329ms]
2025-08-01 14:54:04.0859 Info Torznab search in LimeTorrents => Found 0 releases [0ms]
2025-08-01 15:08:30.0557 Info Torznab search in 0Magnet => Found 98 releases (from cache) [3ms]
2025-08-01 15:08:30.0924 Info Torznab search in 1337x => Found 80 releases (from cache) [2ms]
2025-08-01 15:08:30.1032 Info Torznab search in BitSearch => Found 19 releases (from cache) [0ms]
2025-08-01 15:08:30.1032 Info Torznab search in BitSearch => Found 19 releases (from cache) [1ms]
2025-08-01 15:08:30.1032 Warn Some of the categories provided are unsupported in Elitetorrent-wf: 5010,5020,5030,5040,5045,5050,5060,5070,5080
2025-08-01 15:08:30.1376 Info Torznab search in Elitetorrent-wf => Found 18 releases (from cache) [33ms]
2025-08-01 15:08:30.1152 Info Torznab search in EZTV => Found 100 releases (from cache) [0ms]
2025-08-01 15:08:30.1276 Info Torznab search in Frozen Layer => Found 47 releases (from cache) [1ms]
2025-08-01 15:08:30.1125 Info Torznab search in eMuwarez (API) => Found 100 releases (from cache) [7ms]
2025-08-01 15:08:30.1694 Info Torznab search in LimeTorrents => Found 100 releases (from cache) [7ms]
2025-08-01 15:08:30.2070 Info Torznab search in MejorTorrent => Found 6 releases (from cache) [0ms]
2025-08-01 15:08:30.2228 Info Torznab search in Wolfmax 4k => Found 100 releases (from cache) [3ms]
2025-08-01 15:08:30.2295 Error Jackett.Common.IndexerException: Exception (ilcorsaronero): Name does not resolve

[v0.22.2217.0] Jackett.Common.IndexerException: Exception (ilcorsaronero): Name does not resolve
 ---> System.Net.Http.HttpRequestException: Name does not resolve (ilcorsaronero.link:443)
 ---> System.Net.Sockets.SocketException (0xFFFDFFFF): Name does not resolve
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.DecompressionHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at FlareSolverrSharp.ClearanceHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
   at Jackett.Common.Utils.Clients.HttpWebClient2.Run(WebRequest webRequest) in ./Jackett.Common/Utils/Clients/HttpWebClient2.cs:line 180
   at Jackett.Common.Utils.Clients.WebClient.GetResultAsync(WebRequest request) in ./Jackett.Common/Utils/Clients/WebClient.cs:line 188
   at Jackett.Common.Indexers.BaseWebIndexer.RequestWithCookiesAsync(String url, String cookieOverride, RequestType method, String referer, IEnumerable`1 data, Dictionary`2 headers, String rawbody, Nullable`1 emulateBrowser) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 617
   at Jackett.Common.Indexers.Definitions.CardigannIndexer.PerformQuery(TorznabQuery query) in ./Jackett.Common/Indexers/Definitions/CardigannIndexer.cs:line 1563
   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 378
   --- End of inner exception stack trace ---
   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 403
   at Jackett.Common.Indexers.BaseWebIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 816
   at Jackett.Server.Controllers.ResultsController.Torznab(TorznabRequest request) in ./Jackett.Server/Controllers/ResultsController.cs:line 437

2025-08-01 15:08:30.2349 Info Torznab search in TorrentGalaxyClone => Found 50 releases (from cache) [2ms]
2025-08-01 15:08:32.2184 Info Torznab search in Wolfmax 4k => Found 0 releases [0ms]
2025-08-01 15:24:30.5329 Info Torznab search in 0Magnet => Found 98 releases (from cache) [322ms]
2025-08-01 15:24:30.5382 Info Torznab search in eMuwarez (API) => Found 100 releases (from cache) [4ms]
2025-08-01 15:24:30.5340 Info Torznab search in BitSearch => Found 19 releases (from cache) [0ms]
2025-08-01 15:24:30.5340 Warn Some of the categories provided are unsupported in Elitetorrent-wf: 5010,5020,5030,5040,5045,5050,5060,5070,5080
2025-08-01 15:24:30.5340 Info Torznab search in 1337x => Found 80 releases (from cache) [3ms]
2025-08-01 15:24:30.5382 Info Torznab search in EZTV => Found 100 releases (from cache) [0ms]
2025-08-01 15:24:30.5382 Info Torznab search in Frozen Layer => Found 47 releases (from cache) [2ms]
2025-08-01 15:24:30.5340 Info Torznab search in BitSearch => Found 19 releases (from cache) [0ms]
2025-08-01 15:24:30.5445 Info Torznab search in LimeTorrents => Found 100 releases (from cache) [2ms]
2025-08-01 15:24:30.5445 Info Torznab search in MejorTorrent => Found 6 releases (from cache) [0ms]
2025-08-01 15:24:30.5495 Info Torznab search in Wolfmax 4k => Found 100 releases (from cache) [3ms]
2025-08-01 15:24:30.5540 Info Torznab search in TorrentGalaxyClone => Found 50 releases (from cache) [1ms]
2025-08-01 15:24:30.5832 Info Torznab search in Elitetorrent-wf => Found 18 releases (from cache) [48ms]
2025-08-01 15:24:30.8947 Info Torznab search in ilCorSaRoNeRo => Found 15 releases [353ms]
2025-08-01 15:24:32.2983 Info Torznab search in Wolfmax 4k => Found 0 releases [0ms]
2025-08-01 15:25:03.3508 Info Torznab search in ilCorSaRoNeRo => Found 15 releases (from cache) [0ms]
2025-08-01 15:25:03.3942 Info Torznab search in LimeTorrents => Found 100 releases (from cache) [24ms]
2025-08-01 15:25:03.4663 Warn [bitsearch] Invalid category for value: 'Ebook/Course'
2025-08-01 15:25:03.5106 Warn [bitsearch] Invalid Release: 'https://bitsearch.to/torrent/688cbae87f76a4d5fa1096e2'. No categories provided.
2025-08-01 15:25:03.5228 Info Torznab search in BitSearch => Found 18 releases [244ms]
2025-08-01 15:25:03.6165 Info Torznab search in MoviesDVDR => Found 30 releases [219ms]
2025-08-01 15:25:03.7441 Info Torznab search in Elitetorrent-wf => Found 30 releases [447ms]
2025-08-01 15:25:04.3262 Info Torznab search in eMuwarez (API) => Found 100 releases [1020ms]
2025-08-01 15:25:04.3312 Info Torznab search in EZTV => Found 100 releases [1015ms]
2025-08-01 15:25:05.8764 Info Torznab search in Frozen Layer => Found 47 releases [2536ms]
2025-08-01 15:25:06.4134 Info Torznab search in DivxTotal => Found 100 releases [3127ms]
2025-08-01 15:25:06.8363 Info Torznab search in MejorTorrent => Found 6 releases [3445ms]
2025-08-01 15:25:07.6751 Info Torznab search in Wolfmax 4k => Found 100 releases [4247ms]
2025-08-01 15:25:07.7388 Info Torznab search in Wolfmax 4k => Found 0 releases [0ms]
2025-08-01 15:25:09.8856 Info Torznab search in 1337x => Found 80 releases [6619ms]
2025-08-01 15:40:33.3154 Warn Some of the categories provided are unsupported in Elitetorrent-wf: 5010,5020,5030,5040,5045,5050,5060,5070,5080
2025-08-01 15:40:33.9384 Info Torznab search in ilCorSaRoNeRo => Found 15 releases (from cache) [124ms]
2025-08-01 15:40:41.1761 Info Torznab search in BitSearch => Found 18 releases [7899ms]
2025-08-01 15:40:42.1623 Info Torznab search in BitSearch => Found 18 releases [8993ms]
2025-08-01 15:40:42.1470 Info Torznab search in eMuwarez (API) => Found 100 releases [8870ms]
2025-08-01 15:40:42.2546 Info Torznab search in TorrentGalaxyClone => Found 50 releases [5722ms]
2025-08-01 15:40:42.2546 Info Torznab search in LimeTorrents => Found 100 releases [8513ms]
2025-08-01 15:40:42.2546 Info Torznab search in 0Magnet => Found 98 releases [9231ms]
2025-08-01 15:40:42.7001 Info Torznab search in Elitetorrent-wf => Found 18 releases [9488ms]
2025-08-01 15:40:43.0619 Info Torznab search in EZTV => Found 100 releases [9785ms]
2025-08-01 15:40:43.6736 Info Torznab search in Frozen Layer => Found 47 releases [10117ms]
2025-08-01 15:40:53.9664 Info Torznab search in Wolfmax 4k => Found 100 releases [19506ms]
2025-08-01 15:41:20.1951 Info Torznab search in 1337x => Found 80 releases [47171ms]
2025-08-01 15:41:20.3355 Info Torznab search in Wolfmax 4k => Found 0 releases [0ms]
2025-08-01 15:41:23.1559 Info Torznab search in MejorTorrent => Found 6 releases [48757ms]
2025-08-01 15:56:08.3516 Info Torznab search in 1337x => Found 80 releases (from cache) [3ms]
2025-08-01 15:56:08.3738 Info Torznab search in BitSearch => Found 18 releases (from cache) [1ms]
2025-08-01 15:56:08.4578 Info Torznab search in DivxTotal => Found 100 releases (from cache) [3ms]
2025-08-01 15:56:08.5595 Info Torznab search in Elitetorrent-wf => Found 30 releases (from cache) [2ms]
2025-08-01 15:56:08.5658 Info Torznab search in eMuwarez (API) => Found 100 releases (from cache) [7ms]
2025-08-01 15:56:09.6671 Info Torznab search in MejorTorrent => Found 6 releases (from cache) [1ms]
2025-08-01 15:56:09.6747 Info Torznab search in EZTV => Found 100 releases (from cache) [0ms]
2025-08-01 15:56:09.6875 Info Torznab search in Frozen Layer => Found 47 releases (from cache) [1ms]
2025-08-01 15:56:09.7670 Error Jackett.Common.IndexerException: Exception (limetorrents): Name does not resolve

[v0.22.2217.0] Jackett.Common.IndexerException: Exception (limetorrents): Name does not resolve
 ---> System.Net.Http.HttpRequestException: Name does not resolve (www.limetorrents.lol:443)
 ---> System.Net.Sockets.SocketException (0xFFFDFFFF): Name does not resolve
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.DecompressionHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at FlareSolverrSharp.ClearanceHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
   at Jackett.Common.Utils.Clients.HttpWebClient2.Run(WebRequest webRequest) in ./Jackett.Common/Utils/Clients/HttpWebClient2.cs:line 180
   at Jackett.Common.Utils.Clients.WebClient.GetResultAsync(WebRequest request) in ./Jackett.Common/Utils/Clients/WebClient.cs:line 188
   at Jackett.Common.Indexers.BaseWebIndexer.RequestWithCookiesAsync(String url, String cookieOverride, RequestType method, String referer, IEnumerable`1 data, Dictionary`2 headers, String rawbody, Nullable`1 emulateBrowser) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 617
   at Jackett.Common.Indexers.Definitions.CardigannIndexer.PerformQuery(TorznabQuery query) in ./Jackett.Common/Indexers/Definitions/CardigannIndexer.cs:line 1563
   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 378
   --- End of inner exception stack trace ---
   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 403
   at Jackett.Common.Indexers.BaseWebIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 816
   at Jackett.Server.Controllers.ResultsController.Torznab(TorznabRequest request) in ./Jackett.Server/Controllers/ResultsController.cs:line 437

2025-08-01 15:56:10.3083 Info Torznab search in MoviesDVDR => Found 30 releases (from cache) [1ms]
2025-08-01 15:56:10.3242 Info Torznab search in Wolfmax 4k => Found 100 releases (from cache) [2ms]
2025-08-01 15:56:10.6759 Info Torznab search in ilCorSaRoNeRo => Found 15 releases [1055ms]
2025-08-01 15:56:11.7852 Info Torznab search in Wolfmax 4k => Found 0 releases [0ms]
2025-08-01 15:57:38.3674 Info Torznab search in 0Magnet => Found 98 releases (from cache) [5ms]
2025-08-01 15:57:38.7368 Info Torznab search in 1337x => Found 80 releases (from cache) [3ms]
2025-08-01 15:57:38.9439 Info Torznab search in BitSearch => Found 18 releases (from cache) [1ms]
2025-08-01 15:57:40.2014 Warn Some of the categories provided are unsupported in Elitetorrent-wf: 5010,5020,5030,5040,5045,5050,5060,5070,5080
2025-08-01 15:57:40.2014 Info Torznab search in EZTV => Found 100 releases (from cache) [0ms]
2025-08-01 15:57:40.5334 Info Torznab search in BitSearch => Found 18 releases (from cache) [5ms]
2025-08-01 15:57:40.5372 Info Torznab search in eMuwarez (API) => Found 100 releases (from cache) [212ms]
2025-08-01 15:57:40.5945 Info Torznab search in Elitetorrent-wf => Found 18 releases (from cache) [393ms]
2025-08-01 15:57:40.6434 Info Torznab search in Frozen Layer => Found 47 releases (from cache) [3ms]
2025-08-01 15:57:40.9019 Info Torznab search in ilCorSaRoNeRo => Found 15 releases (from cache) [0ms]
2025-08-01 15:57:41.1275 Info Torznab search in LimeTorrents => Found 100 releases (from cache) [2ms]
2025-08-01 15:57:41.1765 Info Torznab search in MejorTorrent => Found 6 releases (from cache) [0ms]
2025-08-01 15:57:41.2487 Info Torznab search in Wolfmax 4k => Found 100 releases (from cache) [52ms]
2025-08-01 15:57:41.2536 Info Torznab search in TorrentGalaxyClone => Found 50 releases (from cache) [1ms]
2025-08-01 16:14:38.4901 Info Torznab search in 0Magnet => Found 98 releases (from cache) [2ms]
2025-08-01 16:14:38.5004 Info Torznab search in 1337x => Found 80 releases (from cache) [2ms]
2025-08-01 16:14:38.5105 Info Torznab search in BitSearch => Found 18 releases (from cache) [0ms]
2025-08-01 16:14:38.5202 Info Torznab search in BitSearch => Found 18 releases (from cache) [0ms]
2025-08-01 16:14:38.5319 Warn Some of the categories provided are unsupported in Elitetorrent-wf: 5010,5020,5030,5040,5045,5050,5060,5070,5080
2025-08-01 16:14:38.5452 Info Torznab search in Elitetorrent-wf => Found 18 releases (from cache) [13ms]
2025-08-01 16:14:38.5615 Info Torznab search in eMuwarez (API) => Found 100 releases (from cache) [6ms]
2025-08-01 16:14:38.5746 Info Torznab search in EZTV => Found 100 releases (from cache) [0ms]
2025-08-01 16:14:38.6047 Info Torznab search in Frozen Layer => Found 47 releases (from cache) [1ms]
2025-08-01 16:14:38.7108 Info Torznab search in LimeTorrents => Found 100 releases (from cache) [2ms]
2025-08-01 16:14:38.7194 Info Torznab search in MejorTorrent => Found 6 releases (from cache) [0ms]
2025-08-01 16:14:38.7307 Info Torznab search in Wolfmax 4k => Found 100 releases (from cache) [2ms]
2025-08-01 16:14:38.7401 Info Torznab search in TorrentGalaxyClone => Found 50 releases (from cache) [1ms]
2025-08-01 16:14:38.9732 Info Torznab search in ilCorSaRoNeRo => Found 15 releases [306ms]
2025-08-01 16:27:08.9889 Info Torznab search in ilCorSaRoNeRo => Found 15 releases (from cache) [0ms]
2025-08-01 16:27:09.6988 Info Torznab search in BitSearch => Found 18 releases [1002ms]
2025-08-01 16:27:09.7211 Info Torznab search in MoviesDVDR => Found 30 releases [637ms]
2025-08-01 16:27:09.8685 Info Torznab search in Elitetorrent-wf => Found 30 releases [1091ms]
2025-08-01 16:27:09.8765 Info Torznab search in LimeTorrents => Found 100 releases [874ms]
2025-08-01 16:27:10.1274 Info Torznab search in EZTV => Found 100 releases [1260ms]
2025-08-01 16:27:10.1705 Info Torznab search in eMuwarez (API) => Found 100 releases [1350ms]
2025-08-01 16:27:10.7271 Info Torznab search in LimeTorrents => Found 0 releases [0ms]
2025-08-01 16:27:12.0740 Info Torznab search in Frozen Layer => Found 47 releases [3164ms]
2025-08-01 16:27:12.2865 Info Torznab search in MejorTorrent => Found 6 releases [3243ms]
2025-08-01 16:27:12.7205 Info Torznab search in DivxTotal => Found 100 releases [3984ms]
2025-08-01 16:27:13.7177 Info Torznab search in Wolfmax 4k => Found 100 releases [4587ms]
2025-08-01 16:27:13.7500 Info Torznab search in Wolfmax 4k => Found 0 releases [0ms]
2025-08-01 16:27:15.6212 Info Torznab search in 1337x => Found 80 releases [6967ms]
2025-08-01 16:30:38.6602 Warn Some of the categories provided are unsupported in Elitetorrent-wf: 5010,5020,5030,5040,5045,5050,5060,5070,5080
2025-08-01 16:30:38.7008 Info Torznab search in ilCorSaRoNeRo => Found 15 releases (from cache) [0ms]
2025-08-01 16:30:38.7359 Error Jackett.Common.IndexerException: Exception (limetorrents): Name does not resolve

[v0.22.2217.0] Jackett.Common.IndexerException: Exception (limetorrents): Name does not resolve
 ---> System.Net.Http.HttpRequestException: Name does not resolve (www.limetorrents.lol:443)
 ---> System.Net.Sockets.SocketException (0xFFFDFFFF): Name does not resolve
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.DecompressionHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at FlareSolverrSharp.ClearanceHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
   at Jackett.Common.Utils.Clients.HttpWebClient2.Run(WebRequest webRequest) in ./Jackett.Common/Utils/Clients/HttpWebClient2.cs:line 180
   at Jackett.Common.Utils.Clients.WebClient.GetResultAsync(WebRequest request) in ./Jackett.Common/Utils/Clients/WebClient.cs:line 188
   at Jackett.Common.Indexers.BaseWebIndexer.RequestWithCookiesAsync(String url, String cookieOverride, RequestType method, String referer, IEnumerable`1 data, Dictionary`2 headers, String rawbody, Nullable`1 emulateBrowser) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 617
   at Jackett.Common.Indexers.Definitions.CardigannIndexer.PerformQuery(TorznabQuery query) in ./Jackett.Common/Indexers/Definitions/CardigannIndexer.cs:line 1563
   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 378
   --- End of inner exception stack trace ---
   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 403
   at Jackett.Common.Indexers.BaseWebIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 816
   at Jackett.Server.Controllers.ResultsController.Torznab(TorznabRequest request) in ./Jackett.Server/Controllers/ResultsController.cs:line 437

2025-08-01 16:30:38.8163 Info Torznab search in BitSearch => Found 18 releases [159ms]
2025-08-01 16:30:38.9110 Info Torznab search in BitSearch => Found 18 releases [271ms]
2025-08-01 16:30:38.9370 Info Torznab search in 0Magnet => Found 98 releases [324ms]
2025-08-01 16:30:39.4693 Info Torznab search in Elitetorrent-wf => Found 18 releases [809ms]
2025-08-01 16:30:39.5184 Info Torznab search in EZTV => Found 100 releases [833ms]
2025-08-01 16:30:39.6082 Info Torznab search in TorrentGalaxyClone => Found 50 releases [746ms]
2025-08-01 16:30:39.6810 Info Torznab search in eMuwarez (API) => Found 100 releases [1013ms]
2025-08-01 16:30:41.6155 Info Torznab search in Frozen Layer => Found 47 releases [2926ms]
2025-08-01 16:30:41.8978 Info Torznab search in MejorTorrent => Found 6 releases [3140ms]
2025-08-01 16:30:44.0070 Info Torznab search in Wolfmax 4k => Found 100 releases [5184ms]
2025-08-01 16:30:44.0491 Info Torznab search in Wolfmax 4k => Found 0 releases [0ms]
2025-08-01 16:30:45.2576 Info Torznab search in 1337x => Found 80 releases [6634ms]
2025-08-01 16:46:38.7374 Info Torznab search in 0Magnet => Found 98 releases (from cache) [2ms]
2025-08-01 16:46:38.7472 Info Torznab search in 1337x => Found 80 releases (from cache) [1ms]
2025-08-01 16:46:38.7619 Info Torznab search in BitSearch => Found 18 releases (from cache) [0ms]
2025-08-01 16:46:38.7894 Info Torznab search in BitSearch => Found 18 releases (from cache) [0ms]
2025-08-01 16:46:38.7991 Warn Some of the categories provided are unsupported in Elitetorrent-wf: 5010,5020,5030,5040,5045,5050,5060,5070,5080
2025-08-01 16:46:38.8094 Info Torznab search in Elitetorrent-wf => Found 18 releases (from cache) [10ms]
2025-08-01 16:46:38.8177 Info Torznab search in eMuwarez (API) => Found 100 releases (from cache) [7ms]
2025-08-01 16:46:38.8206 Info Torznab search in EZTV => Found 100 releases (from cache) [0ms]
2025-08-01 16:46:38.8312 Info Torznab search in Frozen Layer => Found 47 releases (from cache) [1ms]
2025-08-01 16:46:38.8492 Info Torznab search in ilCorSaRoNeRo => Found 15 releases (from cache) [0ms]
2025-08-01 16:46:38.9354 Info Torznab search in MejorTorrent => Found 6 releases (from cache) [0ms]
2025-08-01 16:46:38.9368 Info Torznab search in TorrentGalaxyClone => Found 50 releases (from cache) [1ms]
2025-08-01 16:46:38.9403 Info Torznab search in Wolfmax 4k => Found 100 releases (from cache) [3ms]
2025-08-01 16:46:38.9708 Error Jackett.Common.IndexerException: Exception (limetorrents): Name does not resolve

[v0.22.2217.0] Jackett.Common.IndexerException: Exception (limetorrents): Name does not resolve
 ---> System.Net.Http.HttpRequestException: Name does not resolve (www.limetorrents.lol:443)
 ---> System.Net.Sockets.SocketException (0xFFFDFFFF): Name does not resolve
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.DecompressionHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at FlareSolverrSharp.ClearanceHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
   at Jackett.Common.Utils.Clients.HttpWebClient2.Run(WebRequest webRequest) in ./Jackett.Common/Utils/Clients/HttpWebClient2.cs:line 180
   at Jackett.Common.Utils.Clients.WebClient.GetResultAsync(WebRequest request) in ./Jackett.Common/Utils/Clients/WebClient.cs:line 188
   at Jackett.Common.Indexers.BaseWebIndexer.RequestWithCookiesAsync(String url, String cookieOverride, RequestType method, String referer, IEnumerable`1 data, Dictionary`2 headers, String rawbody, Nullable`1 emulateBrowser) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 617
   at Jackett.Common.Indexers.Definitions.CardigannIndexer.PerformQuery(TorznabQuery query) in ./Jackett.Common/Indexers/Definitions/CardigannIndexer.cs:line 1563
   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 378
   --- End of inner exception stack trace ---
   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 403
   at Jackett.Common.Indexers.BaseWebIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 816
   at Jackett.Server.Controllers.ResultsController.Torznab(TorznabRequest request) in ./Jackett.Server/Controllers/ResultsController.cs:line 437

2025-08-01 16:58:08.7347 Info Torznab search in 1337x => Found 80 releases (from cache) [2ms]
2025-08-01 16:58:08.7420 Info Torznab search in BitSearch => Found 18 releases (from cache) [0ms]
2025-08-01 16:58:08.7544 Info Torznab search in DivxTotal => Found 100 releases (from cache) [2ms]
2025-08-01 16:58:08.7687 Info Torznab search in Elitetorrent-wf => Found 30 releases (from cache) [1ms]
2025-08-01 16:58:08.7814 Info Torznab search in eMuwarez (API) => Found 100 releases (from cache) [4ms]
2025-08-01 16:58:08.8912 Info Torznab search in EZTV => Found 100 releases (from cache) [0ms]
2025-08-01 16:58:08.9021 Info Torznab search in Frozen Layer => Found 47 releases (from cache) [1ms]
2025-08-01 16:58:08.9858 Info Torznab search in LimeTorrents => Found 100 releases (from cache) [2ms]
2025-08-01 16:58:08.9939 Info Torznab search in MejorTorrent => Found 6 releases (from cache) [0ms]
2025-08-01 16:58:09.0085 Info Torznab search in MoviesDVDR => Found 30 releases (from cache) [1ms]
2025-08-01 16:58:09.0251 Info Torznab search in Wolfmax 4k => Found 100 releases (from cache) [4ms]
2025-08-01 16:58:09.5025 Info Torznab search in ilCorSaRoNeRo => Found 15 releases [570ms]
2025-08-01 16:58:10.9703 Info Torznab search in Wolfmax 4k => Found 0 releases [0ms]
2025-08-01 17:06:13.9515 Warn Some of the categories provided are unsupported in Elitetorrent-wf: 5010,5020,5030,5040,5045,5050,5060,5070,5080
2025-08-01 17:07:09.8166 Info Torznab search in BitSearch => Found 19 releases [58608ms]
2025-08-01 17:07:10.1037 Info Torznab search in Elitetorrent-wf => Found 18 releases [59050ms]
2025-08-01 17:07:10.2191 Info Torznab search in LimeTorrents => Found 100 releases [59389ms]
2025-08-01 17:07:10.2191 Info Torznab search in ilCorSaRoNeRo => Found 15 releases [59522ms]
2025-08-01 17:07:10.3228 Info Torznab search in 0Magnet => Found 98 releases [59270ms]
2025-08-01 17:07:10.3707 Info Torznab search in BitSearch => Found 19 releases [59185ms]
2025-08-01 17:07:11.1587 Info Torznab search in eMuwarez (API) => Found 100 releases [60541ms]
2025-08-01 17:07:11.2687 Info Torznab search in Frozen Layer => Found 47 releases [60339ms]
2025-08-01 17:07:15.9374 Info Torznab search in Wolfmax 4k => Found 100 releases [63366ms]
2025-08-01 17:07:20.6639 Info Torznab search in EZTV => Found 100 releases [70101ms]
2025-08-01 17:07:20.8136 Info Torznab search in TorrentGalaxyClone => Found 50 releases [7447ms]
2025-08-01 17:07:20.9482 Info Torznab search in Wolfmax 4k => Found 0 releases [0ms]
2025-08-01 17:07:20.9617 Info Torznab search in LimeTorrents => Found 0 releases [0ms]
2025-08-01 17:07:21.3045 Info Torznab search in 1337x => Found 80 releases [70752ms]
2025-08-01 17:07:25.4985 Info Torznab search in MejorTorrent => Found 12 releases [72957ms]
2025-08-01 17:23:27.4047 Info Torznab search in 0Magnet => Found 98 releases (from cache) [2ms]
2025-08-01 17:23:27.4720 Info Torznab search in 1337x => Found 80 releases (from cache) [59ms]
2025-08-01 17:23:27.4800 Info Torznab search in BitSearch => Found 19 releases (from cache) [0ms]
2025-08-01 17:23:27.4880 Info Torznab search in BitSearch => Found 19 releases (from cache) [0ms]
2025-08-01 17:23:27.4976 Warn Some of the categories provided are unsupported in Elitetorrent-wf: 5010,5020,5030,5040,5045,5050,5060,5070,5080
2025-08-01 17:23:27.5071 Info Torznab search in Elitetorrent-wf => Found 18 releases (from cache) [9ms]
2025-08-01 17:23:27.5229 Info Torznab search in eMuwarez (API) => Found 100 releases (from cache) [7ms]
2025-08-01 17:23:27.5877 Info Torznab search in EZTV => Found 100 releases (from cache) [0ms]
2025-08-01 17:23:27.6380 Info Torznab search in Frozen Layer => Found 47 releases (from cache) [1ms]
2025-08-01 17:23:27.7668 Info Torznab search in ilCorSaRoNeRo => Found 15 releases (from cache) [0ms]
2025-08-01 17:23:27.8108 Info Torznab search in LimeTorrents => Found 100 releases (from cache) [2ms]
2025-08-01 17:23:27.8169 Info Torznab search in MejorTorrent => Found 12 releases (from cache) [0ms]
2025-08-01 17:23:27.8266 Info Torznab search in Wolfmax 4k => Found 100 releases (from cache) [2ms]
2025-08-01 17:23:27.8532 Info Torznab search in TorrentGalaxyClone => Found 50 releases (from cache) [2ms]
2025-08-01 17:28:56.2324 Info Torznab search in ilCorSaRoNeRo => Found 15 releases (from cache) [5ms]
2025-08-01 17:28:56.5041 Info Torznab search in BitSearch => Found 19 releases [622ms]
2025-08-01 17:28:57.4345 Info Torznab search in Elitetorrent-wf => Found 30 releases [1512ms]
2025-08-01 17:28:58.0373 Error Jackett.Common.IndexerException: Exception (limetorrents): Name does not resolve

[v0.22.2217.0] Jackett.Common.IndexerException: Exception (limetorrents): Name does not resolve
 ---> System.Net.Http.HttpRequestException: Name does not resolve (www.limetorrents.lol:443)
 ---> System.Net.Sockets.SocketException (0xFFFDFFFF): Name does not resolve
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.DecompressionHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at FlareSolverrSharp.ClearanceHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
   at Jackett.Common.Utils.Clients.HttpWebClient2.Run(WebRequest webRequest) in ./Jackett.Common/Utils/Clients/HttpWebClient2.cs:line 180
   at Jackett.Common.Utils.Clients.WebClient.GetResultAsync(WebRequest request) in ./Jackett.Common/Utils/Clients/WebClient.cs:line 188
   at Jackett.Common.Indexers.BaseWebIndexer.RequestWithCookiesAsync(String url, String cookieOverride, RequestType method, String referer, IEnumerable`1 data, Dictionary`2 headers, String rawbody, Nullable`1 emulateBrowser) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 617
   at Jackett.Common.Indexers.Definitions.CardigannIndexer.PerformQuery(TorznabQuery query) in ./Jackett.Common/Indexers/Definitions/CardigannIndexer.cs:line 1563
   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 378
   --- End of inner exception stack trace ---
   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 403
   at Jackett.Common.Indexers.BaseWebIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 816
   at Jackett.Server.Controllers.ResultsController.Torznab(TorznabRequest request) in ./Jackett.Server/Controllers/ResultsController.cs:line 437

2025-08-01 17:29:00.0272 Info Torznab search in MoviesDVDR => Found 30 releases [3767ms]
2025-08-01 17:29:00.7701 Info Torznab search in EZTV => Found 100 releases [4540ms]
2025-08-01 17:29:02.9216 Info Torznab search in eMuwarez (API) => Found 100 releases [6982ms]
2025-08-01 17:29:05.8955 Info Torznab search in Frozen Layer => Found 47 releases [9670ms]
2025-08-01 17:29:07.7133 Info Torznab search in DivxTotal => Found 100 releases [11821ms]
2025-08-01 17:29:07.7976 Info Torznab search in 1337x => Found 80 releases [11923ms]
2025-08-01 17:29:07.8923 Info Torznab search in Wolfmax 4k => Found 100 releases [11624ms]
2025-08-01 17:29:08.1497 Info Torznab search in Wolfmax 4k => Found 0 releases [0ms]
2025-08-01 17:29:09.1469 Info Torznab search in MejorTorrent => Found 12 releases [12907ms]
2025-08-01 17:39:27.5973 Info Torznab search in 0Magnet => Found 98 releases (from cache) [6ms]
2025-08-01 17:39:27.6116 Info Torznab search in BitSearch => Found 19 releases (from cache) [2ms]
2025-08-01 17:39:27.6143 Info Torznab search in BitSearch => Found 19 releases (from cache) [0ms]
2025-08-01 17:39:27.6143 Info Torznab search in 1337x => Found 80 releases (from cache) [6ms]
2025-08-01 17:39:27.7794 Warn Some of the categories provided are unsupported in Elitetorrent-wf: 5010,5020,5030,5040,5045,5050,5060,5070,5080
2025-08-01 17:39:27.8570 Info Torznab search in Elitetorrent-wf => Found 18 releases (from cache) [79ms]
2025-08-01 17:39:27.8508 Info Torznab search in EZTV => Found 100 releases (from cache) [0ms]
2025-08-01 17:39:27.8545 Info Torznab search in Frozen Layer => Found 47 releases (from cache) [2ms]
2025-08-01 17:39:27.8247 Info Torznab search in eMuwarez (API) => Found 100 releases (from cache) [39ms]
2025-08-01 17:39:27.8704 Info Torznab search in ilCorSaRoNeRo => Found 15 releases (from cache) [1ms]
2025-08-01 17:39:28.3067 Info Torznab search in LimeTorrents => Found 100 releases (from cache) [8ms]
2025-08-01 17:39:29.8176 Info Torznab search in MejorTorrent => Found 12 releases (from cache) [112ms]
2025-08-01 17:39:30.0979 Info Torznab search in TorrentGalaxyClone => Found 50 releases (from cache) [1ms]
2025-08-01 17:39:30.1072 Info Torznab search in Wolfmax 4k => Found 100 releases (from cache) [2ms]
2025-08-01 17:57:02.8005 Info Torznab search in 0Magnet => Found 98 releases [56063ms]
2025-08-01 17:57:12.4979 Info Torznab search in BitSearch => Found 19 releases [4932ms]
2025-08-01 17:57:15.4358 Warn Some of the categories provided are unsupported in Elitetorrent-wf: 5010,5020,5030,5040,5045,5050,5060,5070,5080
2025-08-01 17:57:16.0921 Info Torznab search in BitSearch => Found 19 releases [4213ms]
2025-08-01 17:57:20.7834 Info Torznab search in ilCorSaRoNeRo => Found 15 releases [3197ms]
2025-08-01 17:57:47.5351 Info Torznab search in Elitetorrent-wf => Found 18 releases [32102ms]
2025-08-01 17:57:53.8301 Info Torznab search in Frozen Layer => Found 47 releases [38002ms]
2025-08-01 17:57:54.6311 Info Torznab search in LimeTorrents => Found 100 releases [37004ms]
2025-08-01 17:57:55.5793 Info Torznab search in TorrentGalaxyClone => Found 50 releases [30148ms]
2025-08-01 17:58:11.9524 Info Torznab search in eMuwarez (API) => Found 100 releases [58519ms]
2025-08-01 17:58:13.4579 Info Torznab search in EZTV => Found 100 releases [59985ms]
2025-08-01 17:58:14.9397 Info Torznab search in LimeTorrents => Found 0 releases [0ms]
2025-08-01 17:58:15.1331 Info Torznab search in 1337x => Found 80 releases [116780ms]
2025-08-01 17:59:08.0495 Info Torznab search in Wolfmax 4k => Found 100 releases [107083ms]
2025-08-01 17:59:06.8505 Warn As of "08/01/2025 15:57:47 +00:00", the heartbeat has been running for "00:00:13.3078272" which is longer than "00:00:01". This could be caused by thread pool starvation.
2025-08-01 17:59:26.3973 Info Torznab search in MejorTorrent => Found 12 releases [125823ms]
2025-08-01 18:00:06.2773 Info Torznab search in 1337x => Found 80 releases (from cache) [2ms]
2025-08-01 18:00:06.2897 Info Torznab search in BitSearch => Found 19 releases (from cache) [0ms]
2025-08-01 18:00:06.4580 Info Torznab search in DivxTotal => Found 100 releases (from cache) [2ms]
2025-08-01 18:00:06.7448 Info Torznab search in Elitetorrent-wf => Found 30 releases (from cache) [1ms]
2025-08-01 18:00:08.7691 Info Torznab search in eMuwarez (API) => Found 100 releases (from cache) [4ms]
2025-08-01 18:00:09.3169 Info Torznab search in EZTV => Found 100 releases (from cache) [0ms]
2025-08-01 18:00:09.3169 Info Torznab search in Frozen Layer => Found 47 releases (from cache) [1ms]
2025-08-01 18:00:10.2157 Info Torznab search in ilCorSaRoNeRo => Found 15 releases [309ms]
2025-08-01 18:00:10.3025 Info Torznab search in LimeTorrents => Found 100 releases [197ms]
2025-08-01 18:00:10.3075 Info Torznab search in MejorTorrent => Found 12 releases (from cache) [0ms]
2025-08-01 18:00:10.3103 Info Torznab search in Wolfmax 4k => Found 100 releases (from cache) [2ms]
2025-08-01 18:00:10.3186 Info Torznab search in MoviesDVDR => Found 30 releases (from cache) [7ms]
2025-08-01 18:00:12.1215 Info Torznab search in LimeTorrents => Found 0 releases [0ms]
2025-08-01 18:00:12.1909 Info Torznab search in Wolfmax 4k => Found 0 releases [0ms]
2025-08-01 18:15:21.6124 Info Torznab search in 0Magnet => Found 98 releases (from cache) [3ms]
2025-08-01 18:15:21.6372 Info Torznab search in 1337x => Found 80 releases (from cache) [2ms]
2025-08-01 18:15:21.6717 Info Torznab search in BitSearch => Found 19 releases (from cache) [0ms]
2025-08-01 18:15:21.7561 Info Torznab search in BitSearch => Found 19 releases (from cache) [0ms]
2025-08-01 18:15:22.0495 Info Torznab search in eMuwarez (API) => Found 100 releases (from cache) [6ms]
2025-08-01 18:15:22.0602 Warn Some of the categories provided are unsupported in Elitetorrent-wf: 5010,5020,5030,5040,5045,5050,5060,5070,5080
2025-08-01 18:15:22.0747 Info Torznab search in MejorTorrent => Found 12 releases (from cache) [0ms]
2025-08-01 18:15:22.0670 Info Torznab search in EZTV => Found 100 releases (from cache) [1ms]
2025-08-01 18:15:22.0697 Info Torznab search in Frozen Layer => Found 47 releases (from cache) [2ms]
2025-08-01 18:15:22.0647 Info Torznab search in ilCorSaRoNeRo => Found 15 releases (from cache) [0ms]
2025-08-01 18:15:22.0778 Info Torznab search in Elitetorrent-wf => Found 18 releases (from cache) [17ms]
2025-08-01 18:15:22.0778 Info Torznab search in LimeTorrents => Found 100 releases (from cache) [14ms]
2025-08-01 18:15:22.0925 Info Torznab search in Wolfmax 4k => Found 100 releases (from cache) [3ms]
2025-08-01 18:15:22.6106 Info Torznab search in TorrentGalaxyClone => Found 50 releases (from cache) [2ms]
2025-08-01 18:15:24.0995 Info Torznab search in Wolfmax 4k => Found 0 releases [0ms]
2025-08-01 18:31:07.3077 Info Torznab search in ilCorSaRoNeRo => Found 15 releases (from cache) [0ms]
2025-08-01 18:31:07.3377 Info Torznab search in LimeTorrents => Found 100 releases (from cache) [24ms]
2025-08-01 18:31:07.8141 Warn [bitsearch] Invalid category for value: 'Ebook/Course'
2025-08-01 18:31:07.8453 Warn [bitsearch] Invalid Release: 'https://bitsearch.to/torrent/688ce4ad7f76a4d5fa10a0c6'. No categories provided.
2025-08-01 18:31:07.8898 Info Torznab search in BitSearch => Found 18 releases [802ms]
2025-08-01 18:31:08.2091 Info Torznab search in MoviesDVDR => Found 30 releases [805ms]
2025-08-01 18:31:08.2462 Info Torznab search in Elitetorrent-wf => Found 30 releases [1023ms]
2025-08-01 18:31:08.6207 Info Torznab search in eMuwarez (API) => Found 100 releases [1378ms]
2025-08-01 18:31:09.1479 Info Torznab search in EZTV => Found 100 releases [1889ms]
2025-08-01 18:31:10.5780 Info Torznab search in Frozen Layer => Found 47 releases [3302ms]
2025-08-01 18:31:11.2361 Info Torznab search in DivxTotal => Found 100 releases [4042ms]
2025-08-01 18:31:12.4780 Info Torznab search in Wolfmax 4k => Found 100 releases [5051ms]
2025-08-01 18:31:13.3241 Info Torznab search in Wolfmax 4k => Found 0 releases [0ms]
2025-08-01 18:31:13.7944 Info Torznab search in 1337x => Found 80 releases [6826ms]
2025-08-01 18:31:15.0995 Info Torznab search in MejorTorrent => Found 12 releases [7715ms]
2025-08-01 18:31:51.8668 Info Torznab search in 0Magnet => Found 98 releases (from cache) [4ms]
2025-08-01 18:31:51.8807 Info Torznab search in 1337x => Found 80 releases (from cache) [2ms]
2025-08-01 18:31:51.9148 Info Torznab search in BitSearch => Found 19 releases (from cache) [1ms]
2025-08-01 18:31:51.9510 Warn Some of the categories provided are unsupported in Elitetorrent-wf: 5010,5020,5030,5040,5045,5050,5060,5070,5080
2025-08-01 18:31:51.9599 Info Torznab search in Elitetorrent-wf => Found 18 releases (from cache) [8ms]
2025-08-01 18:31:51.9599 Info Torznab search in BitSearch => Found 19 releases (from cache) [0ms]
2025-08-01 18:31:51.9835 Info Torznab search in eMuwarez (API) => Found 100 releases (from cache) [5ms]
2025-08-01 18:31:51.9934 Info Torznab search in EZTV => Found 100 releases (from cache) [0ms]
2025-08-01 18:31:52.0038 Info Torznab search in Frozen Layer => Found 47 releases (from cache) [1ms]
2025-08-01 18:31:52.0499 Info Torznab search in ilCorSaRoNeRo => Found 15 releases (from cache) [0ms]
2025-08-01 18:31:52.0630 Info Torznab search in LimeTorrents => Found 100 releases (from cache) [2ms]
2025-08-01 18:31:52.0630 Info Torznab search in MejorTorrent => Found 12 releases (from cache) [1ms]
2025-08-01 18:31:52.1223 Info Torznab search in Wolfmax 4k => Found 100 releases (from cache) [2ms]
2025-08-01 18:31:52.1415 Info Torznab search in TorrentGalaxyClone => Found 50 releases (from cache) [1ms]
