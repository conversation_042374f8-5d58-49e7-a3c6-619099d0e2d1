2025-08-01 18:00:52.4|Debug|DownloadDecisionMaker|Processing release 'Suenos de libertad  S13E66 [HDTV] SPANISH' from 'Wolfmax'
2025-08-01 18:00:53.0|Debug|<PERSON><PERSON><PERSON>|Parsing string 'Sue<PERSON> de libertad  S13E66 [HDTV] SPANISH'
2025-08-01 18:00:53.1|Debug|Parse<PERSON>|Unable to parse Suenos de libertad  S13E66 [HDTV] SPANISH
2025-08-01 18:00:53.1|Debug|DownloadDecisionMaker|Processing release 'La promesa  S16E44 [HDTV] SPANISH' from 'Wolfmax'
2025-08-01 18:00:53.1|Debug|Parser|Parsing string 'La promesa  S16E44 [HDTV] SPANISH'
2025-08-01 18:00:53.1|Debug|Parser|Unable to parse La promesa  S16E44 [HDTV] SPANISH
2025-08-01 18:00:53.1|Debug|DownloadDecisionMaker|Processing release '<PERSON><PERSON>  S06E08 [HDTV 720p AC3 5.1] SPANISH' from 'Wolfmax'
2025-08-01 18:00:53.1|Debug|Parser|Parsing string 'R<PERSON>  S06E08 [HDTV 720p AC3 5.1] SPANISH'
2025-08-01 18:00:53.1|Debug|Parser|Unable to parse Rocco  S06E08 [HDTV 720p AC3 5.1] SPANISH
2025-08-01 18:00:53.1|Debug|DownloadDecisionMaker|Processing release 'Rocco  S06E07 [HDTV 720p AC3 5.1] SPANISH' from 'Wolfmax'
2025-08-01 18:00:53.1|Debug|Parser|Parsing string 'Rocco  S06E07 [HDTV 720p AC3 5.1] SPANISH'
2025-08-01 18:00:53.1|Debug|Parser|Unable to parse Rocco  S06E07 [HDTV 720p AC3 5.1] SPANISH
2025-08-01 18:00:53.1|Debug|DownloadDecisionMaker|Processing release 'Miley Cyrus Something Beautiful  [HDTV 1080p AC3 5.1] SPANISH' from 'Wolfmax'
2025-08-01 18:00:53.1|Debug|Parser|Parsing string 'Miley Cyrus Something Beautiful  [HDTV 1080p AC3 5.1] SPANISH'
2025-08-01 18:00:53.2|Debug|Parser|Unable to parse Miley Cyrus Something Beautiful  [HDTV 1080p AC3 5.1] SPANISH
2025-08-01 18:00:53.2|Debug|DownloadDecisionMaker|Processing release 'La cuenta atras  S01E08 [HDTV 1080p AC3 5.1] SPANISH' from 'Wolfmax'
2025-08-01 18:00:53.2|Debug|Parser|Parsing string 'La cuenta atras  S01E08 [HDTV 1080p AC3 5.1] SPANISH'
2025-08-01 18:00:53.2|Debug|Parser|Unable to parse La cuenta atras  S01E08 [HDTV 1080p AC3 5.1] SPANISH
2025-08-01 18:00:53.2|Debug|DownloadDecisionMaker|Processing release 'La cuenta atras  S01E08 [HDTV 720p AC3 5.1] SPANISH' from 'Wolfmax'
2025-08-01 18:00:53.2|Debug|Parser|Parsing string 'La cuenta atras  S01E08 [HDTV 720p AC3 5.1] SPANISH'
2025-08-01 18:00:53.2|Debug|Parser|Unable to parse La cuenta atras  S01E08 [HDTV 720p AC3 5.1] SPANISH
2025-08-01 18:00:53.2|Debug|DownloadDecisionMaker|Processing release 'La Cuenta Atras  S01E08 [WEBRip-2160p] SPANISH' from 'Wolfmax'
2025-08-01 18:00:53.2|Debug|Parser|Parsing string 'La Cuenta Atras  S01E08 [WEBRip-2160p] SPANISH'
2025-08-01 18:00:53.2|Debug|Parser|Unable to parse La Cuenta Atras  S01E08 [WEBRip-2160p] SPANISH
2025-08-01 18:00:53.2|Debug|DownloadDecisionMaker|Processing release 'Sin el (2024)  [BLuRayRip] SPANISH' from 'Wolfmax'
2025-08-01 18:00:53.2|Debug|Parser|Parsing string 'Sin el (2024)  [BLuRayRip] SPANISH'
2025-08-01 18:00:53.3|Debug|Parser|Movie Parsed. Sin el - 2024 
2025-08-01 18:00:53.3|Debug|Parser|Release Group parsed: 
2025-08-01 18:00:53.3|Debug|Parser|Languages parsed: Spanish
2025-08-01 18:00:53.3|Debug|QualityParser|Trying to parse quality for 'Sin el (2024)  [BLuRayRip] SPANISH'
2025-08-01 18:00:53.3|Debug|Parser|Quality parsed: Unknown v1
2025-08-01 18:00:53.3|Debug|Parser|Edition parsed: 
2025-08-01 18:00:53.3|Debug|ParsingService|No matching movie for titles 'Sin el (2024)'
2025-08-01 18:00:53.3|Debug|DownloadDecisionMaker|Release 'Sin el (2024)  [BLuRayRip] SPANISH' from 'Wolfmax' rejected for the following reasons: [Permanent] Unknown Movie. Unable to identify correct movie using release name.
2025-08-01 18:00:53.3|Debug|DownloadDecisionMaker|Processing release 'Sin el (2024)  [BluRay 720p] SPANISH' from 'Wolfmax'
2025-08-01 18:00:53.3|Debug|Parser|Parsing string 'Sin el (2024)  [BluRay 720p] SPANISH'
2025-08-01 18:00:53.3|Debug|Parser|Movie Parsed. Sin el - 2024 
2025-08-01 18:00:53.3|Debug|Parser|Release Group parsed: 
2025-08-01 18:00:53.3|Debug|Parser|Languages parsed: Spanish
2025-08-01 18:00:53.3|Debug|QualityParser|Trying to parse quality for 'Sin el (2024)  [BluRay 720p] SPANISH'
2025-08-01 18:00:53.4|Debug|Parser|Quality parsed: Bluray-720p v1
2025-08-01 18:00:53.4|Debug|Parser|Edition parsed: 
2025-08-01 18:00:53.4|Debug|ParsingService|No matching movie for titles 'Sin el (2024)'
2025-08-01 18:00:53.4|Debug|DownloadDecisionMaker|Release 'Sin el (2024)  [BluRay 720p] SPANISH' from 'Wolfmax' rejected for the following reasons: [Permanent] Unknown Movie. Unable to identify correct movie using release name.
2025-08-01 18:00:53.4|Debug|DownloadDecisionMaker|Processing release 'M3GAN 2.0 (2025)  [BluRay 1080p] SPANISH' from 'Wolfmax'
2025-08-01 18:00:53.4|Debug|Parser|Parsing string 'M3GAN 2.0 (2025)  [BluRay 1080p] SPANISH'
2025-08-01 18:00:53.4|Debug|Parser|Movie Parsed. M3GAN 2 0 - 2025 
2025-08-01 18:00:53.4|Debug|Parser|Release Group parsed: 
2025-08-01 18:00:53.4|Debug|Parser|Languages parsed: Spanish
2025-08-01 18:00:53.4|Debug|QualityParser|Trying to parse quality for 'M3GAN 2.0 (2025)  [BluRay 1080p] SPANISH'
2025-08-01 18:00:53.4|Debug|Parser|Quality parsed: Bluray-1080p v1
2025-08-01 18:00:53.4|Debug|Parser|Edition parsed: 
2025-08-01 18:00:53.4|Debug|ParsingService|No matching movie for titles 'M3GAN 2 0 (2025)'
2025-08-01 18:00:53.4|Debug|DownloadDecisionMaker|Release 'M3GAN 2.0 (2025)  [BluRay 1080p] SPANISH' from 'Wolfmax' rejected for the following reasons: [Permanent] Unknown Movie. Unable to identify correct movie using release name.
2025-08-01 18:00:53.5|Debug|DownloadDecisionMaker|Processing release 'M3GAN 2.0 (2025)  [BLuRayRip] SPANISH' from 'Wolfmax'
2025-08-01 18:00:53.5|Debug|Parser|Parsing string 'M3GAN 2.0 (2025)  [BLuRayRip] SPANISH'
2025-08-01 18:00:53.5|Debug|Parser|Movie Parsed. M3GAN 2 0 - 2025 
2025-08-01 18:00:53.5|Debug|Parser|Release Group parsed: 
2025-08-01 18:00:53.5|Debug|Parser|Languages parsed: Spanish
2025-08-01 18:00:53.5|Debug|QualityParser|Trying to parse quality for 'M3GAN 2.0 (2025)  [BLuRayRip] SPANISH'
2025-08-01 18:00:53.5|Debug|Parser|Quality parsed: Unknown v1
2025-08-01 18:00:53.5|Debug|Parser|Edition parsed: 
2025-08-01 18:00:53.5|Debug|ParsingService|No matching movie for titles 'M3GAN 2 0 (2025)'
2025-08-01 18:00:53.5|Debug|DownloadDecisionMaker|Release 'M3GAN 2.0 (2025)  [BLuRayRip] SPANISH' from 'Wolfmax' rejected for the following reasons: [Permanent] Unknown Movie. Unable to identify correct movie using release name.
2025-08-01 18:00:53.5|Debug|DownloadDecisionMaker|Processing release 'M3GAN 2.0 (2025)  [BluRay 720p] SPANISH' from 'Wolfmax'
2025-08-01 18:00:53.5|Debug|Parser|Parsing string 'M3GAN 2.0 (2025)  [BluRay 720p] SPANISH'
2025-08-01 18:00:53.5|Debug|Parser|Movie Parsed. M3GAN 2 0 - 2025 
2025-08-01 18:00:53.5|Debug|Parser|Release Group parsed: 
2025-08-01 18:00:53.5|Debug|Parser|Languages parsed: Spanish
2025-08-01 18:00:53.6|Debug|QualityParser|Trying to parse quality for 'M3GAN 2.0 (2025)  [BluRay 720p] SPANISH'
2025-08-01 18:00:53.6|Debug|Parser|Quality parsed: Bluray-720p v1
2025-08-01 18:00:53.6|Debug|Parser|Edition parsed: 
2025-08-01 18:00:53.6|Debug|ParsingService|No matching movie for titles 'M3GAN 2 0 (2025)'
2025-08-01 18:00:53.6|Debug|DownloadDecisionMaker|Release 'M3GAN 2.0 (2025)  [BluRay 720p] SPANISH' from 'Wolfmax' rejected for the following reasons: [Permanent] Unknown Movie. Unable to identify correct movie using release name.
2025-08-01 18:00:53.6|Debug|DownloadDecisionMaker|Processing release 'La huella del mal (2025)  [BLuRayRip] SPANISH' from 'Wolfmax'
2025-08-01 18:00:53.6|Debug|Parser|Parsing string 'La huella del mal (2025)  [BLuRayRip] SPANISH'
2025-08-01 18:00:53.6|Debug|Parser|Movie Parsed. La huella del mal - 2025 
2025-08-01 18:00:53.6|Debug|Parser|Release Group parsed: 
2025-08-01 18:00:53.6|Debug|Parser|Languages parsed: Spanish
2025-08-01 18:00:53.6|Debug|QualityParser|Trying to parse quality for 'La huella del mal (2025)  [BLuRayRip] SPANISH'
2025-08-01 18:00:53.6|Debug|Parser|Quality parsed: Unknown v1
2025-08-01 18:00:53.6|Debug|Parser|Edition parsed: 
2025-08-01 18:00:53.6|Debug|ParsingService|No matching movie for titles 'La huella del mal (2025)'
2025-08-01 18:00:53.6|Debug|DownloadDecisionMaker|Release 'La huella del mal (2025)  [BLuRayRip] SPANISH' from 'Wolfmax' rejected for the following reasons: [Permanent] Unknown Movie. Unable to identify correct movie using release name.
2025-08-01 18:00:53.7|Debug|DownloadDecisionMaker|Processing release 'La huella del mal (2025)  [BluRay 720p] SPANISH' from 'Wolfmax'
2025-08-01 18:00:53.7|Debug|Parser|Parsing string 'La huella del mal (2025)  [BluRay 720p] SPANISH'
2025-08-01 18:00:53.7|Debug|Parser|Movie Parsed. La huella del mal - 2025 
2025-08-01 18:00:53.7|Debug|Parser|Release Group parsed: 
2025-08-01 18:00:53.7|Debug|Parser|Languages parsed: Spanish
2025-08-01 18:00:53.7|Debug|QualityParser|Trying to parse quality for 'La huella del mal (2025)  [BluRay 720p] SPANISH'
2025-08-01 18:00:53.7|Debug|Parser|Quality parsed: Bluray-720p v1
2025-08-01 18:00:53.7|Debug|Parser|Edition parsed: 
2025-08-01 18:00:53.7|Debug|ParsingService|No matching movie for titles 'La huella del mal (2025)'
2025-08-01 18:00:53.7|Debug|DownloadDecisionMaker|Release 'La huella del mal (2025)  [BluRay 720p] SPANISH' from 'Wolfmax' rejected for the following reasons: [Permanent] Unknown Movie. Unable to identify correct movie using release name.
2025-08-01 18:00:53.7|Debug|DownloadDecisionMaker|Processing release 'Washington Black  S01E04 [HDTV] SPANISH' from 'Wolfmax'
2025-08-01 18:00:53.7|Debug|Parser|Parsing string 'Washington Black  S01E04 [HDTV] SPANISH'
2025-08-01 18:00:53.7|Debug|Parser|Unable to parse Washington Black  S01E04 [HDTV] SPANISH
2025-08-01 18:00:53.7|Debug|DownloadDecisionMaker|Processing release 'Rocco  S06E05 [HDTV] SPANISH' from 'Wolfmax'
2025-08-01 18:00:53.8|Debug|Parser|Parsing string 'Rocco  S06E05 [HDTV] SPANISH'
2025-08-01 18:00:53.8|Debug|Parser|Unable to parse Rocco  S06E05 [HDTV] SPANISH
2025-08-01 18:00:53.8|Debug|DownloadDecisionMaker|Processing release 'WWE Increible  S01E05 [HDTV 1080p AC3 5.1] SPANISH' from 'Wolfmax'
2025-08-01 18:00:53.8|Debug|Parser|Parsing string 'WWE Increible  S01E05 [HDTV 1080p AC3 5.1] SPANISH'
2025-08-01 18:00:53.8|Debug|Parser|Unable to parse WWE Increible  S01E05 [HDTV 1080p AC3 5.1] SPANISH
2025-08-01 18:00:53.8|Debug|DownloadDecisionMaker|Processing release 'WWE Increible  S01E04 [HDTV 1080p AC3 5.1] SPANISH' from 'Wolfmax'
2025-08-01 18:00:53.8|Debug|Parser|Parsing string 'WWE Increible  S01E04 [HDTV 1080p AC3 5.1] SPANISH'
2025-08-01 18:00:53.8|Debug|Parser|Unable to parse WWE Increible  S01E04 [HDTV 1080p AC3 5.1] SPANISH
2025-08-01 18:00:53.8|Debug|DownloadDecisionMaker|Processing release 'WWE Increible  S01E03 [HDTV 1080p AC3 5.1] SPANISH' from 'Wolfmax'
2025-08-01 18:00:53.8|Debug|Parser|Parsing string 'WWE Increible  S01E03 [HDTV 1080p AC3 5.1] SPANISH'
2025-08-01 18:00:53.8|Debug|Parser|Unable to parse WWE Increible  S01E03 [HDTV 1080p AC3 5.1] SPANISH
2025-08-01 18:00:53.9|Info|RssSyncService|RSS Sync Completed. Reports found: 733, Reports grabbed: 0
2025-08-01 18:00:53.9|Debug|PendingReleaseService|Removing failed releases from pending
2025-08-01 18:01:00.1|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:01:00.1|Debug|Api|[POST] /api/v3/command?apikey=(removed) 201.Created (9 ms)
2025-08-01 18:01:00.2|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:01:00.2|Debug|Api|[GET] /api/v3/queue?apikey=(removed)&includeEpisode=true: 200.OK (13 ms)
2025-08-01 18:01:22.1|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:01:23.0|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:01:23.1|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:01:23.1|Debug|Api|[GET] /api/v3/queue/status?apikey=(removed) 200.OK (100 ms)
2025-08-01 18:01:23.1|Debug|Api|[GET] /api/v3/queue/details?apikey=(removed) 200.OK (41 ms)
2025-08-01 18:01:23.7|Debug|Api|[GET] /api/v3/movie?apikey=(removed) 200.OK (1642 ms)
2025-08-01 18:01:49.7|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:01:49.8|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:01:49.8|Debug|Api|[GET] /api/v3/queue/status?apikey=(removed) 200.OK (6 ms)
2025-08-01 18:01:49.8|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:01:49.8|Debug|Api|[GET] /api/v3/queue/details?apikey=(removed) 200.OK (7 ms)
2025-08-01 18:01:50.1|Debug|Api|[GET] /api/v3/movie?apikey=(removed) 200.OK (384 ms)
2025-08-01 18:01:56.0|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:01:56.0|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:01:56.0|Debug|Api|[GET] /api/v3/queue/status?apikey=(removed) 200.OK (10 ms)
2025-08-01 18:01:56.0|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:01:56.0|Debug|Api|[GET] /api/v3/queue/details?apikey=(removed) 200.OK (10 ms)
2025-08-01 18:01:56.4|Debug|Api|[GET] /api/v3/movie?apikey=(removed) 200.OK (421 ms)
2025-08-01 18:02:00.0|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:02:00.1|Debug|Api|[POST] /api/v3/command?apikey=(removed) 201.Created (35 ms)
2025-08-01 18:02:00.1|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:02:00.1|Debug|Api|[GET] /api/v3/queue?apikey=(removed)&includeEpisode=true: 200.OK (26 ms)
2025-08-01 18:02:16.2|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:02:16.5|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:02:16.7|Debug|Api|[GET] /api/v3/queue/status?apikey=(removed) 200.OK (192 ms)
2025-08-01 18:02:16.8|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:02:16.9|Debug|Api|[GET] /api/v3/queue/details?apikey=(removed) 200.OK (38 ms)
2025-08-01 18:02:18.6|Debug|Api|[GET] /api/v3/movie?apikey=(removed) 200.OK (2394 ms)
2025-08-01 18:02:37.3|Debug|ImportListSyncService|No enabled import lists, skipping sync and cleaning
2025-08-01 18:03:00.1|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:03:00.1|Debug|Api|[POST] /api/v3/command?apikey=(removed) 201.Created (10 ms)
2025-08-01 18:03:00.1|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:03:00.1|Debug|Api|[GET] /api/v3/queue?apikey=(removed)&includeEpisode=true: 200.OK (9 ms)
2025-08-01 18:03:31.9|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:03:32.0|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:03:32.0|Debug|Api|[GET] /api/v3/queue/status?apikey=(removed) 200.OK (9 ms)
2025-08-01 18:03:32.4|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:03:32.6|Debug|Api|[GET] /api/v3/queue/details?apikey=(removed) 200.OK (196 ms)
2025-08-01 18:03:35.8|Debug|Api|[GET] /api/v3/movie?apikey=(removed) 200.OK (3889 ms)
2025-08-01 18:04:00.0|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:04:00.0|Debug|Api|[POST] /api/v3/command?apikey=(removed) 201.Created (19 ms)
2025-08-01 18:04:00.0|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:04:00.1|Debug|Api|[GET] /api/v3/queue?apikey=(removed)&includeEpisode=true: 200.OK (85 ms)
2025-08-01 18:05:00.0|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:05:00.0|Debug|Api|[POST] /api/v3/command?apikey=(removed) 201.Created (14 ms)
2025-08-01 18:05:00.0|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:05:00.0|Debug|Api|[GET] /api/v3/queue?apikey=(removed)&includeEpisode=true: 200.OK (9 ms)
2025-08-01 18:05:17.4|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:05:17.4|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:05:17.4|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:05:17.4|Debug|Api|[GET] /api/v3/queue/status?apikey=(removed) 200.OK (21 ms)
2025-08-01 18:05:17.4|Debug|Api|[GET] /api/v3/queue/details?apikey=(removed) 200.OK (21 ms)
2025-08-01 18:05:17.9|Debug|Api|[GET] /api/v3/movie?apikey=(removed) 200.OK (535 ms)
2025-08-01 18:05:33.9|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:05:33.9|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:05:34.0|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:05:34.4|Debug|Api|[GET] /api/v3/queue/status?apikey=(removed) 200.OK (495 ms)
2025-08-01 18:05:34.9|Debug|Api|[GET] /api/v3/queue/details?apikey=(removed) 200.OK (857 ms)
2025-08-01 18:05:36.7|Debug|Api|[GET] /api/v3/movie?apikey=(removed) 200.OK (2782 ms)
2025-08-01 18:06:00.0|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:06:00.0|Debug|Api|[POST] /api/v3/command?apikey=(removed) 201.Created (9 ms)
2025-08-01 18:06:00.0|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:06:00.0|Debug|Api|[GET] /api/v3/queue?apikey=(removed)&includeEpisode=true: 200.OK (10 ms)
2025-08-01 18:07:00.0|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:07:00.0|Debug|Api|[POST] /api/v3/command?apikey=(removed) 201.Created (10 ms)
2025-08-01 18:07:00.0|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:07:00.0|Debug|Api|[GET] /api/v3/queue?apikey=(removed)&includeEpisode=true: 200.OK (24 ms)
2025-08-01 18:08:00.0|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:08:00.0|Debug|Api|[POST] /api/v3/command?apikey=(removed) 201.Created (12 ms)
2025-08-01 18:08:00.0|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:08:00.0|Debug|Api|[GET] /api/v3/queue?apikey=(removed)&includeEpisode=true: 200.OK (11 ms)
2025-08-01 18:08:06.6|Debug|ImportListSyncService|No enabled import lists, skipping sync and cleaning
2025-08-01 18:08:36.4|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:08:36.5|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:08:36.5|Debug|Api|[GET] /api/v3/queue/status?apikey=(removed) 200.OK (16 ms)
2025-08-01 18:08:36.5|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:08:36.5|Debug|Api|[GET] /api/v3/queue/details?apikey=(removed) 200.OK (17 ms)
2025-08-01 18:08:39.9|Debug|Api|[GET] /api/v3/movie?apikey=(removed) 200.OK (3447 ms)
2025-08-01 18:08:40.5|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:08:40.5|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:08:40.6|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:08:40.6|Debug|Api|[GET] /api/v3/queue/status?apikey=(removed) 200.OK (31 ms)
2025-08-01 18:08:40.6|Debug|Api|[GET] /api/v3/queue/details?apikey=(removed) 200.OK (34 ms)
2025-08-01 18:08:46.3|Debug|Api|[GET] /api/v3/movie?apikey=(removed) 200.OK (5720 ms)
2025-08-01 18:08:48.6|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:08:48.6|Debug|Api|[GET] /api/v3/queue/status?apikey=(removed) 200.OK (10 ms)
2025-08-01 18:08:48.7|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:08:48.7|Debug|Api|[GET] /api/v3/queue/details?apikey=(removed) 200.OK (22 ms)
2025-08-01 18:09:00.0|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:09:00.0|Debug|Api|[POST] /api/v3/command?apikey=(removed) 201.Created (21 ms)
2025-08-01 18:09:00.0|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:09:00.0|Debug|Api|[GET] /api/v3/queue?apikey=(removed)&includeEpisode=true: 200.OK (11 ms)
2025-08-01 18:10:00.0|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:10:00.0|Debug|Api|[POST] /api/v3/command?apikey=(removed) 201.Created (13 ms)
2025-08-01 18:10:00.0|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:10:00.0|Debug|Api|[GET] /api/v3/queue?apikey=(removed)&includeEpisode=true: 200.OK (14 ms)
2025-08-01 18:11:00.0|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:11:00.0|Debug|Api|[POST] /api/v3/command?apikey=(removed) 201.Created (10 ms)
2025-08-01 18:11:00.0|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:11:00.0|Debug|Api|[GET] /api/v3/queue?apikey=(removed)&includeEpisode=true: 200.OK (15 ms)
2025-08-01 18:12:00.0|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:12:00.0|Debug|Api|[POST] /api/v3/command?apikey=(removed) 201.Created (11 ms)
2025-08-01 18:12:00.1|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:12:00.1|Debug|Api|[GET] /api/v3/queue?apikey=(removed)&includeEpisode=true: 200.OK (11 ms)
2025-08-01 18:12:22.0|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:12:22.0|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:12:22.0|Debug|Api|[GET] /api/v3/queue/status?apikey=(removed) 200.OK (12 ms)
2025-08-01 18:12:22.1|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:12:22.1|Debug|Api|[GET] /api/v3/queue/details?apikey=(removed) 200.OK (33 ms)
2025-08-01 18:12:23.1|Debug|Api|[GET] /api/v3/movie?apikey=(removed) 200.OK (1068 ms)
2025-08-01 18:12:23.4|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:12:23.6|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:12:23.7|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:12:23.7|Debug|Api|[GET] /api/v3/queue/status?apikey=(removed) 200.OK (22 ms)
2025-08-01 18:12:23.7|Debug|Api|[GET] /api/v3/queue/details?apikey=(removed) 200.OK (52 ms)
2025-08-01 18:12:24.7|Debug|Api|[GET] /api/v3/movie?apikey=(removed) 200.OK (1339 ms)
2025-08-01 18:12:32.9|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:12:33.0|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:12:33.0|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:12:33.0|Debug|Api|[GET] /api/v3/queue/status?apikey=(removed) 200.OK (11 ms)
2025-08-01 18:12:33.0|Debug|Api|[GET] /api/v3/queue/details?apikey=(removed) 200.OK (22 ms)
2025-08-01 18:12:33.7|Debug|Api|[GET] /api/v3/movie?apikey=(removed) 200.OK (843 ms)
2025-08-01 18:12:35.8|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:12:35.8|Debug|Api|[GET] /api/v3/queue/status?apikey=(removed) 200.OK (9 ms)
2025-08-01 18:12:36.3|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:12:36.4|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:12:36.4|Debug|Api|[GET] /api/v3/queue/details?apikey=(removed) 200.OK (34 ms)
2025-08-01 18:12:36.7|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:12:36.7|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:12:36.7|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:12:36.8|Debug|Api|[GET] /api/v3/queue/status?apikey=(removed) 200.OK (56 ms)
2025-08-01 18:12:36.8|Debug|Api|[GET] /api/v3/queue/details?apikey=(removed) 200.OK (64 ms)
2025-08-01 18:12:37.1|Debug|Api|[GET] /api/v3/movie?apikey=(removed) 200.OK (819 ms)
2025-08-01 18:12:37.4|Debug|Api|[GET] /api/v3/movie?apikey=(removed) 200.OK (698 ms)
2025-08-01 18:12:39.9|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:12:39.9|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:12:39.9|Debug|Api|[GET] /api/v3/queue/status?apikey=(removed) 200.OK (18 ms)
2025-08-01 18:12:39.9|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:12:40.0|Debug|Api|[GET] /api/v3/queue/details?apikey=(removed) 200.OK (19 ms)
2025-08-01 18:12:40.3|Debug|Api|[GET] /api/v3/movie?apikey=(removed) 200.OK (422 ms)
2025-08-01 18:12:45.4|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:12:45.5|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:12:45.5|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:12:45.5|Debug|Api|[GET] /api/v3/queue/status?apikey=(removed) 200.OK (18 ms)
2025-08-01 18:12:45.5|Debug|Api|[GET] /api/v3/queue/details?apikey=(removed) 200.OK (16 ms)
2025-08-01 18:12:46.0|Debug|Api|[GET] /api/v3/movie?apikey=(removed) 200.OK (562 ms)
2025-08-01 18:12:54.2|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:12:54.2|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:12:54.3|Debug|Api|[GET] /api/v3/queue/status?apikey=(removed) 200.OK (10 ms)
2025-08-01 18:12:54.3|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:12:54.3|Debug|Api|[GET] /api/v3/queue/details?apikey=(removed) 200.OK (19 ms)
2025-08-01 18:12:54.7|Debug|Api|[GET] /api/v3/movie?apikey=(removed) 200.OK (471 ms)
2025-08-01 18:13:00.0|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:13:00.0|Debug|Api|[POST] /api/v3/command?apikey=(removed) 201.Created (13 ms)
2025-08-01 18:13:00.0|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:13:00.0|Debug|Api|[GET] /api/v3/queue?apikey=(removed)&includeEpisode=true: 200.OK (15 ms)
2025-08-01 18:13:36.6|Debug|ImportListSyncService|No enabled import lists, skipping sync and cleaning
2025-08-01 18:13:39.0|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:13:39.0|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:13:39.0|Debug|Api|[GET] /api/v3/queue/status?apikey=(removed) 200.OK (12 ms)
2025-08-01 18:13:39.1|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:13:39.1|Debug|Api|[GET] /api/v3/queue/details?apikey=(removed) 200.OK (7 ms)
2025-08-01 18:13:39.5|Debug|Api|[GET] /api/v3/movie?apikey=(removed) 200.OK (533 ms)
2025-08-01 18:14:00.0|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:14:00.0|Debug|Api|[POST] /api/v3/command?apikey=(removed) 201.Created (9 ms)
2025-08-01 18:14:00.0|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:14:00.0|Debug|Api|[GET] /api/v3/queue?apikey=(removed)&includeEpisode=true: 200.OK (14 ms)
2025-08-01 18:14:38.4|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:14:38.5|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:14:38.5|Debug|Api|[GET] /api/v3/queue/status?apikey=(removed) 200.OK (13 ms)
2025-08-01 18:14:38.5|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:14:38.5|Debug|Api|[GET] /api/v3/queue/details?apikey=(removed) 200.OK (19 ms)
2025-08-01 18:14:39.0|Debug|Api|[GET] /api/v3/movie?apikey=(removed) 200.OK (574 ms)
2025-08-01 18:15:00.0|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:15:00.0|Debug|Api|[POST] /api/v3/command?apikey=(removed) 201.Created (14 ms)
2025-08-01 18:15:00.0|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:15:00.1|Debug|Api|[GET] /api/v3/queue?apikey=(removed)&includeEpisode=true: 200.OK (75 ms)
2025-08-01 18:16:00.0|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:16:00.0|Debug|Api|[POST] /api/v3/command?apikey=(removed) 201.Created (11 ms)
2025-08-01 18:16:00.0|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:16:00.0|Debug|Api|[GET] /api/v3/queue?apikey=(removed)&includeEpisode=true: 200.OK (8 ms)
2025-08-01 18:16:02.8|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:16:02.9|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:16:02.9|Debug|Api|[GET] /api/v3/queue/status?apikey=(removed) 200.OK (8 ms)
2025-08-01 18:16:02.9|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:16:02.9|Debug|Api|[GET] /api/v3/queue/details?apikey=(removed) 200.OK (8 ms)
2025-08-01 18:16:03.4|Debug|Api|[GET] /api/v3/movie?apikey=(removed) 200.OK (557 ms)
2025-08-01 18:16:08.9|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:16:08.9|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:16:08.9|Debug|Api|[GET] /api/v3/queue/status?apikey=(removed) 200.OK (8 ms)
2025-08-01 18:16:08.9|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:16:09.0|Debug|Api|[GET] /api/v3/queue/details?apikey=(removed) 200.OK (13 ms)
2025-08-01 18:16:09.3|Debug|Api|[GET] /api/v3/movie?apikey=(removed) 200.OK (430 ms)
2025-08-01 18:16:40.7|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:16:40.7|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:16:40.7|Debug|Api|[GET] /api/v3/queue/status?apikey=(removed) 200.OK (12 ms)
2025-08-01 18:16:40.7|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:16:40.7|Debug|Api|[GET] /api/v3/queue/details?apikey=(removed) 200.OK (11 ms)
2025-08-01 18:16:41.1|Debug|Api|[GET] /api/v3/movie?apikey=(removed) 200.OK (463 ms)
2025-08-01 18:16:51.6|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:16:51.6|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:16:51.6|Debug|Api|[GET] /api/v3/queue/status?apikey=(removed) 200.OK (24 ms)
2025-08-01 18:16:51.7|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:16:51.7|Debug|Api|[GET] /api/v3/queue/details?apikey=(removed) 200.OK (31 ms)
2025-08-01 18:16:52.2|Debug|Api|[GET] /api/v3/movie?apikey=(removed) 200.OK (604 ms)
2025-08-01 18:16:52.4|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:16:52.4|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:16:52.4|Debug|Api|[GET] /api/v3/queue/status?apikey=(removed) 200.OK (27 ms)
2025-08-01 18:16:52.4|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:16:52.5|Debug|Api|[GET] /api/v3/queue/details?apikey=(removed) 200.OK (44 ms)
2025-08-01 18:16:52.8|Debug|Api|[GET] /api/v3/movie?apikey=(removed) 200.OK (455 ms)
2025-08-01 18:16:59.5|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:16:59.5|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:16:59.6|Debug|Api|[GET] /api/v3/queue/status?apikey=(removed) 200.OK (19 ms)
2025-08-01 18:16:59.6|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:16:59.6|Debug|Api|[GET] /api/v3/queue/details?apikey=(removed) 200.OK (31 ms)
2025-08-01 18:17:00.0|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:17:00.0|Debug|Api|[POST] /api/v3/command?apikey=(removed) 201.Created (14 ms)
2025-08-01 18:17:00.0|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:17:00.0|Debug|Api|[GET] /api/v3/movie?apikey=(removed) 200.OK (506 ms)
2025-08-01 18:17:00.0|Debug|Api|[GET] /api/v3/queue?apikey=(removed)&includeEpisode=true: 200.OK (17 ms)
2025-08-01 18:17:10.5|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:17:10.5|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:17:10.5|Debug|Api|[GET] /api/v3/queue/status?apikey=(removed) 200.OK (18 ms)
2025-08-01 18:17:10.6|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:17:10.6|Debug|Api|[GET] /api/v3/queue/details?apikey=(removed) 200.OK (19 ms)
2025-08-01 18:17:11.3|Debug|Api|[GET] /api/v3/movie?apikey=(removed) 200.OK (744 ms)
2025-08-01 18:17:11.4|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:17:11.4|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:17:11.4|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:17:11.4|Debug|Api|[GET] /api/v3/queue/status?apikey=(removed) 200.OK (10 ms)
2025-08-01 18:17:11.4|Debug|Api|[GET] /api/v3/queue/details?apikey=(removed) 200.OK (18 ms)
2025-08-01 18:17:11.9|Debug|Api|[GET] /api/v3/movie?apikey=(removed) 200.OK (501 ms)
2025-08-01 18:17:34.7|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:17:34.7|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:17:34.7|Debug|Api|[GET] /api/v3/queue/status?apikey=(removed) 200.OK (12 ms)
2025-08-01 18:17:34.9|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:17:34.9|Debug|Api|[GET] /api/v3/queue/details?apikey=(removed) 200.OK (34 ms)
2025-08-01 18:17:35.3|Debug|Api|[GET] /api/v3/movie?apikey=(removed) 200.OK (566 ms)
2025-08-01 18:17:35.4|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:17:35.4|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:17:35.4|Debug|Api|[GET] /api/v3/queue/status?apikey=(removed) 200.OK (17 ms)
2025-08-01 18:17:35.5|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:17:35.6|Debug|Api|[GET] /api/v3/queue/details?apikey=(removed) 200.OK (20 ms)
2025-08-01 18:17:35.9|Debug|Api|[GET] /api/v3/movie?apikey=(removed) 200.OK (520 ms)
2025-08-01 18:17:42.0|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:17:42.0|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:17:42.0|Debug|Api|[GET] /api/v3/queue/status?apikey=(removed) 200.OK (11 ms)
2025-08-01 18:17:42.0|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:17:42.1|Debug|Api|[GET] /api/v3/queue/details?apikey=(removed) 200.OK (36 ms)
2025-08-01 18:17:42.5|Debug|Api|[GET] /api/v3/movie?apikey=(removed) 200.OK (480 ms)
2025-08-01 18:17:47.3|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:17:47.3|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:17:47.4|Debug|Api|[GET] /api/v3/queue/status?apikey=(removed) 200.OK (22 ms)
2025-08-01 18:17:47.4|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:17:47.4|Debug|Api|[GET] /api/v3/queue/details?apikey=(removed) 200.OK (13 ms)
2025-08-01 18:17:47.9|Debug|Api|[GET] /api/v3/movie?apikey=(removed) 200.OK (581 ms)
2025-08-01 18:17:48.0|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:17:48.1|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:17:48.1|Debug|Api|[GET] /api/v3/queue/status?apikey=(removed) 200.OK (18 ms)
2025-08-01 18:17:48.1|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:17:48.2|Debug|Api|[GET] /api/v3/queue/details?apikey=(removed) 200.OK (8 ms)
2025-08-01 18:17:48.5|Debug|Api|[GET] /api/v3/movie?apikey=(removed) 200.OK (471 ms)
2025-08-01 18:17:53.3|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:17:53.3|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:17:53.3|Debug|Api|[GET] /api/v3/queue/status?apikey=(removed) 200.OK (18 ms)
2025-08-01 18:17:53.3|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:17:53.4|Debug|Api|[GET] /api/v3/queue/details?apikey=(removed) 200.OK (26 ms)
2025-08-01 18:17:53.8|Debug|Api|[GET] /api/v3/movie?apikey=(removed) 200.OK (540 ms)
2025-08-01 18:17:53.9|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:17:53.9|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:17:53.9|Debug|Api|[GET] /api/v3/queue/status?apikey=(removed) 200.OK (7 ms)
2025-08-01 18:17:54.0|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:17:54.0|Debug|Api|[GET] /api/v3/queue/details?apikey=(removed) 200.OK (8 ms)
2025-08-01 18:17:54.4|Debug|Api|[GET] /api/v3/movie?apikey=(removed) 200.OK (460 ms)
2025-08-01 18:18:00.0|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:18:00.0|Debug|Api|[POST] /api/v3/command?apikey=(removed) 201.Created (11 ms)
2025-08-01 18:18:00.0|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:18:00.0|Debug|Api|[GET] /api/v3/queue?apikey=(removed)&includeEpisode=true: 200.OK (12 ms)
2025-08-01 18:18:01.0|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:18:01.0|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:18:01.0|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:18:01.0|Debug|Api|[GET] /api/v3/queue/status?apikey=(removed) 200.OK (16 ms)
2025-08-01 18:18:01.0|Debug|Api|[GET] /api/v3/queue/details?apikey=(removed) 200.OK (28 ms)
2025-08-01 18:18:01.5|Debug|Api|[GET] /api/v3/movie?apikey=(removed) 200.OK (461 ms)
2025-08-01 18:18:02.6|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:18:02.6|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:18:02.6|Debug|Api|[GET] /api/v3/queue/status?apikey=(removed) 200.OK (21 ms)
2025-08-01 18:18:02.6|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:18:02.7|Debug|Api|[GET] /api/v3/queue/details?apikey=(removed) 200.OK (14 ms)
2025-08-01 18:18:03.1|Debug|Api|[GET] /api/v3/movie?apikey=(removed) 200.OK (490 ms)
2025-08-01 18:18:10.1|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:18:10.2|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:18:10.2|Debug|Api|[GET] /api/v3/queue/status?apikey=(removed) 200.OK (13 ms)
2025-08-01 18:18:10.2|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:18:10.2|Debug|Api|[GET] /api/v3/queue/details?apikey=(removed) 200.OK (8 ms)
2025-08-01 18:18:10.7|Debug|Api|[GET] /api/v3/movie?apikey=(removed) 200.OK (522 ms)
2025-08-01 18:19:00.0|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:19:00.0|Debug|Api|[POST] /api/v3/command?apikey=(removed) 201.Created (10 ms)
2025-08-01 18:19:00.0|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:19:00.0|Debug|Api|[GET] /api/v3/queue?apikey=(removed)&includeEpisode=true: 200.OK (16 ms)
2025-08-01 18:19:06.6|Debug|ImportListSyncService|No enabled import lists, skipping sync and cleaning
2025-08-01 18:20:00.0|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:20:00.0|Debug|Api|[POST] /api/v3/command?apikey=(removed) 201.Created (13 ms)
2025-08-01 18:20:00.0|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:20:00.0|Debug|Api|[GET] /api/v3/queue?apikey=(removed)&includeEpisode=true: 200.OK (12 ms)
2025-08-01 18:20:04.2|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:20:04.2|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:20:04.3|Debug|Api|[GET] /api/v3/queue/status?apikey=(removed) 200.OK (6 ms)
2025-08-01 18:20:04.3|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:20:04.3|Debug|Api|[GET] /api/v3/queue/details?apikey=(removed) 200.OK (24 ms)
2025-08-01 18:20:04.8|Debug|Api|[GET] /api/v3/movie?apikey=(removed) 200.OK (570 ms)
2025-08-01 18:20:40.8|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:20:40.8|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:20:40.8|Debug|Api|[GET] /api/v3/queue/details?apikey=(removed) 200.OK (17 ms)
2025-08-01 18:20:40.8|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:20:40.9|Debug|Api|[GET] /api/v3/queue/status?apikey=(removed) 200.OK (17 ms)
2025-08-01 18:20:41.3|Debug|Api|[GET] /api/v3/movie?apikey=(removed) 200.OK (531 ms)
2025-08-01 18:20:57.8|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:20:57.8|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:20:57.9|Debug|Api|[GET] /api/v3/queue/status?apikey=(removed) 200.OK (14 ms)
2025-08-01 18:20:57.9|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:20:57.9|Debug|Api|[GET] /api/v3/queue/details?apikey=(removed) 200.OK (17 ms)
2025-08-01 18:20:58.3|Debug|Api|[GET] /api/v3/movie?apikey=(removed) 200.OK (492 ms)
2025-08-01 18:21:00.0|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:21:00.0|Debug|Api|[POST] /api/v3/command?apikey=(removed) 201.Created (11 ms)
2025-08-01 18:21:00.0|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was successfully authenticated.
2025-08-01 18:21:00.0|Debug|Api|[GET] /api/v3/queue?apikey=(removed)&includeEpisode=true: 200.OK (36 ms)
