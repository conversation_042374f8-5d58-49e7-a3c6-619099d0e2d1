{"Title": "<PERSON> and <PERSON><PERSON><PERSON>", "seriesID": "tt2861424", "Season": 4, "totalSeasons": 8, "Episodes": [{"Title": "Edge of Tomorty: <PERSON>", "Year": "2019", "Rated": "TV-14", "Released": "10 Nov 2019", "Runtime": "22 min", "Genre": "Animation, Adventure, Comedy", "Director": "<PERSON>, <PERSON>", "Writer": "<PERSON>, <PERSON>, <PERSON>", "Actors": "<PERSON>, <PERSON>, <PERSON>", "Plot": "<PERSON> brings <PERSON><PERSON><PERSON> to a planet containing crystals that show whoever is touching them all the ways they may die depending on their choices.", "Language": "English", "Country": "United States", "Poster": "https://m.media-amazon.com/images/M/MV5BOWYyMzRmMWItMTg0MS00MTFmLWI3NmUtZWU2OTJmMTM0ZTY5XkEyXkFqcGc@._V1_SX300.jpg", "imdbRating": "8.9", "imdbVotes": "16496", "imdbID": "tt7446798", "Episode": 1}, {"Title": "The Old Man and the Seat", "Year": "2019", "Rated": "TV-14", "Released": "17 Nov 2019", "Runtime": "22 min", "Genre": "Animation, Adventure, Comedy", "Director": "<PERSON>, <PERSON>", "Writer": "<PERSON>, <PERSON>, <PERSON>", "Actors": "<PERSON>, <PERSON>, <PERSON>", "Plot": "<PERSON> goes to his private bathroom to find that someone else has used it. <PERSON> creates an app with an unlikely alien and <PERSON><PERSON><PERSON> pays the price.", "Language": "English", "Country": "United States", "Poster": "https://m.media-amazon.com/images/M/MV5BNWZkNWU2NzEtODZmYS00YTlhLWE5NTItMTBkYzlhMGUyZTczXkEyXkFqcGc@._V1_SX300.jpg", "imdbRating": "8.2", "imdbVotes": "14373", "imdbID": "tt10655676", "Episode": 2}, {"Title": "One Crew over the Crewcoo's Morty", "Year": "2019", "Rated": "TV-14", "Released": "24 Nov 2019", "Runtime": "22 min", "Genre": "Animation, Adventure, Comedy", "Director": "<PERSON>, <PERSON>", "Writer": "<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>", "Actors": "<PERSON>, <PERSON>, <PERSON>", "Plot": "On a treasure-seeking expedition in an alien temple, <PERSON> and <PERSON><PERSON><PERSON> discover that a heist expert has snatched the prize from under their noses. Further twists, turns and double-crosses abound.", "Language": "English", "Country": "United States", "Poster": "https://m.media-amazon.com/images/M/MV5BNTQ5Nzk5ODItYzk3Yy00YzZiLTk1NDktMWQ5NjNhMWU1NTZkXkEyXkFqcGc@._V1_SX300.jpg", "imdbRating": "8.2", "imdbVotes": "13995", "imdbID": "tt10655678", "Episode": 3}, {"Title": "<PERSON><PERSON> and Hoarder: Special <PERSON><PERSON><PERSON>'s <PERSON><PERSON><PERSON>", "Year": "2019", "Rated": "TV-14", "Released": "08 Dec 2019", "Runtime": "22 min", "Genre": "Animation, Adventure, Comedy", "Director": "<PERSON>, <PERSON>", "Writer": "<PERSON>, <PERSON>, <PERSON>", "Actors": "<PERSON>, <PERSON>, <PERSON>", "Plot": "<PERSON><PERSON><PERSON> asks <PERSON> for a dragon and <PERSON> finds a wizard of another dimension who makes a soul bonding contract between <PERSON><PERSON><PERSON> and a dragon called <PERSON><PERSON>hroma<PERSON>. Meanwhile, <PERSON> meets a talking cat in his bedroom who wants to fly to Flor...", "Language": "English", "Country": "United States", "Poster": "https://m.media-amazon.com/images/M/MV5BN2FjOGE3YzQtZTgxMC00Mjk1LThlNGEtMTc5YTU5ZjFhYzFkXkEyXkFqcGdeQXVyNjg5MjU3NjE@._V1_SX300.jpg", "imdbRating": "7.3", "imdbVotes": "13987", "imdbID": "tt10655680", "Episode": 4}, {"Title": "Rattlestar Ricklactica", "Year": "2019", "Rated": "TV-14", "Released": "15 Dec 2019", "Runtime": "22 min", "Genre": "Animation, Adventure, Comedy", "Director": "<PERSON>, <PERSON>", "Writer": "<PERSON>, <PERSON>, <PERSON>", "Actors": "<PERSON>, <PERSON>, <PERSON>", "Plot": "<PERSON><PERSON><PERSON> discovers a race of intelligent space snakes after suffering a potentially lethal bite. <PERSON> attempts to prove that he isn't completely incompetent.", "Language": "English", "Country": "United States", "Poster": "https://m.media-amazon.com/images/M/MV5BZGIyOGVkYTUtZTExZi00NWRlLTg3ODUtYWIyNDJiNDc0ODFlXkEyXkFqcGc@._V1_SX300.jpg", "imdbRating": "8.8", "imdbVotes": "13722", "imdbID": "tt10655682", "Episode": 5}, {"Title": "Never Ricking Mo<PERSON>y", "Year": "2020", "Rated": "TV-14", "Released": "03 May 2020", "Runtime": "21 min", "Genre": "Animation, Adventure, Comedy", "Director": "<PERSON>, <PERSON>", "Writer": "<PERSON>, <PERSON>, <PERSON>", "Actors": "<PERSON>, <PERSON>, <PERSON>", "Plot": "An anthology episode that follows <PERSON> and <PERSON><PERSON><PERSON> on a train with people who don't like <PERSON>. They have to find their way out but they bump into unexpected situations along the way.", "Language": "English", "Country": "United States", "Poster": "https://m.media-amazon.com/images/M/MV5BYmExYjk1N2ItOTk1My00MzA3LWI0NDctNWY2MDZkMzI3MDc5XkEyXkFqcGc@._V1_SX300.jpg", "imdbRating": "8.0", "imdbVotes": "12953", "imdbID": "tt10655686", "Episode": 6}, {"Title": "<PERSON><PERSON><PERSON><PERSON>", "Year": "2020", "Rated": "TV-14", "Released": "10 May 2020", "Runtime": "21 min", "Genre": "Animation, Adventure, Comedy", "Director": "<PERSON>, <PERSON>", "Writer": "<PERSON>, <PERSON>, <PERSON>", "Actors": "<PERSON>, <PERSON>, <PERSON>", "Plot": "<PERSON>, <PERSON><PERSON><PERSON> and <PERSON> visit an alien civilisation, where <PERSON> and <PERSON><PERSON><PERSON> are controlled by parasites and <PERSON> lives a luxurious lifestyle.", "Language": "English", "Country": "United States", "Poster": "https://m.media-amazon.com/images/M/MV5BNzlhM2JjYmItYWU2Yi00YmVjLWIwODItYjg3Mjk3N2M4YzNjXkEyXkFqcGc@._V1_SX300.jpg", "imdbRating": "8.0", "imdbVotes": "11075", "imdbID": "tt10655690", "Episode": 7}, {"Title": "The Vat of Acid Episode", "Year": "2020", "Rated": "TV-14", "Released": "17 May 2020", "Runtime": "22 min", "Genre": "Animation, Adventure, Comedy", "Director": "<PERSON>, <PERSON>", "Writer": "<PERSON>, <PERSON>, <PERSON>", "Actors": "<PERSON>, <PERSON>, <PERSON>", "Plot": "<PERSON> &amp; <PERSON><PERSON><PERSON> go on a simple exchange as <PERSON> tells <PERSON><PERSON><PERSON> if something goes wrong jump into the same vat of acid as he does.", "Language": "English", "Country": "United States", "Poster": "https://m.media-amazon.com/images/M/MV5BZWM5YjMyZjEtYzJmYi00NDgyLWI5N2EtYjE5MzU5ZTRjMTUxXkEyXkFqcGc@._V1_SX300.jpg", "imdbRating": "9.5", "imdbVotes": "17440", "imdbID": "tt10655692", "Episode": 8}, {"Title": "<PERSON><PERSON> of Mort", "Year": "2020", "Rated": "TV-14", "Released": "24 May 2020", "Runtime": "22 min", "Genre": "Animation, Adventure, Comedy", "Director": "<PERSON><PERSON><PERSON>, <PERSON>", "Writer": "<PERSON>, <PERSON>, <PERSON>", "Actors": "<PERSON>, <PERSON>, <PERSON>", "Plot": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON> and <PERSON> share a galactic adventure.", "Language": "English", "Country": "United States", "Poster": "https://m.media-amazon.com/images/M/MV5BYmM5MzY2ZjYtMTU1MC00ZTAxLTg4NjctMWVjMTM4NTkwYWRlXkEyXkFqcGc@._V1_SX300.jpg", "imdbRating": "7.7", "imdbVotes": "11214", "imdbID": "tt10655694", "Episode": 9}, {"Title": "Star Mo<PERSON> of the Jerri", "Year": "2020", "Rated": "TV-14", "Released": "31 May 2020", "Runtime": "22 min", "Genre": "Animation, Adventure, Comedy", "Director": "<PERSON>, <PERSON>", "Writer": "<PERSON>, <PERSON>, <PERSON>", "Actors": "<PERSON>, <PERSON>, <PERSON>", "Plot": "An adventure with an invisibility belt, but a family that disappears together, must stay together.", "Language": "English", "Country": "United States", "Poster": "https://m.media-amazon.com/images/M/MV5BNTE3NTIwNTQtMjk1NS00ZjJiLWEwYjItMThkMWYxMzIxOTgwXkEyXkFqcGc@._V1_SX300.jpg", "imdbRating": "9.0", "imdbVotes": "12592", "imdbID": "tt10655696", "Episode": 10}], "Response": "True"}