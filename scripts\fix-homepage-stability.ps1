# fix-homepage-stability.ps1
# Script para diagnosticar y reparar problemas de estabilidad de Homepage

param(
    [switch]$DiagnosticOnly,
    [switch]$Fix,
    [switch]$RestartServices,
    [switch]$All
)

# Configuración de colores
$Colors = @{
    Success = "Green"
    Error = "Red"
    Warning = "Yellow"
    Info = "Blue"
    Highlight = "Cyan"
}

function Write-ColorOutput {
    param($Message, $Color = "White")
    Write-Host $Message -ForegroundColor $Colors[$Color]
}

function Test-ServiceConnectivity {
    param($ServiceName, $Port, $Protocol = "HTTP")
    
    Write-ColorOutput "🔍 Probando conectividad: $ServiceName (puerto $Port)" "Info"
    
    try {
        if ($Protocol -eq "HTTP") {
            $Response = Invoke-WebRequest -Uri "http://localhost:$Port" -UseBasicParsing -TimeoutSec 5 -ErrorAction SilentlyContinue
            if ($Response.StatusCode -eq 200) {
                Write-ColorOutput "   ✅ $ServiceName - OK (HTTP $($Response.StatusCode))" "Success"
                return $true
            } else {
                Write-ColorOutput "   ⚠️  $ServiceName - HTTP $($Response.StatusCode)" "Warning"
                return $false
            }
        } else {
            $TcpTest = Test-NetConnection -ComputerName "localhost" -Port $Port -WarningAction SilentlyContinue
            if ($TcpTest.TcpTestSucceeded) {
                Write-ColorOutput "   ✅ $ServiceName - Puerto accesible" "Success"
                return $true
            } else {
                Write-ColorOutput "   ❌ $ServiceName - Puerto no accesible" "Error"
                return $false
            }
        }
    }
    catch {
        Write-ColorOutput "   ❌ $ServiceName - Error: $($_.Exception.Message)" "Error"
        return $false
    }
}

function Test-DockerContainerHealth {
    Write-ColorOutput "🐳 Verificando estado de contenedores Docker..." "Info"
    
    try {
        $Containers = docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-ColorOutput "📊 Estado de contenedores:" "Info"
            $Containers | ForEach-Object { Write-ColorOutput "   $_" "Highlight" }
            
            # Verificar contenedores específicos
            $CriticalContainers = @("homepage", "caddy_proxy", "jellyfin", "sonarr", "radarr", "qbittorrent")
            $UnhealthyContainers = @()
            
            foreach ($Container in $CriticalContainers) {
                $ContainerInfo = docker inspect $Container --format "{{.State.Health.Status}}" 2>$null
                if ($LASTEXITCODE -eq 0) {
                    if ($ContainerInfo -eq "unhealthy") {
                        $UnhealthyContainers += $Container
                        Write-ColorOutput "   ⚠️  $Container está marcado como unhealthy" "Warning"
                    } elseif ($ContainerInfo -eq "healthy") {
                        Write-ColorOutput "   ✅ $Container está healthy" "Success"
                    } else {
                        Write-ColorOutput "   ℹ️  $Container no tiene health check configurado" "Info"
                    }
                }
            }
            
            return $UnhealthyContainers
        } else {
            Write-ColorOutput "❌ Error ejecutando Docker. ¿Está Docker ejecutándose?" "Error"
            return @("docker-error")
        }
    }
    catch {
        Write-ColorOutput "❌ Error verificando contenedores: $($_.Exception.Message)" "Error"
        return @("docker-error")
    }
}

function Test-NetworkConnectivity {
    Write-ColorOutput "🌐 Verificando conectividad de red..." "Info"
    
    # Servicios críticos a verificar
    $Services = @(
        @{Name="Jellyfin"; Port=8096},
        @{Name="Homepage"; Port=3001},
        @{Name="Caddy"; Port=80; Protocol="TCP"},
        @{Name="Caddy HTTPS"; Port=443; Protocol="TCP"},
        @{Name="Sonarr"; Port=8989},
        @{Name="Radarr"; Port=7878},
        @{Name="qBittorrent"; Port=8091},
        @{Name="Jackett"; Port=9117},
        @{Name="Jellyseerr"; Port=5055}
    )
    
    $FailedServices = @()
    foreach ($Service in $Services) {
        $Protocol = if ($Service.Protocol) { $Service.Protocol } else { "HTTP" }
        if (-not (Test-ServiceConnectivity -ServiceName $Service.Name -Port $Service.Port -Protocol $Protocol)) {
            $FailedServices += $Service.Name
        }
        Start-Sleep -Seconds 1
    }
    
    return $FailedServices
}

function Test-DuckDNSConnectivity {
    Write-ColorOutput "🦆 Verificando conectividad DuckDNS..." "Info"
    
    $DuckDNSDomains = @(
        "tankeflix.duckdns.org",
        "tanketorrent.duckdns.org", 
        "tankesonarr.duckdns.org",
        "tankeradarr.duckdns.org",
        "tankejackett.duckdns.org",
        "tankejellyseerr.duckdns.org",
        "tankeeee2.duckdns.org"
    )
    
    $FailedDomains = @()
    foreach ($Domain in $DuckDNSDomains) {
        try {
            Write-ColorOutput "   🔍 Probando $Domain..." "Info"
            $Response = Invoke-WebRequest -Uri "https://$Domain" -UseBasicParsing -TimeoutSec 10 -ErrorAction SilentlyContinue
            if ($Response.StatusCode -eq 200) {
                Write-ColorOutput "   ✅ $Domain - OK" "Success"
            } else {
                Write-ColorOutput "   ⚠️  $Domain - HTTP $($Response.StatusCode)" "Warning"
                $FailedDomains += $Domain
            }
        }
        catch {
            Write-ColorOutput "   ❌ $Domain - Error: $($_.Exception.Message)" "Error"
            $FailedDomains += $Domain
        }
        Start-Sleep -Seconds 2
    }
    
    return $FailedDomains
}

function Fix-HomepageConfiguration {
    Write-ColorOutput "🔧 Reparando configuración de Homepage..." "Info"
    
    try {
        # Verificar que las referencias a AdGuard Home estén eliminadas
        $ServicesFile = "C:\docker\duckdns_updater\homepage\config\services.yaml"
        $ServicesContent = Get-Content $ServicesFile -Raw
        
        if ($ServicesContent -match "AdGuard|adguard|tankeguard") {
            Write-ColorOutput "   ⚠️  Encontradas referencias a AdGuard Home en services.yaml" "Warning"
            Write-ColorOutput "   ℹ️  Las referencias ya han sido limpiadas en esta sesión" "Info"
        } else {
            Write-ColorOutput "   ✅ services.yaml limpio de referencias a AdGuard Home" "Success"
        }
        
        # Verificar configuración de Docker
        $DockerFile = "C:\docker\duckdns_updater\homepage\config\docker.yaml"
        if (Test-Path $DockerFile) {
            Write-ColorOutput "   ✅ docker.yaml configurado correctamente" "Success"
        } else {
            Write-ColorOutput "   ❌ docker.yaml no encontrado" "Error"
        }
        
        return $true
    }
    catch {
        Write-ColorOutput "   ❌ Error reparando configuración: $($_.Exception.Message)" "Error"
        return $false
    }
}

function Fix-DockerPermissions {
    Write-ColorOutput "🔐 Verificando permisos de Docker..." "Info"
    
    try {
        # En Windows, verificar que Docker Desktop esté ejecutándose
        $DockerProcess = Get-Process "Docker Desktop" -ErrorAction SilentlyContinue
        if ($DockerProcess) {
            Write-ColorOutput "   ✅ Docker Desktop está ejecutándose" "Success"
        } else {
            Write-ColorOutput "   ⚠️  Docker Desktop no está ejecutándose" "Warning"
            Write-ColorOutput "   💡 Inicia Docker Desktop manualmente" "Info"
        }
        
        # Verificar acceso al socket de Docker
        $DockerInfo = docker info 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-ColorOutput "   ✅ Acceso al socket de Docker OK" "Success"
            return $true
        } else {
            Write-ColorOutput "   ❌ No se puede acceder al socket de Docker" "Error"
            Write-ColorOutput "   💡 Reinicia Docker Desktop" "Info"
            return $false
        }
    }
    catch {
        Write-ColorOutput "   ❌ Error verificando Docker: $($_.Exception.Message)" "Error"
        return $false
    }
}

function Restart-HomepageService {
    Write-ColorOutput "🔄 Reiniciando servicio de Homepage..." "Info"
    
    try {
        Set-Location "C:\docker\duckdns_updater"
        
        Write-ColorOutput "   🛑 Deteniendo Homepage..." "Info"
        $Result = docker-compose stop homepage 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-ColorOutput "   ✅ Homepage detenido" "Success"
        } else {
            Write-ColorOutput "   ⚠️  Advertencia deteniendo Homepage: $Result" "Warning"
        }
        
        Start-Sleep -Seconds 5
        
        Write-ColorOutput "   🚀 Iniciando Homepage..." "Info"
        $Result = docker-compose up -d homepage 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-ColorOutput "   ✅ Homepage iniciado" "Success"
            
            # Esperar a que el servicio esté listo
            Write-ColorOutput "   ⏳ Esperando que Homepage esté listo..." "Info"
            Start-Sleep -Seconds 30
            
            # Verificar que esté funcionando
            if (Test-ServiceConnectivity -ServiceName "Homepage" -Port 3001) {
                Write-ColorOutput "   ✅ Homepage funcionando correctamente" "Success"
                return $true
            } else {
                Write-ColorOutput "   ⚠️  Homepage iniciado pero no responde aún" "Warning"
                return $false
            }
        } else {
            Write-ColorOutput "   ❌ Error iniciando Homepage: $Result" "Error"
            return $false
        }
    }
    catch {
        Write-ColorOutput "   ❌ Error reiniciando Homepage: $($_.Exception.Message)" "Error"
        return $false
    }
}

function Restart-AllServices {
    Write-ColorOutput "🔄 Reiniciando todos los servicios..." "Warning"
    
    try {
        Set-Location "C:\docker\duckdns_updater"
        Set-Location "C:\docker\arrs"
        
        Write-ColorOutput "   🛑 Deteniendo servicios *arr..." "Info"
        docker-compose down
        
        Set-Location "C:\docker\duckdns_updater"
        Write-ColorOutput "   🛑 Deteniendo servicios principales..." "Info"
        docker-compose down
        
        Start-Sleep -Seconds 10
        
        Write-ColorOutput "   🚀 Iniciando servicios principales..." "Info"
        docker-compose up -d
        
        Start-Sleep -Seconds 15
        
        Set-Location "C:\docker\arrs"
        Write-ColorOutput "   🚀 Iniciando servicios *arr..." "Info"
        docker-compose up -d
        
        Write-ColorOutput "   ⏳ Esperando que todos los servicios estén listos..." "Info"
        Start-Sleep -Seconds 60
        
        Write-ColorOutput "   ✅ Reinicio completo finalizado" "Success"
        return $true
    }
    catch {
        Write-ColorOutput "   ❌ Error reiniciando servicios: $($_.Exception.Message)" "Error"
        return $false
    }
}

# Función principal
function Start-Diagnosis {
    Clear-Host
    Write-ColorOutput "🔍 Diagnóstico de Estabilidad de Homepage" "Info"
    Write-ColorOutput "=" * 50 "Info"
    Write-ColorOutput "📅 Fecha: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" "Info"
    Write-ColorOutput ""
    
    # Diagnóstico completo
    Write-ColorOutput "🏥 Iniciando diagnóstico completo..." "Info"
    Write-ColorOutput ""
    
    # 1. Verificar contenedores Docker
    $UnhealthyContainers = Test-DockerContainerHealth
    Write-ColorOutput ""
    
    # 2. Verificar conectividad local
    $FailedServices = Test-NetworkConnectivity
    Write-ColorOutput ""
    
    # 3. Verificar conectividad DuckDNS
    $FailedDomains = Test-DuckDNSConnectivity
    Write-ColorOutput ""
    
    # Resumen del diagnóstico
    Write-ColorOutput "📊 Resumen del Diagnóstico:" "Info"
    Write-ColorOutput "=" * 30 "Info"
    
    if ($UnhealthyContainers.Count -eq 0) {
        Write-ColorOutput "✅ Todos los contenedores están saludables" "Success"
    } else {
        Write-ColorOutput "⚠️  Contenedores con problemas: $($UnhealthyContainers -join ', ')" "Warning"
    }
    
    if ($FailedServices.Count -eq 0) {
        Write-ColorOutput "✅ Todos los servicios locales responden" "Success"
    } else {
        Write-ColorOutput "❌ Servicios locales con problemas: $($FailedServices -join ', ')" "Error"
    }
    
    if ($FailedDomains.Count -eq 0) {
        Write-ColorOutput "✅ Todos los dominios DuckDNS responden" "Success"
    } else {
        Write-ColorOutput "❌ Dominios DuckDNS con problemas: $($FailedDomains -join ', ')" "Error"
    }
    
    # Aplicar correcciones si se solicita
    if ($Fix -or $All) {
        Write-ColorOutput ""
        Write-ColorOutput "🔧 Aplicando correcciones..." "Info"
        Write-ColorOutput ""
        
        # Reparar configuración de Homepage
        Fix-HomepageConfiguration
        
        # Verificar permisos de Docker
        Fix-DockerPermissions
        
        # Reiniciar Homepage si hay problemas
        if ($UnhealthyContainers -contains "homepage" -or $FailedServices -contains "Homepage") {
            Restart-HomepageService
        }
    }
    
    if ($RestartServices -or $All) {
        Write-ColorOutput ""
        Write-ColorOutput "🔄 Reiniciando servicios..." "Warning"
        Restart-AllServices
    }
    
    Write-ColorOutput ""
    Write-ColorOutput "✅ Diagnóstico completado." "Success"
    
    # Recomendaciones
    Write-ColorOutput ""
    Write-ColorOutput "💡 Recomendaciones:" "Info"
    if ($UnhealthyContainers.Count -gt 0 -or $FailedServices.Count -gt 0) {
        Write-ColorOutput "   • Ejecuta: .\fix-homepage-stability.ps1 -Fix" "Highlight"
        Write-ColorOutput "   • Si persisten problemas: .\fix-homepage-stability.ps1 -All" "Highlight"
    } else {
        Write-ColorOutput "   • Sistema funcionando correctamente" "Success"
        Write-ColorOutput "   • Monitorea los logs: docker logs homepage" "Info"
    }
}

# Ejecutar según parámetros
if ($All) {
    Start-Diagnosis
} elseif ($DiagnosticOnly) {
    # Solo diagnóstico, sin correcciones
    $Fix = $false
    $RestartServices = $false
    Start-Diagnosis
} elseif ($Fix) {
    Start-Diagnosis
} elseif ($RestartServices) {
    Start-Diagnosis
} else {
    Write-ColorOutput "🔍 Script de Diagnóstico y Reparación de Homepage" "Info"
    Write-ColorOutput ""
    Write-ColorOutput "Uso:" "Info"
    Write-ColorOutput "   .\fix-homepage-stability.ps1 -DiagnosticOnly    # Solo diagnóstico" "Highlight"
    Write-ColorOutput "   .\fix-homepage-stability.ps1 -Fix               # Diagnóstico + correcciones" "Highlight"
    Write-ColorOutput "   .\fix-homepage-stability.ps1 -RestartServices   # Diagnóstico + reinicio" "Highlight"
    Write-ColorOutput "   .\fix-homepage-stability.ps1 -All               # Todo lo anterior" "Highlight"
    Write-ColorOutput ""
    Write-ColorOutput "Ejecutando diagnóstico básico..." "Info"
    Start-Diagnosis
}
