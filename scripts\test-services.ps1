# test-services.ps1
# Script simple para probar todos los servicios

param(
    [switch]$Local,
    [switch]$DuckDNS,
    [switch]$All
)

function Write-Status {
    param($Message, $Type = "Info")
    
    switch ($Type) {
        "Success" { Write-Host "✅ $Message" -ForegroundColor Green }
        "Error" { Write-Host "❌ $Message" -ForegroundColor Red }
        "Warning" { Write-Host "⚠️  $Message" -ForegroundColor Yellow }
        "Info" { Write-Host "ℹ️  $Message" -ForegroundColor Blue }
        default { Write-Host "$Message" }
    }
}

function Test-LocalServices {
    Write-Host ""
    Write-Host "🏠 PROBANDO SERVICIOS LOCALES" -ForegroundColor Cyan
    Write-Host "=============================" -ForegroundColor Cyan
    Write-Host ""
    
    $Services = @(
        @{Name="Homepage"; Port=3001; URL="http://localhost:3001"},
        @{Name="Jellyfin"; Port=8096; URL="http://localhost:8096"},
        @{Name="Sonarr"; Port=8989; URL="http://localhost:8989"},
        @{Name="Radarr"; Port=7878; URL="http://localhost:7878"},
        @{Name="qBittorrent"; Port=8091; URL="http://localhost:8091"},
        @{Name="Jackett"; Port=9117; URL="http://localhost:9117"},
        @{Name="Jellyseerr"; Port=5055; URL="http://localhost:5055"},
        @{Name="Portainer"; Port=9000; URL="http://localhost:9000"}
    )
    
    $Results = @()
    foreach ($Service in $Services) {
        Write-Host "Probando $($Service.Name)..." -NoNewline
        
        try {
            $Response = Invoke-WebRequest -Uri $Service.URL -UseBasicParsing -TimeoutSec 5 -ErrorAction Stop
            $StatusCode = $Response.StatusCode
            $ResponseTime = (Measure-Command { 
                Invoke-WebRequest -Uri $Service.URL -UseBasicParsing -TimeoutSec 5 -ErrorAction Stop 
            }).TotalMilliseconds
            
            Write-Host " ✅ OK ($StatusCode) - $([math]::Round($ResponseTime))ms" -ForegroundColor Green
            $Results += @{Service=$Service.Name; Status="OK"; ResponseTime=$ResponseTime; Port=$Service.Port}
        }
        catch {
            Write-Host " ❌ Error" -ForegroundColor Red
            $Results += @{Service=$Service.Name; Status="Error"; ResponseTime=0; Port=$Service.Port}
        }
    }
    
    Write-Host ""
    $SuccessCount = ($Results | Where-Object { $_.Status -eq "OK" }).Count
    Write-Status "Servicios locales funcionando: $SuccessCount/$($Results.Count)" "Info"
    
    return $Results
}

function Test-DuckDNSServices {
    Write-Host ""
    Write-Host "🦆 PROBANDO SERVICIOS DUCKDNS" -ForegroundColor Cyan
    Write-Host "==============================" -ForegroundColor Cyan
    Write-Host ""
    
    $Services = @(
        @{Name="Homepage"; URL="https://tankeeee2.duckdns.org"},
        @{Name="Jellyfin"; URL="https://tankeflix.duckdns.org"},
        @{Name="Sonarr"; URL="https://tankesonarr.duckdns.org"},
        @{Name="Radarr"; URL="https://tankeradarr.duckdns.org"},
        @{Name="qBittorrent"; URL="https://tanketorrent.duckdns.org"},
        @{Name="Jackett"; URL="https://tankejackett.duckdns.org"},
        @{Name="Jellyseerr"; URL="https://tankejellyseerr.duckdns.org"}
    )
    
    $Results = @()
    foreach ($Service in $Services) {
        Write-Host "Probando $($Service.Name)..." -NoNewline
        
        try {
            $Response = Invoke-WebRequest -Uri $Service.URL -UseBasicParsing -TimeoutSec 10 -ErrorAction Stop
            $StatusCode = $Response.StatusCode
            $ResponseTime = (Measure-Command { 
                Invoke-WebRequest -Uri $Service.URL -UseBasicParsing -TimeoutSec 10 -ErrorAction Stop 
            }).TotalMilliseconds
            
            Write-Host " ✅ OK ($StatusCode) - $([math]::Round($ResponseTime))ms" -ForegroundColor Green
            $Results += @{Service=$Service.Name; Status="OK"; ResponseTime=$ResponseTime; URL=$Service.URL}
        }
        catch {
            Write-Host " ❌ Error: $($_.Exception.Message.Split(':')[0])" -ForegroundColor Red
            $Results += @{Service=$Service.Name; Status="Error"; ResponseTime=0; URL=$Service.URL}
        }
        
        Start-Sleep -Seconds 1
    }
    
    Write-Host ""
    $SuccessCount = ($Results | Where-Object { $_.Status -eq "OK" }).Count
    Write-Status "Servicios DuckDNS funcionando: $SuccessCount/$($Results.Count)" "Info"
    
    return $Results
}

function Show-Summary {
    param($LocalResults, $DuckDNSResults)
    
    Write-Host ""
    Write-Host "📊 RESUMEN COMPLETO" -ForegroundColor Cyan
    Write-Host "===================" -ForegroundColor Cyan
    Write-Host ""
    
    if ($LocalResults) {
        $LocalOK = ($LocalResults | Where-Object { $_.Status -eq "OK" }).Count
        $LocalTotal = $LocalResults.Count
        Write-Status "Servicios locales: $LocalOK/$LocalTotal funcionando" "Info"
        
        # Mostrar servicios con problemas
        $LocalFailed = $LocalResults | Where-Object { $_.Status -eq "Error" }
        if ($LocalFailed) {
            Write-Host "  Servicios locales con problemas:" -ForegroundColor Yellow
            $LocalFailed | ForEach-Object { Write-Host "    • $($_.Service) (puerto $($_.Port))" -ForegroundColor Red }
        }
    }
    
    if ($DuckDNSResults) {
        $DuckDNSOK = ($DuckDNSResults | Where-Object { $_.Status -eq "OK" }).Count
        $DuckDNSTotal = $DuckDNSResults.Count
        Write-Status "Servicios DuckDNS: $DuckDNSOK/$DuckDNSTotal funcionando" "Info"
        
        # Mostrar servicios con problemas
        $DuckDNSFailed = $DuckDNSResults | Where-Object { $_.Status -eq "Error" }
        if ($DuckDNSFailed) {
            Write-Host "  Servicios DuckDNS con problemas:" -ForegroundColor Yellow
            $DuckDNSFailed | ForEach-Object { Write-Host "    • $($_.Service)" -ForegroundColor Red }
        }
    }
    
    Write-Host ""
    
    # Recomendaciones
    if (($LocalResults -and ($LocalResults | Where-Object { $_.Status -eq "Error" })) -or 
        ($DuckDNSResults -and ($DuckDNSResults | Where-Object { $_.Status -eq "Error" }))) {
        Write-Host "💡 RECOMENDACIONES:" -ForegroundColor Yellow
        Write-Host "  • Verificar que Docker esté ejecutándose" -ForegroundColor White
        Write-Host "  • Ejecutar: .\fix-homepage-simple.ps1 -Fix" -ForegroundColor White
        Write-Host "  • Revisar logs: docker logs <nombre_contenedor>" -ForegroundColor White
    } else {
        Write-Status "¡Todos los servicios funcionan correctamente!" "Success"
    }
}

# Función principal
if ($Local) {
    $LocalResults = Test-LocalServices
    Show-Summary -LocalResults $LocalResults
} elseif ($DuckDNS) {
    $DuckDNSResults = Test-DuckDNSServices
    Show-Summary -DuckDNSResults $DuckDNSResults
} elseif ($All) {
    $LocalResults = Test-LocalServices
    $DuckDNSResults = Test-DuckDNSServices
    Show-Summary -LocalResults $LocalResults -DuckDNSResults $DuckDNSResults
} else {
    Write-Host ""
    Write-Host "🧪 SCRIPT DE PRUEBA DE SERVICIOS" -ForegroundColor Cyan
    Write-Host "================================" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Uso:" -ForegroundColor Yellow
    Write-Host "  .\test-services.ps1 -Local     # Probar servicios locales" -ForegroundColor White
    Write-Host "  .\test-services.ps1 -DuckDNS   # Probar servicios DuckDNS" -ForegroundColor White
    Write-Host "  .\test-services.ps1 -All       # Probar todos los servicios" -ForegroundColor White
    Write-Host ""
    Write-Host "Ejecutando prueba completa..." -ForegroundColor Green
    
    $LocalResults = Test-LocalServices
    $DuckDNSResults = Test-DuckDNSServices
    Show-Summary -LocalResults $LocalResults -DuckDNSResults $DuckDNSResults
}

Write-Host ""
