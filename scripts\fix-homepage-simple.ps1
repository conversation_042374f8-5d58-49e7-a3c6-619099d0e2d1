# fix-homepage-simple.ps1
# Script simplificado para diagnosticar y reparar Homepage

param(
    [switch]$Diagnostic,
    [switch]$Fix,
    [switch]$Restart
)

# Función para escribir con colores
function Write-Status {
    param($Message, $Type = "Info")
    
    switch ($Type) {
        "Success" { Write-Host "✅ $Message" -ForegroundColor Green }
        "Error" { Write-Host "❌ $Message" -ForegroundColor Red }
        "Warning" { Write-Host "⚠️  $Message" -ForegroundColor Yellow }
        "Info" { Write-Host "ℹ️  $Message" -ForegroundColor Blue }
        default { Write-Host "$Message" }
    }
}

function Test-ServiceConnectivity {
    Write-Status "Probando conectividad de servicios..." "Info"
    
    $Services = @(
        @{Name="Homepage"; Port=3001},
        @{Name="Jellyfin"; Port=8096},
        @{Name="Sonarr"; Port=8989},
        @{Name="Radarr"; Port=7878},
        @{Name="qBittorrent"; Port=8091},
        @{Name="Jackett"; Port=9117},
        @{Name="Jellyseerr"; Port=5055}
    )
    
    $Results = @()
    foreach ($Service in $Services) {
        try {
            $Response = Invoke-WebRequest -Uri "http://localhost:$($Service.Port)" -UseBasicParsing -TimeoutSec 5 -ErrorAction Stop
            Write-Status "$($Service.Name) (puerto $($Service.Port)) - OK" "Success"
            $Results += @{Service=$Service.Name; Status="OK"; Port=$Service.Port}
        }
        catch {
            Write-Status "$($Service.Name) (puerto $($Service.Port)) - Error" "Error"
            $Results += @{Service=$Service.Name; Status="Error"; Port=$Service.Port}
        }
    }
    
    return $Results
}

function Test-DockerContainers {
    Write-Status "Verificando contenedores Docker..." "Info"
    
    try {
        $Containers = docker ps --format "{{.Names}}\t{{.Status}}" 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Status "Contenedores ejecutándose:" "Info"
            $Containers | ForEach-Object { Write-Host "  $_" }
            
            # Verificar Homepage específicamente
            $HomepageStatus = docker inspect homepage --format "{{.State.Status}}" 2>$null
            if ($HomepageStatus -eq "running") {
                Write-Status "Homepage está ejecutándose" "Success"
            } else {
                Write-Status "Homepage no está ejecutándose correctamente" "Warning"
            }
            
            return $true
        } else {
            Write-Status "Error ejecutando Docker" "Error"
            return $false
        }
    }
    catch {
        Write-Status "Error verificando Docker: $($_.Exception.Message)" "Error"
        return $false
    }
}

function Restart-Homepage {
    Write-Status "Reiniciando Homepage..." "Info"
    
    try {
        Set-Location "C:\docker\duckdns_updater"
        
        Write-Status "Deteniendo Homepage..." "Info"
        docker-compose stop homepage
        
        Start-Sleep -Seconds 5
        
        Write-Status "Iniciando Homepage..." "Info"
        docker-compose up -d homepage
        
        Write-Status "Esperando que Homepage esté listo..." "Info"
        Start-Sleep -Seconds 20
        
        # Verificar que esté funcionando
        try {
            $Response = Invoke-WebRequest -Uri "http://localhost:3001" -UseBasicParsing -TimeoutSec 10
            if ($Response.StatusCode -eq 200) {
                Write-Status "Homepage reiniciado correctamente" "Success"
                return $true
            }
        }
        catch {
            Write-Status "Homepage reiniciado pero aún no responde" "Warning"
            return $false
        }
    }
    catch {
        Write-Status "Error reiniciando Homepage: $($_.Exception.Message)" "Error"
        return $false
    }
}

function Show-HomepageLogs {
    Write-Status "Últimas líneas del log de Homepage:" "Info"
    try {
        $Logs = docker logs homepage --tail 20 2>$null
        if ($Logs) {
            $Logs | ForEach-Object { Write-Host "  $_" }
        } else {
            Write-Status "No se pudieron obtener los logs" "Warning"
        }
    }
    catch {
        Write-Status "Error obteniendo logs: $($_.Exception.Message)" "Error"
    }
}

function Start-Diagnostic {
    Write-Host ""
    Write-Host "🔍 DIAGNÓSTICO DE HOMEPAGE" -ForegroundColor Cyan
    Write-Host "=========================" -ForegroundColor Cyan
    Write-Host ""
    
    # Verificar Docker
    $DockerOK = Test-DockerContainers
    Write-Host ""
    
    # Verificar conectividad
    $ConnectivityResults = Test-ServiceConnectivity
    Write-Host ""
    
    # Mostrar logs si hay problemas
    $HomepageResult = $ConnectivityResults | Where-Object { $_.Service -eq "Homepage" }
    if ($HomepageResult.Status -eq "Error") {
        Show-HomepageLogs
        Write-Host ""
    }
    
    # Resumen
    Write-Host "📊 RESUMEN:" -ForegroundColor Cyan
    $SuccessCount = ($ConnectivityResults | Where-Object { $_.Status -eq "OK" }).Count
    $TotalCount = $ConnectivityResults.Count
    Write-Status "Servicios funcionando: $SuccessCount/$TotalCount" "Info"
    
    if ($SuccessCount -eq $TotalCount) {
        Write-Status "Todos los servicios funcionan correctamente" "Success"
    } else {
        Write-Status "Algunos servicios tienen problemas" "Warning"
        Write-Host ""
        Write-Host "💡 Para intentar reparar:" -ForegroundColor Yellow
        Write-Host "   .\fix-homepage-simple.ps1 -Fix" -ForegroundColor White
        Write-Host "   .\fix-homepage-simple.ps1 -Restart" -ForegroundColor White
    }
}

function Start-Fix {
    Write-Host ""
    Write-Host "🔧 REPARACIÓN DE HOMEPAGE" -ForegroundColor Cyan
    Write-Host "=========================" -ForegroundColor Cyan
    Write-Host ""
    
    # Verificar configuración
    Write-Status "Verificando archivos de configuración..." "Info"
    
    $ConfigFiles = @(
        "C:\docker\duckdns_updater\homepage\config\services.yaml",
        "C:\docker\duckdns_updater\homepage\config\settings.yaml",
        "C:\docker\duckdns_updater\homepage\config\docker.yaml"
    )
    
    foreach ($File in $ConfigFiles) {
        if (Test-Path $File) {
            Write-Status "$(Split-Path $File -Leaf) - OK" "Success"
        } else {
            Write-Status "$(Split-Path $File -Leaf) - Falta" "Error"
        }
    }
    
    Write-Host ""
    
    # Reiniciar Homepage
    $RestartResult = Restart-Homepage
    
    Write-Host ""
    if ($RestartResult) {
        Write-Status "Reparación completada" "Success"
    } else {
        Write-Status "Reparación parcial - revisa los logs" "Warning"
    }
}

# Función principal
if ($Diagnostic) {
    Start-Diagnostic
} elseif ($Fix) {
    Start-Fix
} elseif ($Restart) {
    Write-Host ""
    Write-Host "🔄 REINICIO DE HOMEPAGE" -ForegroundColor Cyan
    Write-Host "======================" -ForegroundColor Cyan
    Write-Host ""
    Restart-Homepage
} else {
    Write-Host ""
    Write-Host "🛠️  SCRIPT DE REPARACIÓN DE HOMEPAGE" -ForegroundColor Cyan
    Write-Host "====================================" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Uso:" -ForegroundColor Yellow
    Write-Host "  .\fix-homepage-simple.ps1 -Diagnostic    # Solo diagnóstico" -ForegroundColor White
    Write-Host "  .\fix-homepage-simple.ps1 -Fix           # Reparar problemas" -ForegroundColor White
    Write-Host "  .\fix-homepage-simple.ps1 -Restart       # Solo reiniciar" -ForegroundColor White
    Write-Host ""
    Write-Host "Ejecutando diagnóstico..." -ForegroundColor Green
    Start-Diagnostic
}

Write-Host ""
