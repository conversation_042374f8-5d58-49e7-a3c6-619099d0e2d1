#!/usr/bin/env python3
"""
Script de prueba para verificar que se esté usando el perfil de calidad correcto
"""

import asyncio
import httpx
import json

# Configuración
SONARR_URL = "http://*************:8989"
RADARR_URL = "http://*************:7878"
SONARR_API_KEY = "2f9da07e98744f4890c0960d15ead111"
RADARR_API_KEY = "cfc0cde90b0f483eb4190dc634ca86f2"

async def test_quality_profile_usage():
    """Probar que se use el perfil de calidad correcto"""
    print("🧪 Probando configuración de perfiles de calidad...")
    
    # Verificar perfiles de Sonarr
    print("\n📊 Verificando perfiles de Sonarr...")
    async with httpx.AsyncClient() as client:
        response = await client.get(
            f"{SONARR_URL}/api/v3/qualityprofile",
            headers={'X-Api-Key': SONARR_API_KEY}
        )
        sonarr_profiles = response.json()
        
        uhd_hd_profile = next((p for p in sonarr_profiles if p['name'] == 'UHD - HD'), None)
        if uhd_hd_profile:
            print(f"✅ Perfil 'UHD - HD' encontrado en Sonarr:")
            print(f"   - ID: {uhd_hd_profile['id']}")
            print(f"   - Nombre: {uhd_hd_profile['name']}")
            print(f"   - Cutoff: {uhd_hd_profile['cutoff']}")
            
            # Mostrar calidades permitidas
            allowed_qualities = []
            for item in uhd_hd_profile['items']:
                if item.get('allowed'):
                    if 'quality' in item:
                        allowed_qualities.append(f"{item['quality']['name']} ({item['quality']['resolution']}p)")
                    elif 'name' in item:
                        allowed_qualities.append(item['name'])
            
            print(f"   - Calidades permitidas: {', '.join(allowed_qualities)}")
        else:
            print("❌ Perfil 'UHD - HD' NO encontrado en Sonarr")
    
    # Verificar perfiles de Radarr
    print("\n📊 Verificando perfiles de Radarr...")
    async with httpx.AsyncClient() as client:
        response = await client.get(
            f"{RADARR_URL}/api/v3/qualityprofile",
            headers={'X-Api-Key': RADARR_API_KEY}
        )
        radarr_profiles = response.json()
        
        uhd_hd_profile = next((p for p in radarr_profiles if p['name'] == 'UHD - HD'), None)
        if uhd_hd_profile:
            print(f"✅ Perfil 'UHD - HD' encontrado en Radarr:")
            print(f"   - ID: {uhd_hd_profile['id']}")
            print(f"   - Nombre: {uhd_hd_profile['name']}")
            print(f"   - Cutoff: {uhd_hd_profile['cutoff']}")
            
            # Mostrar calidades permitidas
            allowed_qualities = []
            for item in uhd_hd_profile['items']:
                if item.get('allowed'):
                    if 'quality' in item:
                        allowed_qualities.append(f"{item['quality']['name']} ({item['quality']['resolution']}p)")
                    elif 'name' in item:
                        allowed_qualities.append(item['name'])
            
            print(f"   - Calidades permitidas: {', '.join(allowed_qualities)}")
        else:
            print("❌ Perfil 'UHD - HD' NO encontrado en Radarr")

async def test_add_with_quality_profile():
    """Probar agregar contenido con el perfil de calidad correcto"""
    print("\n🎬 Probando agregar contenido con perfil 'UHD - HD'...")
    
    # Datos de prueba para una serie
    series_data = {
        "title": "Test Series Quality",
        "tvdbId": 999999,  # ID ficticio para prueba
        "qualityProfileId": 7,  # UHD - HD
        "languageProfileId": 1,
        "rootFolderPath": "/CONTENIDO/SERIES",
        "monitored": True,
        "addOptions": {"searchForMissingEpisodes": True}
    }
    
    print(f"📤 Datos de prueba para Sonarr:")
    print(json.dumps(series_data, indent=2))
    
    # Datos de prueba para una película
    movie_data = {
        "title": "Test Movie Quality",
        "tmdbId": 999999,  # ID ficticio para prueba
        "qualityProfileId": 7,  # UHD - HD
        "rootFolderPath": "/CONTENIDO/PELICULAS",
        "monitored": True,
        "addOptions": {"searchForMovie": True}
    }
    
    print(f"\n📤 Datos de prueba para Radarr:")
    print(json.dumps(movie_data, indent=2))
    
    print("\n⚠️  NOTA: Estos son datos de prueba con IDs ficticios.")
    print("   El bot ahora usará el perfil 'UHD - HD' (ID: 7) que garantiza:")
    print("   ✅ Mínimo 1080p")
    print("   ✅ Incluye 4K/2160p")
    print("   ✅ Excluye calidades SD/720p")

async def main():
    print("🔧 Verificando configuración de perfiles de calidad del bot...")
    print("="*70)
    
    await test_quality_profile_usage()
    await test_add_with_quality_profile()
    
    print("\n" + "="*70)
    print("🎉 Verificación completada!")
    print("\n📝 Resumen de cambios:")
    print("   ❌ ANTES: Perfil 'Español' (ID: 1) - Calidad horrible (incluía CAM, WORKPRINT, etc.)")
    print("   ✅ AHORA: Perfil 'UHD - HD' (ID: 7) - Solo 1080p y 4K")
    print("\n🚀 Próximos pasos:")
    print("   1. Prueba agregar contenido en Telegram: /agregar [nombre]")
    print("   2. Verifica que se descargue en 1080p o superior")
    print("   3. Los logs del bot mostrarán: 'Usando perfil de calidad UHD - HD (ID: 7)'")

if __name__ == "__main__":
    asyncio.run(main())
