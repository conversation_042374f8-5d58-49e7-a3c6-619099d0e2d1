# Homepage API Keys Configuration - Configuración Centralizada de API Keys
# IMPORTANTE: Este archivo contiene información sensible. NO lo subas a repositorios públicos.
# Asegúrate de añadir este archivo a tu .gitignore

# === INSTRUCCIONES DE USO ===
# 1. Reemplaza cada "TU_API_KEY_XXXX" con la API key real del servicio correspondiente
# 2. Guarda el archivo
# 3. Reinicia el contenedor de Homepage: docker-compose restart homepage


# === ADGUARD HOME ===
# SERVICIO ELIMINADO - Referencias mantenidas para referencia histórica
# HOMEPAGE_VAR_ADGUARD_USERNAME=Tankeeee2_GAMES
# HOMEPAGE_VAR_ADGUARD_PASSWORD=Ahmadmuhsin4148!

# === SONARR ===
# Dónde obtenerla: Sonarr → Configuración → General → Seguridad → API Key
HOMEPAGE_VAR_SONARR_KEY=2f9da07e98744f4890c0960d15ead111

# === RADARR ===
# Dónde obtenerla: Radarr → Configuración → General → Seguridad → API Key
HOMEPAGE_VAR_RADARR_KEY=cfc0cde90b0f483eb4190dc634ca86f2

# === JACKETT ===
# Dónde obtenerla: Jackett → Panel principal → API Key (visible en la parte superior)
HOMEPAGE_VAR_JACKETT_KEY=xb68zjzfmw3cnbbmufcxr9ou7kh8te37

# === JELLYSEERR ===
# Dónde obtenerla: Jellyseerr → Configuración → General → API Key
HOMEPAGE_VAR_JELLYSEERR_KEY=MTc1Mjc2MDk0NTQ5MzIzMjNhMzVmLWRlOTktNDkxMi04Y2IyLTYzNDU4OTFiN2U5Yg==

# === JELLYFIN ===
# Dónde obtenerla: Jellyfin → Panel de administración → API Keys → Crear nueva
HOMEPAGE_VAR_JELLYFIN_KEY=6a794b57ff4d4d10bf31875612761d8e

# === QBITTORRENT ===
# Configuración: qBittorrent → Herramientas → Opciones → Web UI
# Usar las credenciales configuradas para el Web UI
HOMEPAGE_VAR_QBITTORRENT_USERNAME=Tankeeee2_GAMES
HOMEPAGE_VAR_QBITTORRENT_PASSWORD=Ahmadmuhsin4148!

# === PORTAINER ===
# Dónde obtenerla: Portainer → User settings → Access tokens → Add access token
HOMEPAGE_VAR_PORTAINER_KEY=ptr_8QIjVt+OCoRdD7VBikGe4VKZc58gZRwnbKzU1eFIaVQ=

# === SERVICIOS EXTERNOS (OPCIONALES) ===
# OpenWeatherMap - Para widget de clima
# Registrarse en: https://openweathermap.org/api
HOMEPAGE_VAR_OPENWEATHER_KEY=TU_API_KEY_OPENWEATHER

# WeatherAPI - Alternativa para clima
# Registrarse en: https://www.weatherapi.com/
HOMEPAGE_VAR_WEATHERAPI_KEY=TU_API_KEY_WEATHERAPI

# === CONFIGURACIÓN DE AUTENTICACIÓN BASE64 ===
# Para servicios que requieren autenticación básica HTTP
# Generar con: echo -n "usuario:password" | base64
# HOMEPAGE_VAR_ADGUARD_AUTH_BASE64=VGFua2VlZWUyX0dBTUVTOkFobWFkbXVoc2luNDE0OCE= # ELIMINADO

# === CONFIGURACIÓN DE URLS INTERNAS ===
# URLs internas para comunicación entre contenedores
HOMEPAGE_VAR_JELLYFIN_URL=http://host.docker.internal:8096
HOMEPAGE_VAR_SONARR_URL=http://host.docker.internal:8989
HOMEPAGE_VAR_RADARR_URL=http://host.docker.internal:7878
HOMEPAGE_VAR_JACKETT_URL=http://host.docker.internal:9117
HOMEPAGE_VAR_JELLYSEERR_URL=http://host.docker.internal:5055
HOMEPAGE_VAR_QBITTORRENT_URL=http://host.docker.internal:8091
# HOMEPAGE_VAR_ADGUARD_URL=http://host.docker.internal:8080 # ELIMINADO
HOMEPAGE_VAR_PORTAINER_URL=http://host.docker.internal:9000
HOMEPAGE_VAR_HOMEASSISTANT_URL=http://host.docker.internal:8123
HOMEPAGE_VAR_FLARESOLVERR_URL=http://host.docker.internal:8191

# === CONFIGURACIÓN DE DOMINIOS EXTERNOS ===
# Dominios públicos para acceso externo
HOMEPAGE_VAR_JELLYFIN_DOMAIN=https://tankeflix.duckdns.org
HOMEPAGE_VAR_SONARR_DOMAIN=https://tankesonarr.duckdns.org
HOMEPAGE_VAR_RADARR_DOMAIN=https://tankeradarr.duckdns.org
HOMEPAGE_VAR_JACKETT_DOMAIN=https://tankejackett.duckdns.org
HOMEPAGE_VAR_JELLYSEERR_DOMAIN=https://tankejellyseerr.duckdns.org
HOMEPAGE_VAR_QBITTORRENT_DOMAIN=https://tanketorrent.duckdns.org
# HOMEPAGE_VAR_ADGUARD_DOMAIN=https://tankeguard.duckdns.org # ELIMINADO

# === CONFIGURACIÓN DE TIMEOUTS ===
# Timeouts para widgets (en milisegundos)
HOMEPAGE_VAR_WIDGET_TIMEOUT=10000

# === CONFIGURACIÓN DE INTERVALOS DE ACTUALIZACIÓN ===
# Intervalos de actualización para widgets (en segundos)
HOMEPAGE_VAR_REFRESH_INTERVAL=30

# === CONFIGURACIÓN DE LOGS ===
# Nivel de logging para Homepage
HOMEPAGE_VAR_LOG_LEVEL=info

# === NOTAS IMPORTANTES ===
# 1. Después de configurar las API keys, reinicia Homepage:
#    docker-compose restart homepage
#
# 2. Para verificar que las API keys funcionan, revisa los logs:
#    docker logs homepage
#
# 3. Si un widget no funciona, verifica:
#    - La API key es correcta
#    - El servicio está ejecutándose
#    - La URL es accesible desde el contenedor de Homepage
#
# 4. Para servicios que requieren autenticación básica:
#    echo -n "usuario:password" | base64
#    Copia el resultado en la variable correspondiente
#
# 5. Mantén este archivo seguro y no lo compartas públicamente
