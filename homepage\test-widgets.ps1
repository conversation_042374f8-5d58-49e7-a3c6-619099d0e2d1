# Script para verificar el funcionamiento de widgets de Homepage
# Test Homepage Widgets Functionality

Write-Host "=== VERIFICACIÓN DE WIDGETS DE HOMEPAGE ===" -ForegroundColor Cyan
Write-Host ""

# Función para probar API
function Test-ServiceAPI {
    param(
        [string]$ServiceName,
        [string]$URL,
        [hashtable]$Headers = @{},
        [string]$ExpectedContent = ""
    )
    
    Write-Host "🔍 Probando $ServiceName..." -ForegroundColor Yellow
    
    try {
        $response = Invoke-WebRequest -Uri $URL -Headers $Headers -TimeoutSec 10 -UseBasicParsing -ErrorAction Stop
        Write-Host "  ✅ Status: $($response.StatusCode)" -ForegroundColor Green
        
        if ($ExpectedContent -and $response.Content -match $ExpectedContent) {
            Write-Host "  ✅ Contenido esperado encontrado" -ForegroundColor Green
        }
        elseif ($ExpectedContent) {
            Write-Host "  ⚠️  Contenido esperado no encontrado" -ForegroundColor Yellow
        }
        
        return $true
    }
    catch {
        Write-Host "  ❌ Error: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Configuración de APIs
$qBittorrentAuth = @{
    "Content-Type" = "application/x-www-form-urlencoded"
}

$adguardAuth = @{
    "Authorization" = "Basic VGFua2VlZWUyX0dBTUVTOkFobWFkbXVoc2luNDE0OCE="
}

Write-Host "📊 Verificando APIs de servicios..." -ForegroundColor Cyan
Write-Host ""

# Probar cada servicio
$results = @{}

# Jellyfin
$results["Jellyfin"] = Test-ServiceAPI -ServiceName "Jellyfin" -URL "http://localhost:8096/System/Info/Public" -ExpectedContent "ServerName"

# Sonarr
$results["Sonarr"] = Test-ServiceAPI -ServiceName "Sonarr" -URL "http://localhost:8989/api/v3/system/status?apikey=2f9da07e98744f4890c0960d15ead111" -ExpectedContent "version"

# Radarr
$results["Radarr"] = Test-ServiceAPI -ServiceName "Radarr" -URL "http://localhost:7878/api/v3/system/status?apikey=cfc0cde90b0f483eb4190dc634ca86f2" -ExpectedContent "version"

# Jackett
$results["Jackett"] = Test-ServiceAPI -ServiceName "Jackett" -URL "http://localhost:9117/api/v2.0/server/config?apikey=xb68zjzfmw3cnbbmufcxr9ou7kh8te37" -ExpectedContent "server_version"

# qBittorrent (requiere login primero)
Write-Host "🔍 Probando qBittorrent (con autenticación)..." -ForegroundColor Yellow
try {
    # Primero hacer login
    $loginBody = "username=Tankeeee2_GAMES&password=Ahmadmuhsin4148!"
    $loginResponse = Invoke-WebRequest -Uri "http://localhost:8091/api/v2/auth/login" -Method POST -Body $loginBody -Headers $qBittorrentAuth -SessionVariable qbSession -UseBasicParsing
    
    if ($loginResponse.StatusCode -eq 200) {
        # Probar API con sesión
        $torrentResponse = Invoke-WebRequest -Uri "http://localhost:8091/api/v2/torrents/info" -WebSession $qbSession -UseBasicParsing
        Write-Host "  ✅ qBittorrent API funcional" -ForegroundColor Green
        $results["qBittorrent"] = $true
    }
    else {
        Write-Host "  ❌ Error de login en qBittorrent" -ForegroundColor Red
        $results["qBittorrent"] = $false
    }
}
catch {
    Write-Host "  ❌ Error en qBittorrent: $($_.Exception.Message)" -ForegroundColor Red
    $results["qBittorrent"] = $false
}

# AdGuard Home
$results["AdGuard"] = Test-ServiceAPI -ServiceName "AdGuard Home" -URL "http://localhost:8080/control/stats" -Headers $adguardAuth -ExpectedContent "num_dns_queries"

# Jellyseerr
$results["Jellyseerr"] = Test-ServiceAPI -ServiceName "Jellyseerr" -URL "http://localhost:5055/api/v1/status" -ExpectedContent "version"

# Portainer
$results["Portainer"] = Test-ServiceAPI -ServiceName "Portainer" -URL "http://localhost:9000/api/status" -ExpectedContent "Version"

Write-Host ""
Write-Host "=== RESUMEN DE RESULTADOS ===" -ForegroundColor Cyan
Write-Host ""

$successCount = 0
$totalCount = $results.Count

foreach ($service in $results.GetEnumerator()) {
    if ($service.Value) {
        Write-Host "✅ $($service.Key): Funcionando" -ForegroundColor Green
        $successCount++
    }
    else {
        Write-Host "❌ $($service.Key): Con problemas" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "📊 Resultado: $successCount/$totalCount servicios funcionando correctamente" -ForegroundColor Cyan

if ($successCount -eq $totalCount) {
    Write-Host "🎉 ¡Todos los widgets deberían funcionar perfectamente!" -ForegroundColor Green
}
elseif ($successCount -gt ($totalCount / 2)) {
    Write-Host "⚠️  La mayoría de widgets funcionan. Revisa los servicios con problemas." -ForegroundColor Yellow
}
else {
    Write-Host "🚨 Varios servicios tienen problemas. Revisa la configuración." -ForegroundColor Red
}

Write-Host ""
Write-Host "=== PRÓXIMOS PASOS ===" -ForegroundColor Cyan
Write-Host ""
Write-Host "1. Reinicia Homepage para aplicar cambios:" -ForegroundColor White
Write-Host "   docker restart homepage" -ForegroundColor Gray
Write-Host ""
Write-Host "2. Accede a Homepage para ver los widgets:" -ForegroundColor White
Write-Host "   🌐 Local: http://localhost:3001" -ForegroundColor Gray
Write-Host "   🌐 Externo: https://tankeeee2.duckdns.org" -ForegroundColor Gray
Write-Host ""
Write-Host "3. Si hay problemas, revisa los logs:" -ForegroundColor White
Write-Host "   docker logs homepage --tail 20" -ForegroundColor Gray
