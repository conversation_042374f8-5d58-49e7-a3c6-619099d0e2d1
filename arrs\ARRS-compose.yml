version: "3.8"

services:
  # --- QBitTorrent ---
  qbittorrent:
    image: lscr.io/linuxserver/qbittorrent:latest
    container_name: qbittorrent
    environment:
      - PUID=1000
      - PGID=1000
      - TZ=Europe/Madrid
      - WEBUI_PORT=8091
    volumes:
      - C:\docker\arrs\qbittorrent:/config
      - C:\Users\<USER>\Downloads:/Descargas_C
      - D:\CONTENIDO:/CONTENIDO
      - D:/:/volumenes/Disco_D
      - E:/:/volumenes/Disco_E
      - F:/:/volumenes/Disco_F
    ports:
      - "8091:8091"
      - "6881:6881"
      - "6881:6881/udp"
    restart: unless-stopped
    networks:
      - arr_net
    # Etiquetas para Watchtower - habilitar actualizaciones automáticas
    labels:
      - "com.centurylinklabs.watchtower.enable=true"

  # --- Sonarr ---
  sonarr:
    image: lscr.io/linuxserver/sonarr:latest
    container_name: sonarr
    environment:
      - PUID=1000
      - PGID=1000
      - TZ=Europe/Madrid
    volumes:
      - C:\docker\arrs\sonarr\config:/config
      - D:\CONTENIDO:/CONTENIDO
    ports:
      - "8989:8989"
    restart: unless-stopped
    networks:
      - arr_net
    # Etiquetas para Watchtower - habilitar actualizaciones automáticas
    labels:
      - "com.centurylinklabs.watchtower.enable=true"

  # --- Radarr ---
  radarr:
    image: lscr.io/linuxserver/radarr:latest
    container_name: radarr
    environment:
      - PUID=1000
      - PGID=1000
      - TZ=Europe/Madrid
    volumes:
      - C:\docker\arrs\radarr:/config
      - D:\CONTENIDO:/CONTENIDO
    ports:
      - "7878:7878"
    restart: unless-stopped
    networks:
      - arr_net
    # Etiquetas para Watchtower - habilitar actualizaciones automáticas
    labels:
      - "com.centurylinklabs.watchtower.enable=true"

  # --- Jackett ---
  jackett:
    image: lscr.io/linuxserver/jackett:latest
    container_name: jackett
    environment:
      - PUID=1000
      - PGID=1000
      - TZ=Europe/Madrid
    volumes:
      - C:\docker\arrs\jackett\config:/config
    ports:
      - "9117:9117"
    restart: unless-stopped
    networks:
      - arr_net
    # Etiquetas para Watchtower - habilitar actualizaciones automáticas
    labels:
      - "com.centurylinklabs.watchtower.enable=true"

  # --- Jellyfin ---
  jellyfin:
    image: jellyfin/jellyfin:latest
    container_name: jellyfin
    environment:
      - PUID=1000
      - PGID=1000
      - TZ=Europe/Madrid
    volumes:
      - C:\docker\arrs\jellyfin\config:/config
      - C:\docker\arrs\jellyfin\cache:/cache
      - D:\CONTENIDO\:/CONTENIDO
    ports:
      - "8096:8096"
    restart: unless-stopped
    networks:
      - arr_net
    # Etiquetas para Watchtower - habilitar actualizaciones automáticas
    labels:
      - "com.centurylinklabs.watchtower.enable=true"

  # --- Jellyseerr ---
  jellyseerr:
    image: fallenbagel/jellyseerr:latest
    container_name: jellyseerr
    environment:
      - PUID=1000
      - PGID=1000
      - TZ=Europe/Madrid
    volumes:
      - C:\docker\arrs\jellyseerr:/app/config
    ports:
      - "5055:5055"
    restart: unless-stopped
    networks:
      - arr_net
    # Etiquetas para Watchtower - habilitar actualizaciones automáticas
    labels:
      - "com.centurylinklabs.watchtower.enable=true"

# --- FlareSolverr ---
  flaresolverr:
    image: ghcr.io/flaresolverr/flaresolverr:latest
    container_name: flaresolverr
    environment:
      - TZ=Europe/Madrid
      - LOG_LEVEL=info
    ports:
      - "8191:8191"
    restart: unless-stopped
    networks:
      - arr_net
      
# --- Definición de la red ---
networks:
  arr_net:
    driver: bridge
    name: arr_net
