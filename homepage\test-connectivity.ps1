# Script para verificar conectividad de servicios desde Homepage
# Test Homepage Widget Connectivity

Write-Host "=== VERIFICACIÓN DE CONECTIVIDAD DE SERVICIOS ===" -ForegroundColor Cyan
Write-Host ""

# Servicios a verificar
$services = @(
    @{Name="Jellyfin"; URL="http://host.docker.internal:8096"; Port=8096},
    @{Name="Sonarr"; URL="http://host.docker.internal:8989"; Port=8989},
    @{Name="Radarr"; URL="http://host.docker.internal:7878"; Port=7878},
    @{Name="Jackett"; URL="http://host.docker.internal:9117"; Port=9117},
    @{Name="qBittorrent"; URL="http://host.docker.internal:8091"; Port=8091},
    @{Name="AdGuard Home"; URL="http://host.docker.internal:8080"; Port=8080},
    @{Name="Jellyseerr"; URL="http://host.docker.internal:5055"; Port=5055},
    @{Name="Portainer"; URL="http://host.docker.internal:9000"; Port=9000}
)

foreach ($service in $services) {
    Write-Host "🔍 Verificando $($service.Name)..." -ForegroundColor Yellow
    
    # Verificar si el puerto está abierto en el host
    try {
        $connection = Test-NetConnection -ComputerName "localhost" -Port $service.Port -WarningAction SilentlyContinue
        if ($connection.TcpTestSucceeded) {
            Write-Host "  ✅ Puerto $($service.Port) abierto en el host" -ForegroundColor Green
            
            # Verificar conectividad HTTP
            try {
                $response = Invoke-WebRequest -Uri "http://localhost:$($service.Port)" -TimeoutSec 5 -UseBasicParsing -ErrorAction Stop
                Write-Host "  ✅ HTTP responde (Status: $($response.StatusCode))" -ForegroundColor Green
            }
            catch {
                Write-Host "  ⚠️  HTTP no responde: $($_.Exception.Message)" -ForegroundColor Yellow
            }
        }
        else {
            Write-Host "  ❌ Puerto $($service.Port) cerrado en el host" -ForegroundColor Red
        }
    }
    catch {
        Write-Host "  ❌ Error verificando puerto: $($_.Exception.Message)" -ForegroundColor Red
    }

    Write-Host ""
}

# Verificar conectividad desde el contenedor de Homepage
Write-Host "🐳 Verificando conectividad desde el contenedor Homepage..." -ForegroundColor Cyan
Write-Host ""

foreach ($service in $services) {
    Write-Host "🔍 Probando $($service.Name) desde Homepage..." -ForegroundColor Yellow
    
    try {
        # Usar docker exec para probar conectividad desde el contenedor
        $result = docker exec homepage sh -c "wget --spider --quiet --timeout=5 $($service.URL) 2>&1; echo \$?" 2>$null
        
        if ($result -match "0$") {
            Write-Host "  ✅ Accesible desde Homepage" -ForegroundColor Green
        }
        else {
            Write-Host "  ❌ No accesible desde Homepage" -ForegroundColor Red
        }
    }
    catch {
        Write-Host "  ❌ Error probando desde Homepage: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "=== VERIFICACIÓN DE LOGS DE HOMEPAGE ===" -ForegroundColor Cyan
Write-Host ""

# Mostrar logs recientes de Homepage
Write-Host "📋 Logs recientes de Homepage:" -ForegroundColor Yellow
docker logs homepage --tail 10

Write-Host ""
Write-Host "=== VERIFICACIÓN COMPLETA ===" -ForegroundColor Cyan
Write-Host ""
Write-Host "Si todos los servicios muestran ✅, los widgets deberían funcionar correctamente." -ForegroundColor Green
Write-Host "Si hay errores ❌, verifica que los servicios estén ejecutándose:" -ForegroundColor Yellow
Write-Host "  docker ps | findstr 'jellyfin\|sonarr\|radarr\|jackett\|qbittorrent\|adguard\|jellyseerr\|portainer'" -ForegroundColor White
Write-Host ""
Write-Host "Para ver Homepage en acción, accede a:" -ForegroundColor Cyan
Write-Host "  🌐 Local: http://localhost:3001" -ForegroundColor White
Write-Host "  🌐 Externo: https://tankeeee2.duckdns.org" -ForegroundColor White
