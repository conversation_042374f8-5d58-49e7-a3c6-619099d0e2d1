# Homepage Services Configuration - Configuración Completa con Widgets
# Documentación: https://gethomepage.dev/latest/configs/services/

# === SERVICIOS DE MEDIA ===
- Servicios:
    - Jellyfin:
        icon: "jellyfin.png"
        href: "https://tankeflix.duckdns.org"
        description: "Servidor multimedia personal"
        widget:
          type: "jellyfin"
          url: "http://host.docker.internal:8096"
          key: "6a794b57ff4d4d10bf31875612761d8e"
          enableBlocks: true
          enableNowPlaying: true
          enableUser: true
          showEpisodeNumber: true
          expandOneStreamToTwoRows: false
    - Jellyseerr:
        icon: "jellyseerr.png"
        href: "https://tankejellyseerr.duckdns.org"
        description: "Gestión de solicitudes de contenido"
        widget:
          type: "jellyseerr"
          url: "http://host.docker.internal:5055"
          key: "MTc1Mjc2MDk0NTQ5MzIzMjNhMzVmLWRlOTktNDkxMi04Y2IyLTYzNDU4OTFiN2U5Yg=="
    - Sonarr:
        icon: "sonarr.png"
        href: "https://tankesonarr.duckdns.org"
        description: "Gestión automática de series"
        widget:
          type: "sonarr"
          url: "http://host.docker.internal:8989"
          key: "2f9da07e98744f4890c0960d15ead111"
          enableQueue: true
    - Radarr:
        icon: "radarr.png"
        href: "https://tankeradarr.duckdns.org"
        description: "Gestión automática de películas"
        widget:
          type: "radarr"
          url: "http://host.docker.internal:7878"
          key: "cfc0cde90b0f483eb4190dc634ca86f2"
          enableQueue: true
    - Jackett:
        icon: "jackett.png"
        href: "https://tankejackett.duckdns.org"
        description: "Indexador de torrents"
        widget:
          type: "jackett"
          url: "http://host.docker.internal:9117"
          key: "xb68zjzfmw3cnbbmufcxr9ou7kh8te37"
    - qBittorrent:
        icon: "qbittorrent.png"
        href: "https://tanketorrent.duckdns.org"
        description: "Cliente torrent"
        widget:
          type: "qbittorrent"
          url: "http://host.docker.internal:8091"
          username: "Tankeeee2_GAMES"
          password: "Ahmadmuhsin4148!"
    - Portainer:
        icon: "portainer.png"
        href: "http://host.docker.internal:9000"
        description: "Gestión de contenedores Docker"
        # Widget temporalmente deshabilitado por problemas de endpoint
        # widget:
        #   type: "portainer"
        #   url: "http://host.docker.internal:9000"
        #   env: 2
        #   key: "ptr_8QIjVt+OCoRdD7VBikGe4VKZc58gZRwnbKzU1eFIaVQ="
