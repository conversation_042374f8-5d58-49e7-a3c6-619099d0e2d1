# Homepage Services Configuration - Layout Compacto
# Configuración optimizada para mostrar servicios en formato compacto (3 por fila)

# === SERVICIOS PRINCIPALES ===
- Servicios:
    - Jellyfin:
        icon: "jellyfin.png"
        href: "https://tankeflix.duckdns.org"
        description: "Media Server"
        widget:
          type: "jellyfin"
          url: "http://host.docker.internal:8096"
          key: "6a794b57ff4d4d10bf31875612761d8e"
          enableBlocks: true
          enableNowPlaying: false
          enableUser: false
    - Jellyseerr:
        icon: "jellyseerr.png"
        href: "https://tankejellyseerr.duckdns.org"
        description: "Solicitudes"
        widget:
          type: "jellyseerr"
          url: "http://host.docker.internal:5055"
          key: "MTc1Mjc2MDk0NTQ5MzIzMjNhMzVmLWRlOTktNDkxMi04Y2IyLTYzNDU4OTFiN2U5Yg=="
    - Sonarr:
        icon: "sonarr.png"
        href: "https://tankesonarr.duckdns.org"
        description: "Series TV"
        widget:
          type: "sonarr"
          url: "http://host.docker.internal:8989"
          key: "2f9da07e98744f4890c0960d15ead111"
          enableQueue: true
    - Radarr:
        icon: "radarr.png"
        href: "https://tankeradarr.duckdns.org"
        description: "Películas"
        widget:
          type: "radarr"
          url: "http://host.docker.internal:7878"
          key: "cfc0cde90b0f483eb4190dc634ca86f2"
          enableQueue: true
    - Jackett:
        icon: "jackett.png"
        href: "https://tankejackett.duckdns.org"
        description: "Indexador"
        widget:
          type: "jackett"
          url: "http://host.docker.internal:9117"
          key: "xb68zjzfmw3cnbbmufcxr9ou7kh8te37"
    - qBittorrent:
        icon: "qbittorrent.png"
        href: "https://tanketorrent.duckdns.org"
        description: "Torrents"
        widget:
          type: "qbittorrent"
          url: "http://host.docker.internal:8091"
          username: "Tankeeee2_GAMES"
          password: "Ahmadmuhsin4148!"
    - Portainer:
        icon: "portainer.png"
        href: "http://host.docker.internal:9000"
        description: "Docker Management"

# === SERVICIOS DE SISTEMA ===
- Sistema:
    - Homepage:
        icon: "homepage.png"
        href: "http://localhost:3001"
        description: "Dashboard"
    - Caddy:
        icon: "caddy.png"
        href: "https://caddyserver.com"
        description: "Proxy Reverso"
    - DuckDNS:
        icon: "duckdns.png"
        href: "https://duckdns.org"
        description: "DNS Dinámico"

# === PÁGINAS FRECUENTES ===
- Bookmarks:
    - YouTube:
        icon: "youtube.png"
        href: "https://youtube.com"
        description: "Videos"
    - GitHub:
        icon: "github.png"
        href: "https://github.com"
        description: "Repositorios"
    - Reddit:
        icon: "reddit.png"
        href: "https://reddit.com"
        description: "Comunidad"
    - Gmail:
        icon: "gmail.png"
        href: "https://gmail.com"
        description: "Email"
    - HBO:
        icon: "HBO.png"
        href: "https://hbo.com"
        description: "Streaming"
    - Amazon:
        icon: "amazon.png"
        href: "https://amazon.es"
        description: "Compras"
