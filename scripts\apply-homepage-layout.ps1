# apply-homepage-layout.ps1
# Script para aplicar el nuevo layout compacto de Homepage

function Write-Status {
    param($Message, $Type = "Info")
    
    switch ($Type) {
        "Success" { Write-Host "✅ $Message" -ForegroundColor Green }
        "Error" { Write-Host "❌ $Message" -ForegroundColor Red }
        "Warning" { Write-Host "⚠️  $Message" -ForegroundColor Yellow }
        "Info" { Write-Host "ℹ️  $Message" -ForegroundColor Blue }
        default { Write-Host "$Message" }
    }
}

function Test-ConfigurationFiles {
    Write-Status "Verificando archivos de configuración..." "Info"
    
    $ConfigFiles = @(
        @{Path="C:\docker\duckdns_updater\homepage\config\services.yaml"; Name="services.yaml"},
        @{Path="C:\docker\duckdns_updater\homepage\config\settings.yaml"; Name="settings.yaml"}
    )
    
    $AllValid = $true
    
    foreach ($File in $ConfigFiles) {
        if (Test-Path $File.Path) {
            Write-Status "$($File.Name) - Encontrado" "Success"
            
            # Verificar sintaxis YAML básica
            try {
                $Content = Get-Content $File.Path -Raw
                if ($Content -match "^\s*-\s+" -or $Content -match "^\s*\w+:") {
                    Write-Status "$($File.Name) - Sintaxis válida" "Success"
                } else {
                    Write-Status "$($File.Name) - Posible problema de sintaxis" "Warning"
                    $AllValid = $false
                }
            }
            catch {
                Write-Status "$($File.Name) - Error leyendo archivo" "Error"
                $AllValid = $false
            }
        } else {
            Write-Status "$($File.Name) - No encontrado" "Error"
            $AllValid = $false
        }
    }
    
    return $AllValid
}

function Show-LayoutChanges {
    Write-Host ""
    Write-Host "🎨 CAMBIOS APLICADOS AL LAYOUT:" -ForegroundColor Cyan
    Write-Host "===============================" -ForegroundColor Cyan
    Write-Host ""
    
    Write-Host "📱 Layout Compacto:" -ForegroundColor Yellow
    Write-Host "  • Servicios principales: 3 por fila" -ForegroundColor White
    Write-Host "  • Servicios de sistema: 3 por fila" -ForegroundColor White
    Write-Host "  • Bookmarks/Enlaces: 3 por fila" -ForegroundColor White
    Write-Host ""
    
    Write-Host "🔧 Servicios Principales (6 servicios):" -ForegroundColor Yellow
    Write-Host "  • Jellyfin (Media Server)" -ForegroundColor White
    Write-Host "  • Jellyseerr (Solicitudes)" -ForegroundColor White
    Write-Host "  • Sonarr (Series TV)" -ForegroundColor White
    Write-Host "  • Radarr (Películas)" -ForegroundColor White
    Write-Host "  • Jackett (Indexador)" -ForegroundColor White
    Write-Host "  • qBittorrent (Torrents)" -ForegroundColor White
    Write-Host "  • Portainer (Docker Management)" -ForegroundColor White
    Write-Host ""
    
    Write-Host "⚙️  Servicios de Sistema (3 servicios):" -ForegroundColor Yellow
    Write-Host "  • Homepage (Dashboard)" -ForegroundColor White
    Write-Host "  • Caddy (Proxy Reverso)" -ForegroundColor White
    Write-Host "  • DuckDNS (DNS Dinámico)" -ForegroundColor White
    Write-Host ""
    
    Write-Host "🔖 Bookmarks/Enlaces Frecuentes (6 enlaces):" -ForegroundColor Yellow
    Write-Host "  • YouTube, GitHub, Reddit" -ForegroundColor White
    Write-Host "  • Gmail, Netflix, Amazon" -ForegroundColor White
    Write-Host ""
    
    Write-Host "🎯 Optimizaciones:" -ForegroundColor Yellow
    Write-Host "  • Descripciones más cortas" -ForegroundColor White
    Write-Host "  • Layout compacto activado" -ForegroundColor White
    Write-Host "  • Widgets optimizados para menos espacio" -ForegroundColor White
    Write-Host "  • Errores ocultos para mejor UX" -ForegroundColor White
}

function Restart-Homepage {
    Write-Status "Reiniciando Homepage para aplicar cambios..." "Info"
    
    try {
        Set-Location "C:\docker\duckdns_updater"
        
        Write-Host "  Deteniendo Homepage..." -NoNewline
        docker-compose stop homepage 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host " ✅" -ForegroundColor Green
        } else {
            Write-Host " ⚠️" -ForegroundColor Yellow
        }
        
        Start-Sleep -Seconds 5
        
        Write-Host "  Iniciando Homepage..." -NoNewline
        docker-compose up -d homepage 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host " ✅" -ForegroundColor Green
        } else {
            Write-Host " ❌" -ForegroundColor Red
            return $false
        }
        
        Write-Status "Esperando que Homepage esté listo..." "Info"
        Start-Sleep -Seconds 20
        
        # Verificar que esté funcionando
        try {
            $Response = Invoke-WebRequest -Uri "http://localhost:3001" -UseBasicParsing -TimeoutSec 10
            if ($Response.StatusCode -eq 200) {
                Write-Status "Homepage funcionando con nuevo layout" "Success"
                return $true
            }
        }
        catch {
            Write-Status "Homepage iniciado pero aún cargando..." "Warning"
            return $false
        }
    }
    catch {
        Write-Status "Error reiniciando Homepage: $($_.Exception.Message)" "Error"
        return $false
    }
}

function Show-AccessInfo {
    Write-Host ""
    Write-Host "🌐 ACCESO A HOMEPAGE:" -ForegroundColor Cyan
    Write-Host "=====================" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Local:   http://localhost:3001" -ForegroundColor Green
    Write-Host "Externo: https://tankeeee2.duckdns.org" -ForegroundColor Green
    Write-Host ""
    Write-Host "💡 Si no ves los cambios inmediatamente:" -ForegroundColor Yellow
    Write-Host "  • Refresca la página (Ctrl+F5)" -ForegroundColor White
    Write-Host "  • Limpia caché del navegador" -ForegroundColor White
    Write-Host "  • Espera 1-2 minutos para que cargue completamente" -ForegroundColor White
}

# Función principal
Write-Host ""
Write-Host "🎨 APLICANDO LAYOUT COMPACTO DE HOMEPAGE" -ForegroundColor Cyan
Write-Host "=========================================" -ForegroundColor Cyan
Write-Host ""

# Verificar configuración
$ConfigValid = Test-ConfigurationFiles
Write-Host ""

if (-not $ConfigValid) {
    Write-Status "Hay problemas con los archivos de configuración" "Error"
    Write-Host ""
    Write-Host "💡 Revisa los archivos manualmente:" -ForegroundColor Yellow
    Write-Host "  • C:\docker\duckdns_updater\homepage\config\services.yaml" -ForegroundColor White
    Write-Host "  • C:\docker\duckdns_updater\homepage\config\settings.yaml" -ForegroundColor White
    exit 1
}

# Mostrar cambios
Show-LayoutChanges

# Confirmar aplicación
$Confirmation = Read-Host "`n¿Aplicar el nuevo layout reiniciando Homepage? (s/N)"
if ($Confirmation -ne 's' -and $Confirmation -ne 'S') {
    Write-Status "Operación cancelada" "Warning"
    Write-Host ""
    Write-Host "💡 Los cambios están guardados pero no aplicados." -ForegroundColor Yellow
    Write-Host "   Para aplicar manualmente: docker-compose restart homepage" -ForegroundColor White
    exit 0
}

Write-Host ""

# Reiniciar Homepage
$RestartSuccess = Restart-Homepage

Write-Host ""

if ($RestartSuccess) {
    Write-Status "¡Layout compacto aplicado exitosamente!" "Success"
    Show-AccessInfo
} else {
    Write-Status "Layout aplicado pero Homepage puede necesitar más tiempo" "Warning"
    Show-AccessInfo
    Write-Host ""
    Write-Host "🔧 Si hay problemas:" -ForegroundColor Yellow
    Write-Host "  • Ejecuta: .\fix-homepage-simple.ps1 -Diagnostic" -ForegroundColor White
    Write-Host "  • Revisa logs: docker logs homepage" -ForegroundColor White
}

Write-Host ""
