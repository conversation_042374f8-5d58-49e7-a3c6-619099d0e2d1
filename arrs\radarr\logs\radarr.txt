2025-07-31 17:40:35.8|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/api/v2.0/indexers/limetorrents/results/torznab/api?t=movie&cat=2000,8000,100467,104627,112972,121527,127246,136409,146065,151062,5000,5070&extended=1&apikey=(removed)&offset=0&limit=100: 400.BadRequest (3990 bytes)
<?xml version="1.0" encoding="UTF-8"?>
<error code="900" description="Jackett.Common.IndexerException: Exception (limetorrents): Name does not resolve&#xA; ---&gt; System.Net.Http.HttpRequestException: Name does not resolve (www.limetorrents.lol:443)&#xA; ---&gt; System.Net.Sockets.SocketException (0xFFFDFFFF): Name does not resolve&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)&#xA;   at System.Net.Sockets.Socket.&lt;ConnectAsync&gt;g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   --- End of inner exception stack trace ---&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)&#xA;   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DecompressionHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at FlareSolverrSharp.ClearanceHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpClient.&lt;SendAsync&gt;g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)&#xA;   at Jackett.Common.Utils.Clients.HttpWebClient2.Run(WebRequest webRequest) in ./Jackett.Common/Utils/Clients/HttpWebClient2.cs:line 180&#xA;   at Jackett.Common.Utils.Clients.WebClient.GetResultAsync(WebRequest request) in ./Jackett.Common/Utils/Clients/WebClient.cs:line 188&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.RequestWithCookiesAsync(String url, String cookieOverride, RequestType method, String referer, IEnumerable`1 data, Dictionary`2 headers, String rawbody, Nullable`1 emulateBrowser) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 617&#xA;   at Jackett.Common.Indexers.Definitions.CardigannIndexer.PerformQuery(TorznabQuery query) in ./Jackett.Common/Indexers/Definitions/CardigannIndexer.cs:line 1563&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 378&#xA;   --- End of inner exception stack trace ---&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 403&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 816&#xA;   at Jackett.Server.Controllers.ResultsController.Torznab(TorznabRequest request) in ./Jackett.Server/Controllers/ResultsController.cs:line 437" />
2025-07-31 17:40:35.9|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/29/2025 14:38:35 and 07/29/2025 15:40:35 UTC. Search may be required.
2025-07-31 17:40:36.1|Warn|Torznab|LimeTorrents HTTP request failed: [400:BadRequest] [GET] at [http://jackett:9117/api/v2.0/indexers/limetorrents/results/torznab/api?t=movie&cat=2000,8000,100467,104627,112972,121527,127246,136409,146065,151062,5000,5070&extended=1&apikey=(removed)&offset=0&limit=100]
2025-07-31 17:40:36.3|Warn|Torznab|Indexer MoviesDVDR rss sync didn't cover the period between 07/31/2025 14:38:36 and 07/31/2025 15:40:36 UTC. Search may be required.
2025-07-31 17:40:36.5|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/31/2025 14:38:36 and 07/31/2025 15:40:36 UTC. Search may be required.
2025-07-31 17:40:40.6|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/31/2025 14:38:40 and 07/31/2025 15:40:40 UTC. Search may be required.
2025-07-31 17:40:44.2|Info|DownloadDecisionMaker|Processing 615 releases
2025-07-31 17:41:39.5|Info|RssSyncService|RSS Sync Completed. Reports found: 615, Reports grabbed: 0
2025-07-31 17:50:19.1|Info|Bootstrap|Starting Radarr - /app/radarr/bin/Radarr - Version 5.26.2.10099
2025-07-31 17:50:20.4|Info|AppFolderInfo|Data directory is being overridden to [/config]
2025-07-31 17:50:20.7|Info|AppFolderInfo|Data directory is being overridden to [/config]
2025-07-31 17:50:24.4|Info|AppFolderInfo|Data directory is being overridden to [/config]
2025-07-31 17:50:25.8|Info|MigrationController|*** Migrating data source=/config/radarr.db;cache size=-20000;datetimekind=Utc;journal mode=Wal;pooling=True;version=3;busytimeout=100 ***
2025-07-31 17:50:26.2|Info|FluentMigrator.Runner.MigrationRunner|DatabaseEngineVersionCheck migrating
2025-07-31 17:50:26.4|Info|FluentMigrator.Runner.MigrationRunner|PerformDBOperation 
2025-07-31 17:50:26.5|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Performing DB Operation
2025-07-31 17:50:27.0|Info|DatabaseEngineVersionCheck|SQLite 3.49.2
2025-07-31 17:50:27.0|Info|FluentMigrator.Runner.MigrationRunner|=> 0.5459131s
2025-07-31 17:50:27.0|Info|FluentMigrator.Runner.MigrationRunner|DatabaseEngineVersionCheck migrated
2025-07-31 17:50:27.1|Info|FluentMigrator.Runner.MigrationRunner|=> 0.5742707s
2025-07-31 17:50:27.5|Info|MigrationController|*** Migrating data source=/config/logs.db;cache size=-20000;datetimekind=Utc;journal mode=Wal;pooling=True;version=3;busytimeout=100 ***
2025-07-31 17:50:27.9|Info|FluentMigrator.Runner.MigrationRunner|DatabaseEngineVersionCheck migrating
2025-07-31 17:50:27.9|Info|FluentMigrator.Runner.MigrationRunner|PerformDBOperation 
2025-07-31 17:50:28.0|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Performing DB Operation
2025-07-31 17:50:28.0|Info|DatabaseEngineVersionCheck|SQLite 3.49.2
2025-07-31 17:50:28.1|Info|FluentMigrator.Runner.MigrationRunner|=> 0.1236396s
2025-07-31 17:50:28.1|Info|FluentMigrator.Runner.MigrationRunner|DatabaseEngineVersionCheck migrated
2025-07-31 17:50:28.1|Info|FluentMigrator.Runner.MigrationRunner|=> 0.1515757s
2025-07-31 17:50:34.0|Info|Microsoft.Hosting.Lifetime|Now listening on: http://[::]:7878
2025-07-31 17:50:38.3|Info|CommandExecutor|Starting 2 threads for tasks.
2025-07-31 17:50:39.6|Info|Microsoft.Hosting.Lifetime|Application started. Press Ctrl+C to shut down.
2025-07-31 17:50:39.6|Info|Microsoft.Hosting.Lifetime|Hosting environment: Production
2025-07-31 17:50:39.7|Info|Microsoft.Hosting.Lifetime|Content root path: /app/radarr/bin
2025-07-31 17:50:40.4|Info|ManagedHttpDispatcher|IPv4 is available: True, IPv6 will be disabled
2025-07-31 18:12:09.0|Info|RssSyncService|Starting RSS Sync
2025-07-31 18:12:11.7|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/29/2025 15:40:35 and 07/29/2025 16:12:11 UTC. Search may be required.
2025-07-31 18:12:12.7|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/31/2025 15:40:36 and 07/31/2025 16:12:11 UTC. Search may be required.
2025-07-31 18:12:12.7|Warn|Torznab|Indexer MoviesDVDR rss sync didn't cover the period between 07/31/2025 15:40:36 and 07/31/2025 16:12:11 UTC. Search may be required.
2025-07-31 18:12:13.1|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/api/v2.0/indexers/limetorrents/results/torznab/api?t=movie&cat=2000,8000,100467,104627,112972,121527,127246,136409,146065,151062,5000,5070&extended=1&apikey=(removed)&offset=0&limit=100: 400.BadRequest (3990 bytes)
<?xml version="1.0" encoding="UTF-8"?>
<error code="900" description="Jackett.Common.IndexerException: Exception (limetorrents): Name does not resolve&#xA; ---&gt; System.Net.Http.HttpRequestException: Name does not resolve (www.limetorrents.lol:443)&#xA; ---&gt; System.Net.Sockets.SocketException (0xFFFDFFFF): Name does not resolve&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)&#xA;   at System.Net.Sockets.Socket.&lt;ConnectAsync&gt;g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   --- End of inner exception stack trace ---&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)&#xA;   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DecompressionHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at FlareSolverrSharp.ClearanceHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpClient.&lt;SendAsync&gt;g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)&#xA;   at Jackett.Common.Utils.Clients.HttpWebClient2.Run(WebRequest webRequest) in ./Jackett.Common/Utils/Clients/HttpWebClient2.cs:line 180&#xA;   at Jackett.Common.Utils.Clients.WebClient.GetResultAsync(WebRequest request) in ./Jackett.Common/Utils/Clients/WebClient.cs:line 188&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.RequestWithCookiesAsync(String url, String cookieOverride, RequestType method, String referer, IEnumerable`1 data, Dictionary`2 headers, String rawbody, Nullable`1 emulateBrowser) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 617&#xA;   at Jackett.Common.Indexers.Definitions.CardigannIndexer.PerformQuery(TorznabQuery query) in ./Jackett.Common/Indexers/Definitions/CardigannIndexer.cs:line 1563&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 378&#xA;   --- End of inner exception stack trace ---&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 403&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 816&#xA;   at Jackett.Server.Controllers.ResultsController.Torznab(TorznabRequest request) in ./Jackett.Server/Controllers/ResultsController.cs:line 437" />
2025-07-31 18:12:13.2|Warn|Torznab|LimeTorrents HTTP request failed: [400:BadRequest] [GET] at [http://jackett:9117/api/v2.0/indexers/limetorrents/results/torznab/api?t=movie&cat=2000,8000,100467,104627,112972,121527,127246,136409,146065,151062,5000,5070&extended=1&apikey=(removed)&offset=0&limit=100]
2025-07-31 18:12:15.8|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/31/2025 15:40:40 and 07/31/2025 16:12:15 UTC. Search may be required.
2025-07-31 18:12:17.6|Info|DownloadDecisionMaker|Processing 615 releases
2025-07-31 18:12:56.0|Info|RssSyncService|RSS Sync Completed. Reports found: 615, Reports grabbed: 0
2025-07-31 18:43:09.0|Info|RssSyncService|Starting RSS Sync
2025-07-31 18:43:09.1|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/29/2025 16:12:11 and 07/29/2025 16:12:11 UTC. Search may be required.
2025-07-31 18:43:09.1|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/31/2025 16:12:11 and 07/31/2025 16:12:11 UTC. Search may be required.
2025-07-31 18:43:09.3|Warn|Torznab|Indexer MoviesDVDR rss sync didn't cover the period between 07/31/2025 16:12:11 and 07/31/2025 16:12:11 UTC. Search may be required.
2025-07-31 18:43:11.2|Warn|Torznab|Indexer LimeTorrents rss sync didn't cover the period between 07/31/2025 14:02:35 and 07/31/2025 15:43:09 UTC. Search may be required.
2025-07-31 18:43:11.3|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/31/2025 16:12:15 and 07/31/2025 16:12:15 UTC. Search may be required.
2025-07-31 18:43:11.3|Info|DownloadDecisionMaker|Processing 715 releases
2025-07-31 18:43:47.8|Info|RssSyncService|RSS Sync Completed. Reports found: 715, Reports grabbed: 0
2025-07-31 19:14:09.2|Info|RssSyncService|Starting RSS Sync
2025-07-31 19:14:12.4|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/29/2025 16:12:11 and 07/29/2025 17:14:11 UTC. Search may be required.
2025-07-31 19:14:12.6|Warn|Torznab|Indexer MoviesDVDR rss sync didn't cover the period between 07/31/2025 16:12:11 and 07/31/2025 17:14:11 UTC. Search may be required.
2025-07-31 19:14:12.7|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/31/2025 16:12:11 and 07/31/2025 17:14:12 UTC. Search may be required.
2025-07-31 19:14:16.2|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/31/2025 16:12:15 and 07/31/2025 17:14:15 UTC. Search may be required.
2025-07-31 19:14:18.6|Info|DownloadDecisionMaker|Processing 715 releases
2025-07-31 19:15:31.4|Info|RssSyncService|RSS Sync Completed. Reports found: 715, Reports grabbed: 0
2025-07-31 19:45:39.3|Info|RssSyncService|Starting RSS Sync
2025-07-31 19:45:39.6|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/31/2025 17:14:12 and 07/31/2025 17:14:12 UTC. Search may be required.
2025-07-31 19:45:39.9|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/api/v2.0/indexers/limetorrents/results/torznab/api?t=movie&cat=2000,8000,100467,104627,112972,121527,127246,136409,146065,151062,5000,5070&extended=1&apikey=(removed)&offset=0&limit=100: 400.BadRequest (3990 bytes)
<?xml version="1.0" encoding="UTF-8"?>
<error code="900" description="Jackett.Common.IndexerException: Exception (limetorrents): Name does not resolve&#xA; ---&gt; System.Net.Http.HttpRequestException: Name does not resolve (www.limetorrents.lol:443)&#xA; ---&gt; System.Net.Sockets.SocketException (0xFFFDFFFF): Name does not resolve&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)&#xA;   at System.Net.Sockets.Socket.&lt;ConnectAsync&gt;g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   --- End of inner exception stack trace ---&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)&#xA;   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DecompressionHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at FlareSolverrSharp.ClearanceHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpClient.&lt;SendAsync&gt;g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)&#xA;   at Jackett.Common.Utils.Clients.HttpWebClient2.Run(WebRequest webRequest) in ./Jackett.Common/Utils/Clients/HttpWebClient2.cs:line 180&#xA;   at Jackett.Common.Utils.Clients.WebClient.GetResultAsync(WebRequest request) in ./Jackett.Common/Utils/Clients/WebClient.cs:line 188&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.RequestWithCookiesAsync(String url, String cookieOverride, RequestType method, String referer, IEnumerable`1 data, Dictionary`2 headers, String rawbody, Nullable`1 emulateBrowser) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 617&#xA;   at Jackett.Common.Indexers.Definitions.CardigannIndexer.PerformQuery(TorznabQuery query) in ./Jackett.Common/Indexers/Definitions/CardigannIndexer.cs:line 1563&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 378&#xA;   --- End of inner exception stack trace ---&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 403&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 816&#xA;   at Jackett.Server.Controllers.ResultsController.Torznab(TorznabRequest request) in ./Jackett.Server/Controllers/ResultsController.cs:line 437" />
2025-07-31 19:45:40.0|Warn|Torznab|LimeTorrents HTTP request failed: [400:BadRequest] [GET] at [http://jackett:9117/api/v2.0/indexers/limetorrents/results/torznab/api?t=movie&cat=2000,8000,100467,104627,112972,121527,127246,136409,146065,151062,5000,5070&extended=1&apikey=(removed)&offset=0&limit=100]
2025-07-31 19:45:41.8|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/31/2025 17:14:15 and 07/31/2025 17:14:15 UTC. Search may be required.
2025-07-31 19:45:41.9|Info|DownloadDecisionMaker|Processing 615 releases
2025-07-31 19:46:44.4|Info|RssSyncService|RSS Sync Completed. Reports found: 615, Reports grabbed: 0
2025-07-31 20:17:09.4|Info|RssSyncService|Starting RSS Sync
2025-07-31 20:17:10.3|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/31/2025 17:14:12 and 07/31/2025 18:17:10 UTC. Search may be required.
2025-07-31 20:17:10.3|Warn|Torznab|Indexer MoviesDVDR rss sync didn't cover the period between 07/31/2025 17:14:12 and 07/31/2025 18:17:10 UTC. Search may be required.
2025-07-31 20:17:11.6|Warn|Torznab|Indexer LimeTorrents rss sync didn't cover the period between 07/31/2025 16:42:09 and 07/31/2025 17:45:10 UTC. Search may be required.
2025-07-31 20:17:14.4|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/31/2025 17:14:15 and 07/31/2025 18:17:14 UTC. Search may be required.
2025-07-31 20:17:16.6|Info|DownloadDecisionMaker|Processing 715 releases
2025-07-31 20:17:56.6|Info|RssSyncService|RSS Sync Completed. Reports found: 715, Reports grabbed: 0
2025-07-31 20:48:09.5|Info|RssSyncService|Starting RSS Sync
2025-07-31 20:48:09.6|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/31/2025 18:17:10 and 07/31/2025 18:17:10 UTC. Search may be required.
2025-07-31 20:48:09.8|Warn|Torznab|Indexer MoviesDVDR rss sync didn't cover the period between 07/31/2025 18:17:10 and 07/31/2025 18:17:10 UTC. Search may be required.
2025-07-31 20:48:11.8|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/31/2025 18:17:14 and 07/31/2025 18:17:14 UTC. Search may be required.
2025-07-31 20:48:11.8|Info|DownloadDecisionMaker|Processing 715 releases
2025-07-31 20:48:49.9|Info|RssSyncService|RSS Sync Completed. Reports found: 715, Reports grabbed: 0
2025-07-31 21:19:09.7|Info|RssSyncService|Starting RSS Sync
2025-07-31 21:19:10.0|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/api/v2.0/indexers/limetorrents/results/torznab/api?t=movie&cat=2000,8000,100467,104627,112972,121527,127246,136409,146065,151062,5000,5070&extended=1&apikey=(removed)&offset=0&limit=100: 400.BadRequest (3990 bytes)
<?xml version="1.0" encoding="UTF-8"?>
<error code="900" description="Jackett.Common.IndexerException: Exception (limetorrents): Name does not resolve&#xA; ---&gt; System.Net.Http.HttpRequestException: Name does not resolve (www.limetorrents.lol:443)&#xA; ---&gt; System.Net.Sockets.SocketException (0xFFFDFFFF): Name does not resolve&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)&#xA;   at System.Net.Sockets.Socket.&lt;ConnectAsync&gt;g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   --- End of inner exception stack trace ---&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)&#xA;   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DecompressionHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at FlareSolverrSharp.ClearanceHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpClient.&lt;SendAsync&gt;g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)&#xA;   at Jackett.Common.Utils.Clients.HttpWebClient2.Run(WebRequest webRequest) in ./Jackett.Common/Utils/Clients/HttpWebClient2.cs:line 180&#xA;   at Jackett.Common.Utils.Clients.WebClient.GetResultAsync(WebRequest request) in ./Jackett.Common/Utils/Clients/WebClient.cs:line 188&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.RequestWithCookiesAsync(String url, String cookieOverride, RequestType method, String referer, IEnumerable`1 data, Dictionary`2 headers, String rawbody, Nullable`1 emulateBrowser) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 617&#xA;   at Jackett.Common.Indexers.Definitions.CardigannIndexer.PerformQuery(TorznabQuery query) in ./Jackett.Common/Indexers/Definitions/CardigannIndexer.cs:line 1563&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 378&#xA;   --- End of inner exception stack trace ---&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 403&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 816&#xA;   at Jackett.Server.Controllers.ResultsController.Torznab(TorznabRequest request) in ./Jackett.Server/Controllers/ResultsController.cs:line 437" />
2025-07-31 21:19:10.1|Warn|Torznab|LimeTorrents HTTP request failed: [400:BadRequest] [GET] at [http://jackett:9117/api/v2.0/indexers/limetorrents/results/torznab/api?t=movie&cat=2000,8000,100467,104627,112972,121527,127246,136409,146065,151062,5000,5070&extended=1&apikey=(removed)&offset=0&limit=100]
2025-07-31 21:19:10.2|Warn|Torznab|Indexer MoviesDVDR rss sync didn't cover the period between 07/31/2025 18:17:10 and 07/31/2025 19:19:10 UTC. Search may be required.
2025-07-31 21:19:10.4|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/31/2025 18:17:10 and 07/31/2025 19:19:10 UTC. Search may be required.
2025-07-31 21:19:14.5|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/31/2025 18:17:14 and 07/31/2025 19:19:14 UTC. Search may be required.
2025-07-31 21:19:16.7|Info|DownloadDecisionMaker|Processing 615 releases
2025-07-31 21:19:48.4|Info|RssSyncService|RSS Sync Completed. Reports found: 615, Reports grabbed: 0
2025-07-31 21:41:09.8|Info|RecycleBinProvider|Recycle Bin has not been configured, cannot cleanup.
2025-07-31 21:41:39.8|Info|HousekeepingService|Running housecleaning tasks
2025-07-31 21:41:40.9|Info|Database|Vacuuming Log database
2025-07-31 21:41:41.4|Info|Database|Log database compressed
2025-07-31 21:41:41.5|Info|Database|Vacuuming Main database
2025-07-31 21:41:43.0|Info|Database|Main database compressed
2025-07-31 21:46:40.2|Info|DiskScanService|Scanning disk for A Quiet Place: Day One
2025-07-31 21:46:40.4|Info|DiskScanService|Completed scanning disk for A Quiet Place: Day One
2025-07-31 21:46:40.5|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-31 21:46:40.5|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-31 21:46:40.5|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-31 21:46:40.5|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-31 21:46:40.6|Info|DiskScanService|Scanning disk for Avengers: Infinity War
2025-07-31 21:46:40.7|Info|DiskScanService|Completed scanning disk for Avengers: Infinity War
2025-07-31 21:46:40.7|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-31 21:46:40.7|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-31 21:46:40.8|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-31 21:46:40.8|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-31 21:46:40.9|Info|DiskScanService|Scanning disk for Babylon
2025-07-31 21:46:41.0|Info|DiskScanService|Completed scanning disk for Babylon
2025-07-31 21:46:41.0|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-31 21:46:41.1|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-31 21:46:41.1|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-31 21:46:41.1|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-31 21:46:41.1|Info|DiskScanService|Scanning disk for Captain America: Brave New World
2025-07-31 21:46:41.3|Info|DiskScanService|Completed scanning disk for Captain America: Brave New World
2025-07-31 21:46:41.3|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-31 21:46:41.3|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-31 21:46:41.3|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-31 21:46:41.3|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-31 21:46:41.4|Info|DiskScanService|Scanning disk for The Final Destination
2025-07-31 21:46:41.5|Info|DiskScanService|Completed scanning disk for The Final Destination
2025-07-31 21:46:41.5|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-31 21:46:41.6|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-31 21:46:41.6|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-31 21:46:41.6|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-31 21:46:41.6|Info|DiskScanService|Scanning disk for Django Unchained
2025-07-31 21:46:41.8|Info|DiskScanService|Completed scanning disk for Django Unchained
2025-07-31 21:46:41.8|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-31 21:46:41.8|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-31 21:46:41.8|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-31 21:46:41.9|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-31 21:46:41.9|Info|DiskScanService|Scanning disk for Final Destination
2025-07-31 21:46:42.0|Info|DiskScanService|Completed scanning disk for Final Destination
2025-07-31 21:46:42.1|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-31 21:46:42.1|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-31 21:46:42.1|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-31 21:46:42.1|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-31 21:46:42.2|Info|DiskScanService|Scanning disk for Final Destination 2
2025-07-31 21:46:42.3|Info|DiskScanService|Completed scanning disk for Final Destination 2
2025-07-31 21:46:42.3|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-31 21:46:42.3|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-31 21:46:42.3|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-31 21:46:42.3|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-31 21:46:42.4|Info|DiskScanService|Scanning disk for Final Destination 3
2025-07-31 21:46:42.5|Info|DiskScanService|Completed scanning disk for Final Destination 3
2025-07-31 21:46:42.5|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-31 21:46:42.5|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-31 21:46:42.6|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-31 21:46:42.6|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-31 21:46:42.6|Info|RefreshMovieService|Updating info for Final Destination 5
2025-07-31 21:46:43.0|Info|DiskScanService|Scanning disk for Final Destination 5
2025-07-31 21:46:43.1|Info|DiskScanService|Completed scanning disk for Final Destination 5
2025-07-31 21:46:43.2|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-31 21:46:43.2|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-31 21:46:43.2|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-31 21:46:43.2|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-31 21:46:43.3|Info|RefreshMovieService|Updating info for Final Destination Bloodlines
2025-07-31 21:46:43.5|Info|DiskScanService|Scanning disk for Final Destination Bloodlines
2025-07-31 21:46:43.6|Info|DiskScanService|Completed scanning disk for Final Destination Bloodlines
2025-07-31 21:46:43.6|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-31 21:46:43.6|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-31 21:46:43.7|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-31 21:46:43.7|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-31 21:46:43.8|Info|RefreshMovieService|Updating info for Inglourious Basterds
2025-07-31 21:46:44.0|Info|DiskScanService|Scanning disk for Inglourious Basterds
2025-07-31 21:46:44.1|Info|DiskScanService|Completed scanning disk for Inglourious Basterds
2025-07-31 21:46:44.2|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-31 21:46:44.2|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-31 21:46:44.2|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-31 21:46:44.2|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-31 21:46:44.3|Info|DiskScanService|Scanning disk for Jurassic Park
2025-07-31 21:46:44.4|Info|DiskScanService|Completed scanning disk for Jurassic Park
2025-07-31 21:46:44.4|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-31 21:46:44.5|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-31 21:46:44.5|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-31 21:46:44.5|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-31 21:46:44.5|Info|DiskScanService|Scanning disk for Ocean's Eight
2025-07-31 21:46:44.6|Info|DiskScanService|Completed scanning disk for Ocean's Eight
2025-07-31 21:46:44.7|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-31 21:46:44.7|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-31 21:46:44.7|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-31 21:46:44.7|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-31 21:46:44.8|Info|DiskScanService|Scanning disk for Ocean's Eleven
2025-07-31 21:46:44.9|Info|DiskScanService|Completed scanning disk for Ocean's Eleven
2025-07-31 21:46:45.0|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-31 21:46:45.0|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-31 21:46:45.0|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-31 21:46:45.0|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-31 21:46:45.1|Info|DiskScanService|Scanning disk for Ocean's Thirteen
2025-07-31 21:46:45.2|Info|DiskScanService|Completed scanning disk for Ocean's Thirteen
2025-07-31 21:46:45.2|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-31 21:46:45.2|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-31 21:46:45.3|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-31 21:46:45.3|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-31 21:46:45.3|Info|DiskScanService|Scanning disk for Ocean's Twelve
2025-07-31 21:46:45.5|Info|DiskScanService|Completed scanning disk for Ocean's Twelve
2025-07-31 21:46:45.5|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-31 21:46:45.5|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-31 21:46:45.6|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-31 21:46:45.6|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-31 21:46:45.6|Info|DiskScanService|Scanning disk for Oppenheimer
2025-07-31 21:46:45.7|Info|DiskScanService|Completed scanning disk for Oppenheimer
2025-07-31 21:46:45.8|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-31 21:46:45.8|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-31 21:46:45.8|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-31 21:46:45.8|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-31 21:46:45.9|Info|DiskScanService|Scanning disk for Prometheus
2025-07-31 21:46:46.0|Info|DiskScanService|Completed scanning disk for Prometheus
2025-07-31 21:46:46.0|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-31 21:46:46.1|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-31 21:46:46.1|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-31 21:46:46.1|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-31 21:46:46.2|Info|RefreshMovieService|Updating info for Shutter Island
2025-07-31 21:46:46.5|Info|DiskScanService|Scanning disk for Shutter Island
2025-07-31 21:46:46.6|Info|DiskScanService|Completed scanning disk for Shutter Island
2025-07-31 21:46:46.7|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-31 21:46:46.7|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-31 21:46:46.7|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-31 21:46:46.7|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-31 21:46:46.8|Info|RefreshMovieService|Updating info for The Accountant²
2025-07-31 21:46:46.9|Info|DiskScanService|Scanning disk for The Accountant²
2025-07-31 21:46:47.0|Info|DiskScanService|Completed scanning disk for The Accountant²
2025-07-31 21:46:47.0|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-31 21:46:47.1|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-31 21:46:47.1|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-31 21:46:47.1|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-31 21:46:47.2|Info|DiskScanService|Scanning disk for The Chronicles of Narnia: Prince Caspian
2025-07-31 21:46:47.3|Info|DiskScanService|Completed scanning disk for The Chronicles of Narnia: Prince Caspian
2025-07-31 21:46:47.3|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-31 21:46:47.3|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-31 21:46:47.3|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-31 21:46:47.4|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-31 21:46:47.5|Info|RefreshMovieService|Updating info for The Chronicles of Narnia: The Lion, the Witch and the Wardrobe
2025-07-31 21:46:47.7|Info|DiskScanService|Scanning disk for The Chronicles of Narnia: The Lion, the Witch and the Wardrobe
2025-07-31 21:46:47.7|Info|MediaCoverService|Downloading Poster for [The Chronicles of Narnia: The Lion, the Witch and the Wardrobe (2005)][tt0363771, 411] https://image.tmdb.org/t/p/original/iREd0rNCjYdf5Ar0vfaW32yrkm.jpg
2025-07-31 21:46:47.8|Info|DiskScanService|Completed scanning disk for The Chronicles of Narnia: The Lion, the Witch and the Wardrobe
2025-07-31 21:46:47.8|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-31 21:46:47.9|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-31 21:46:47.9|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-31 21:46:47.9|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-31 21:46:48.1|Info|RefreshMovieService|Updating info for The Chronicles of Narnia: The Voyage of the Dawn Treader
2025-07-31 21:46:48.5|Info|DiskScanService|Scanning disk for The Chronicles of Narnia: The Voyage of the Dawn Treader
2025-07-31 21:46:48.6|Info|MediaCoverService|Downloading Poster for [The Chronicles of Narnia: The Voyage of the Dawn Treader (2010)][tt0980970, 10140] https://image.tmdb.org/t/p/original/pP27zlm9yeKrCeDZLFLP2HKELot.jpg
2025-07-31 21:46:48.8|Info|DiskScanService|Completed scanning disk for The Chronicles of Narnia: The Voyage of the Dawn Treader
2025-07-31 21:46:49.1|Info|MediaCoverService|Downloading Fanart for [The Chronicles of Narnia: The Voyage of the Dawn Treader (2010)][tt0980970, 10140] https://image.tmdb.org/t/p/original/oc3be3waruLd0PB9h4bomN7Le3v.jpg
2025-07-31 21:46:49.2|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-31 21:46:49.4|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-31 21:46:49.6|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-31 21:46:49.6|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-31 21:46:50.1|Info|RefreshMovieService|Updating info for The Hateful Eight
2025-07-31 21:46:50.7|Info|DiskScanService|Scanning disk for The Hateful Eight
2025-07-31 21:46:50.7|Info|MediaCoverService|Downloading Fanart for [The Hateful Eight (2015)][tt3460252, 273248] https://image.tmdb.org/t/p/original/7gfDVfaw0VaIkUGiEH13o3TIC7A.jpg
2025-07-31 21:46:50.8|Info|DiskScanService|Completed scanning disk for The Hateful Eight
2025-07-31 21:46:50.9|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-31 21:46:50.9|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-31 21:46:50.9|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-31 21:46:50.9|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-31 21:46:51.0|Info|DiskScanService|Scanning disk for The Lord of the Rings: The Fellowship of the Ring
2025-07-31 21:46:51.1|Info|DiskScanService|Completed scanning disk for The Lord of the Rings: The Fellowship of the Ring
2025-07-31 21:46:51.2|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-31 21:46:51.2|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-31 21:46:51.2|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-31 21:46:51.2|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-31 21:46:51.3|Info|RefreshMovieService|Updating info for The Revenant
2025-07-31 21:46:51.5|Info|DiskScanService|Scanning disk for The Revenant
2025-07-31 21:46:51.6|Info|MediaCoverService|Downloading Fanart for [The Revenant (2015)][tt1663202, 281957] https://image.tmdb.org/t/p/original/sGOw65ZmfgGfistCjuNsazjs6j9.jpg
2025-07-31 21:46:51.6|Info|DiskScanService|Completed scanning disk for The Revenant
2025-07-31 21:46:51.7|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-31 21:46:51.7|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-31 21:46:51.7|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-31 21:46:51.8|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-31 21:46:51.8|Info|RefreshMovieService|Updating info for Until Dawn
2025-07-31 21:46:52.0|Info|DiskScanService|Scanning disk for Until Dawn
2025-07-31 21:46:52.1|Info|DiskScanService|Completed scanning disk for Until Dawn
2025-07-31 21:46:52.1|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-31 21:46:52.2|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-31 21:46:52.2|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-31 21:46:52.2|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-31 21:46:52.3|Info|RefreshMovieService|Updating info for Sinners
2025-07-31 21:46:52.5|Info|DiskScanService|Scanning disk for Sinners
2025-07-31 21:46:52.6|Info|DiskScanService|Completed scanning disk for Sinners
2025-07-31 21:46:52.7|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-31 21:46:52.7|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-31 21:46:52.7|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-31 21:46:52.7|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-31 21:46:52.8|Info|DiskScanService|Scanning disk for The Lost World: Jurassic Park
2025-07-31 21:46:52.9|Info|DiskScanService|Completed scanning disk for The Lost World: Jurassic Park
2025-07-31 21:46:52.9|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-31 21:46:52.9|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-31 21:46:52.9|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-31 21:46:52.9|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-31 21:46:53.0|Info|RefreshMovieService|Updating info for How to Train Your Dragon 2
2025-07-31 21:46:53.1|Info|DiskScanService|Scanning disk for How to Train Your Dragon 2
2025-07-31 21:46:53.2|Info|DiskScanService|Completed scanning disk for How to Train Your Dragon 2
2025-07-31 21:46:53.3|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-31 21:46:53.3|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-31 21:46:53.3|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-31 21:46:53.3|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-31 21:46:53.4|Info|RefreshMovieService|Updating info for How to Train Your Dragon
2025-07-31 21:46:53.6|Info|DiskScanService|Scanning disk for How to Train Your Dragon
2025-07-31 21:46:53.6|Info|MediaCoverService|Downloading Poster for [How to Train Your Dragon (2010)][tt0892769, 10191] https://image.tmdb.org/t/p/original/ygGmAO60t8GyqUo9xYeYxSZAR3b.jpg
2025-07-31 21:46:53.7|Info|DiskScanService|Completed scanning disk for How to Train Your Dragon
2025-07-31 21:46:53.7|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-31 21:46:53.7|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-31 21:46:53.8|Info|MediaCoverService|Downloading Fanart for [How to Train Your Dragon (2010)][tt0892769, 10191] https://image.tmdb.org/t/p/original/59vDC1BuEQvti24OMr0ZvtAK6R1.jpg
2025-07-31 21:46:53.8|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-31 21:46:53.8|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-31 21:46:53.8|Info|RefreshMovieService|Updating info for How to Train Your Dragon: The Hidden World
2025-07-31 21:46:54.1|Info|DiskScanService|Scanning disk for How to Train Your Dragon: The Hidden World
2025-07-31 21:46:54.1|Info|MediaCoverService|Downloading Fanart for [How to Train Your Dragon: The Hidden World (2019)][tt2386490, 166428] https://image.tmdb.org/t/p/original/h3KN24PrOheHVYs9ypuOIdFBEpX.jpg
2025-07-31 21:46:54.2|Info|DiskScanService|Completed scanning disk for How to Train Your Dragon: The Hidden World
2025-07-31 21:46:54.3|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-31 21:46:54.3|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-31 21:46:54.4|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-31 21:46:54.4|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-31 21:46:54.5|Info|RefreshMovieService|Updating info for Kung Fu Panda
2025-07-31 21:46:54.7|Info|DiskScanService|Scanning disk for Kung Fu Panda
2025-07-31 21:46:54.8|Info|DiskScanService|Completed scanning disk for Kung Fu Panda
2025-07-31 21:46:54.8|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-31 21:46:54.9|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-31 21:46:54.9|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-31 21:46:54.9|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-31 21:46:55.0|Info|RefreshMovieService|Updating info for Kung Fu Panda 2
2025-07-31 21:46:55.2|Info|DiskScanService|Scanning disk for Kung Fu Panda 2
2025-07-31 21:46:55.3|Info|MediaCoverService|Downloading Poster for [Kung Fu Panda 2 (2011)][tt1302011, 49444] https://image.tmdb.org/t/p/original/mtqqD00vB4PGRt20gWtGqFhrkd0.jpg
2025-07-31 21:46:55.4|Info|DiskScanService|Completed scanning disk for Kung Fu Panda 2
2025-07-31 21:46:55.4|Info|MediaCoverService|Downloading Fanart for [Kung Fu Panda 2 (2011)][tt1302011, 49444] https://image.tmdb.org/t/p/original/7BdxZXbSkUiVeCRXKD3hi9KYeWm.jpg
2025-07-31 21:46:55.4|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-31 21:46:55.5|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-31 21:46:55.5|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-31 21:46:55.5|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-31 21:46:55.6|Info|RefreshMovieService|Updating info for Kung Fu Panda 3
2025-07-31 21:46:55.9|Info|DiskScanService|Scanning disk for Kung Fu Panda 3
2025-07-31 21:46:56.0|Info|DiskScanService|Completed scanning disk for Kung Fu Panda 3
2025-07-31 21:46:56.1|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-31 21:46:56.1|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-31 21:46:56.1|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-31 21:46:56.1|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-31 21:46:56.2|Info|RefreshMovieService|Updating info for Kung Fu Panda 4
2025-07-31 21:46:56.5|Info|DiskScanService|Scanning disk for Kung Fu Panda 4
2025-07-31 21:46:56.5|Info|MediaCoverService|Downloading Poster for [Kung Fu Panda 4 (2024)][tt21692408, 1011985] https://image.tmdb.org/t/p/original/nqXsAaQsKw2gKpkfhIgjXNDRqg7.jpg
2025-07-31 21:46:56.6|Info|DiskScanService|Completed scanning disk for Kung Fu Panda 4
2025-07-31 21:46:56.7|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-31 21:46:56.7|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-31 21:46:56.7|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-31 21:46:56.7|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-31 21:46:56.8|Info|RefreshMovieService|Updating info for A Minecraft Movie
2025-07-31 21:46:57.0|Info|DiskScanService|Scanning disk for A Minecraft Movie
2025-07-31 21:46:57.1|Info|MediaCoverService|Downloading Fanart for [A Minecraft Movie (2025)][tt3566834, 950387] https://image.tmdb.org/t/p/original/2Nti3gYAX513wvhp8IiLL6ZDyOm.jpg
2025-07-31 21:46:57.1|Info|DiskScanService|Completed scanning disk for A Minecraft Movie
2025-07-31 21:46:57.2|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-31 21:46:57.2|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-31 21:46:57.3|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-31 21:46:57.3|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-31 21:46:57.4|Info|DiskScanService|Scanning disk for Cast Away
2025-07-31 21:46:57.5|Info|DiskScanService|Completed scanning disk for Cast Away
2025-07-31 21:46:57.5|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-31 21:46:57.5|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-31 21:46:57.6|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-31 21:46:57.6|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-31 21:46:57.6|Info|RefreshMovieService|Updating info for How to Train Your Dragon
2025-07-31 21:46:57.9|Info|DiskScanService|Scanning disk for How to Train Your Dragon
2025-07-31 21:46:58.0|Info|DiskScanService|Completed scanning disk for How to Train Your Dragon
2025-07-31 21:46:58.0|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-31 21:46:58.0|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-31 21:46:58.1|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-31 21:46:58.1|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-31 21:46:58.1|Info|RefreshMovieService|Updating info for John Wick: Chapter 4
2025-07-31 21:46:58.3|Info|DiskScanService|Scanning disk for John Wick: Chapter 4
2025-07-31 21:46:58.3|Info|MediaCoverService|Downloading Poster for [John Wick: Chapter 4 (2023)][tt10366206, 603692] https://image.tmdb.org/t/p/original/vZloFAK7NmvMGKE7VkF5UHaz0I.jpg
2025-07-31 21:46:58.4|Info|DiskScanService|Completed scanning disk for John Wick: Chapter 4
2025-07-31 21:46:58.5|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-31 21:46:58.5|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-31 21:46:58.6|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-31 21:46:58.6|Info|MediaCoverService|Downloading Fanart for [John Wick: Chapter 4 (2023)][tt10366206, 603692] https://image.tmdb.org/t/p/original/7I6VUdPj6tQECNHdviJkUHD2u89.jpg
2025-07-31 21:46:58.6|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-31 21:46:58.7|Info|RefreshMovieService|Updating info for Despicable Me 4
2025-07-31 21:46:58.9|Info|DiskScanService|Scanning disk for Despicable Me 4
2025-07-31 21:46:58.9|Info|MediaCoverService|Downloading Poster for [Despicable Me 4 (2024)][tt7510222, 519182] https://image.tmdb.org/t/p/original/wWba3TaojhK7NdycRhoQpsG0FaH.jpg
2025-07-31 21:46:59.1|Info|DiskScanService|Completed scanning disk for Despicable Me 4
2025-07-31 21:46:59.1|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-31 21:46:59.2|Info|MediaCoverService|Downloading Fanart for [Despicable Me 4 (2024)][tt7510222, 519182] https://image.tmdb.org/t/p/original/lgkPzcOSnTvjeMnuFzozRO5HHw1.jpg
2025-07-31 21:46:59.2|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-31 21:46:59.3|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-31 21:46:59.3|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-31 21:46:59.4|Info|DiskScanService|Scanning disk for Die Hard
2025-07-31 21:46:59.6|Info|DiskScanService|Completed scanning disk for Die Hard
2025-07-31 21:46:59.6|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-31 21:46:59.6|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-31 21:46:59.7|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-31 21:46:59.7|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-31 21:46:59.7|Info|DiskScanService|Scanning disk for American History X
2025-07-31 21:46:59.9|Info|DiskScanService|Completed scanning disk for American History X
2025-07-31 21:46:59.9|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-31 21:46:59.9|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-31 21:47:00.0|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-31 21:47:00.0|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-31 21:47:00.3|Info|RefreshMovieService|Updating info for Interstellar
2025-07-31 21:47:00.4|Info|DiskScanService|Scanning disk for Interstellar
2025-07-31 21:47:00.5|Info|MediaCoverService|Downloading Fanart for [Interstellar (2014)][tt0816692, 157336] https://image.tmdb.org/t/p/original/vgnoBSVzWAV9sNQUORaDGvDp7wx.jpg
2025-07-31 21:47:00.6|Info|DiskScanService|Completed scanning disk for Interstellar
2025-07-31 21:47:00.7|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-31 21:47:00.7|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-31 21:47:00.7|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-31 21:47:00.8|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-31 21:47:00.9|Info|DiskScanService|Scanning disk for God's Crooked Lines
2025-07-31 21:47:01.0|Info|DiskScanService|Completed scanning disk for God's Crooked Lines
2025-07-31 21:47:01.0|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-31 21:47:01.1|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-31 21:47:01.1|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-31 21:47:01.1|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-31 21:50:09.9|Info|RssSyncService|Starting RSS Sync
2025-07-31 21:50:09.9|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/28/2025 19:19:10 and 07/28/2025 19:19:10 UTC. Search may be required.
2025-07-31 21:50:10.0|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/31/2025 19:19:10 and 07/31/2025 19:19:10 UTC. Search may be required.
2025-07-31 21:50:10.2|Warn|Torznab|Indexer MoviesDVDR rss sync didn't cover the period between 07/31/2025 19:19:10 and 07/31/2025 19:19:10 UTC. Search may be required.
2025-07-31 21:50:12.1|Warn|Torznab|Indexer LimeTorrents rss sync didn't cover the period between 07/31/2025 18:15:10 and 07/31/2025 19:28:10 UTC. Search may be required.
2025-07-31 21:50:12.2|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/31/2025 19:19:14 and 07/31/2025 19:19:14 UTC. Search may be required.
2025-07-31 21:50:12.2|Info|DownloadDecisionMaker|Processing 715 releases
2025-07-31 21:50:48.1|Info|RssSyncService|RSS Sync Completed. Reports found: 715, Reports grabbed: 0
2025-07-31 22:21:10.0|Info|RssSyncService|Starting RSS Sync
2025-07-31 22:21:10.4|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/28/2025 19:19:10 and 07/31/2025 20:11:10 UTC. Search may be required.
2025-07-31 22:21:10.7|Warn|Torznab|Indexer MoviesDVDR rss sync didn't cover the period between 07/31/2025 19:19:10 and 07/31/2025 20:21:10 UTC. Search may be required.
2025-07-31 22:21:11.0|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/31/2025 19:19:10 and 07/31/2025 20:21:10 UTC. Search may be required.
2025-07-31 22:21:14.8|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/31/2025 19:19:14 and 07/31/2025 20:21:14 UTC. Search may be required.
2025-07-31 22:21:16.7|Info|DownloadDecisionMaker|Processing 715 releases
2025-07-31 22:21:53.8|Info|RssSyncService|RSS Sync Completed. Reports found: 715, Reports grabbed: 0
2025-07-31 22:52:10.0|Info|RssSyncService|Starting RSS Sync
2025-07-31 22:52:10.1|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/31/2025 20:11:10 and 07/31/2025 20:11:10 UTC. Search may be required.
2025-07-31 22:52:10.2|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/31/2025 20:21:10 and 07/31/2025 20:21:10 UTC. Search may be required.
2025-07-31 22:52:10.3|Warn|Torznab|Indexer MoviesDVDR rss sync didn't cover the period between 07/31/2025 20:21:10 and 07/31/2025 20:21:10 UTC. Search may be required.
2025-07-31 22:52:12.3|Warn|Torznab|Indexer LimeTorrents rss sync didn't cover the period between 07/31/2025 19:43:10 and 07/31/2025 20:17:10 UTC. Search may be required.
2025-07-31 22:52:12.3|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/31/2025 20:21:14 and 07/31/2025 20:21:14 UTC. Search may be required.
2025-07-31 22:52:12.4|Info|DownloadDecisionMaker|Processing 715 releases
2025-07-31 22:52:50.6|Info|RssSyncService|RSS Sync Completed. Reports found: 715, Reports grabbed: 0
2025-07-31 23:23:10.1|Info|RssSyncService|Starting RSS Sync
2025-07-31 23:23:10.6|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/31/2025 20:11:10 and 07/31/2025 20:58:10 UTC. Search may be required.
2025-07-31 23:23:10.8|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/31/2025 20:21:10 and 07/31/2025 21:23:10 UTC. Search may be required.
2025-07-31 23:23:10.9|Warn|Torznab|Indexer MoviesDVDR rss sync didn't cover the period between 07/31/2025 20:21:10 and 07/31/2025 21:23:10 UTC. Search may be required.
2025-07-31 23:23:14.9|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/31/2025 20:21:14 and 07/31/2025 21:23:14 UTC. Search may be required.
2025-07-31 23:23:16.9|Info|DownloadDecisionMaker|Processing 715 releases
2025-07-31 23:23:53.7|Info|RssSyncService|RSS Sync Completed. Reports found: 715, Reports grabbed: 0
2025-07-31 23:54:10.3|Info|RssSyncService|Starting RSS Sync
2025-07-31 23:54:10.4|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/31/2025 20:58:10 and 07/31/2025 20:58:10 UTC. Search may be required.
2025-07-31 23:54:10.5|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/31/2025 21:23:10 and 07/31/2025 21:23:10 UTC. Search may be required.
2025-07-31 23:54:10.6|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/api/v2.0/indexers/limetorrents/results/torznab/api?t=movie&cat=2000,8000,100467,104627,112972,121527,127246,136409,146065,151062,5000,5070&extended=1&apikey=(removed)&offset=0&limit=100: 400.BadRequest (3990 bytes)
<?xml version="1.0" encoding="UTF-8"?>
<error code="900" description="Jackett.Common.IndexerException: Exception (limetorrents): Name does not resolve&#xA; ---&gt; System.Net.Http.HttpRequestException: Name does not resolve (www.limetorrents.lol:443)&#xA; ---&gt; System.Net.Sockets.SocketException (0xFFFDFFFF): Name does not resolve&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)&#xA;   at System.Net.Sockets.Socket.&lt;ConnectAsync&gt;g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   --- End of inner exception stack trace ---&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)&#xA;   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DecompressionHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at FlareSolverrSharp.ClearanceHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpClient.&lt;SendAsync&gt;g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)&#xA;   at Jackett.Common.Utils.Clients.HttpWebClient2.Run(WebRequest webRequest) in ./Jackett.Common/Utils/Clients/HttpWebClient2.cs:line 180&#xA;   at Jackett.Common.Utils.Clients.WebClient.GetResultAsync(WebRequest request) in ./Jackett.Common/Utils/Clients/WebClient.cs:line 188&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.RequestWithCookiesAsync(String url, String cookieOverride, RequestType method, String referer, IEnumerable`1 data, Dictionary`2 headers, String rawbody, Nullable`1 emulateBrowser) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 617&#xA;   at Jackett.Common.Indexers.Definitions.CardigannIndexer.PerformQuery(TorznabQuery query) in ./Jackett.Common/Indexers/Definitions/CardigannIndexer.cs:line 1563&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 378&#xA;   --- End of inner exception stack trace ---&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 403&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 816&#xA;   at Jackett.Server.Controllers.ResultsController.Torznab(TorznabRequest request) in ./Jackett.Server/Controllers/ResultsController.cs:line 437" />
2025-07-31 23:54:10.7|Warn|Torznab|Indexer MoviesDVDR rss sync didn't cover the period between 07/31/2025 21:23:10 and 07/31/2025 21:23:10 UTC. Search may be required.
2025-07-31 23:54:10.7|Warn|Torznab|LimeTorrents HTTP request failed: [400:BadRequest] [GET] at [http://jackett:9117/api/v2.0/indexers/limetorrents/results/torznab/api?t=movie&cat=2000,8000,100467,104627,112972,121527,127246,136409,146065,151062,5000,5070&extended=1&apikey=(removed)&offset=0&limit=100]
2025-07-31 23:54:12.5|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/31/2025 21:23:14 and 07/31/2025 21:23:14 UTC. Search may be required.
2025-07-31 23:54:12.5|Info|DownloadDecisionMaker|Processing 615 releases
2025-07-31 23:54:45.0|Info|RssSyncService|RSS Sync Completed. Reports found: 615, Reports grabbed: 0
2025-08-01 00:25:10.4|Info|RssSyncService|Starting RSS Sync
2025-08-01 00:25:11.2|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/31/2025 20:58:10 and 07/31/2025 22:04:11 UTC. Search may be required.
2025-08-01 00:25:11.3|Warn|Torznab|Indexer MoviesDVDR rss sync didn't cover the period between 07/31/2025 21:23:10 and 07/31/2025 22:25:11 UTC. Search may be required.
2025-08-01 00:25:11.6|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/31/2025 21:23:10 and 07/31/2025 22:25:11 UTC. Search may be required.
2025-08-01 00:25:12.6|Warn|Torznab|Indexer LimeTorrents rss sync didn't cover the period between 07/31/2025 20:51:10 and 07/31/2025 21:53:11 UTC. Search may be required.
2025-08-01 00:25:15.7|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/31/2025 21:23:14 and 07/31/2025 22:25:15 UTC. Search may be required.
2025-08-01 00:25:17.5|Info|DownloadDecisionMaker|Processing 714 releases
2025-08-01 00:25:56.1|Info|RssSyncService|RSS Sync Completed. Reports found: 714, Reports grabbed: 0
2025-08-01 00:56:10.5|Info|RssSyncService|Starting RSS Sync
2025-08-01 00:56:10.8|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/31/2025 22:25:11 and 07/31/2025 22:25:11 UTC. Search may be required.
2025-08-01 00:56:10.8|Warn|Torznab|Indexer Jackett 1337x  rss sync didn't cover the period between 07/30/2025 22:00:00 and 07/30/2025 22:00:00 UTC. Search may be required.
2025-08-01 00:56:10.9|Warn|Torznab|Indexer MoviesDVDR rss sync didn't cover the period between 07/31/2025 22:25:11 and 07/31/2025 22:25:11 UTC. Search may be required.
2025-08-01 00:56:12.9|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/31/2025 22:25:15 and 07/31/2025 22:25:15 UTC. Search may be required.
2025-08-01 00:56:12.9|Info|DownloadDecisionMaker|Processing 714 releases
2025-08-01 00:56:56.3|Info|RssSyncService|RSS Sync Completed. Reports found: 714, Reports grabbed: 0
2025-08-01 00:58:57.9|Info|Bootstrap|Starting Radarr - /app/radarr/bin/Radarr - Version 5.26.2.10099
2025-08-01 00:58:58.4|Info|AppFolderInfo|Data directory is being overridden to [/config]
2025-08-01 00:58:58.9|Info|AppFolderInfo|Data directory is being overridden to [/config]
2025-08-01 00:59:01.4|Info|AppFolderInfo|Data directory is being overridden to [/config]
2025-08-01 00:59:02.5|Info|MigrationController|*** Migrating data source=/config/radarr.db;cache size=-20000;datetimekind=Utc;journal mode=Wal;pooling=True;version=3;busytimeout=100 ***
2025-08-01 00:59:02.9|Info|FluentMigrator.Runner.MigrationRunner|DatabaseEngineVersionCheck migrating
2025-08-01 00:59:03.1|Info|FluentMigrator.Runner.MigrationRunner|PerformDBOperation 
2025-08-01 00:59:03.1|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Performing DB Operation
2025-08-01 00:59:03.6|Info|DatabaseEngineVersionCheck|SQLite 3.49.2
2025-08-01 00:59:03.6|Info|FluentMigrator.Runner.MigrationRunner|=> 0.5211097s
2025-08-01 00:59:03.6|Info|FluentMigrator.Runner.MigrationRunner|DatabaseEngineVersionCheck migrated
2025-08-01 00:59:03.6|Info|FluentMigrator.Runner.MigrationRunner|=> 0.5362441s
2025-08-01 00:59:03.8|Info|MigrationController|*** Migrating data source=/config/logs.db;cache size=-20000;datetimekind=Utc;journal mode=Wal;pooling=True;version=3;busytimeout=100 ***
2025-08-01 00:59:03.9|Info|FluentMigrator.Runner.MigrationRunner|DatabaseEngineVersionCheck migrating
2025-08-01 00:59:04.0|Info|FluentMigrator.Runner.MigrationRunner|PerformDBOperation 
2025-08-01 00:59:04.0|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Performing DB Operation
2025-08-01 00:59:04.0|Info|DatabaseEngineVersionCheck|SQLite 3.49.2
2025-08-01 00:59:04.1|Info|FluentMigrator.Runner.MigrationRunner|=> 0.1003094s
2025-08-01 00:59:04.1|Info|FluentMigrator.Runner.MigrationRunner|DatabaseEngineVersionCheck migrated
2025-08-01 00:59:04.1|Info|FluentMigrator.Runner.MigrationRunner|=> 0.1158116s
2025-08-01 00:59:06.0|Info|Microsoft.Hosting.Lifetime|Now listening on: http://[::]:7878
2025-08-01 00:59:07.8|Info|CommandExecutor|Starting 2 threads for tasks.
2025-08-01 00:59:08.3|Info|Microsoft.Hosting.Lifetime|Application started. Press Ctrl+C to shut down.
2025-08-01 00:59:08.3|Info|Microsoft.Hosting.Lifetime|Hosting environment: Production
2025-08-01 00:59:08.3|Info|Microsoft.Hosting.Lifetime|Content root path: /app/radarr/bin
2025-08-01 00:59:09.6|Info|ManagedHttpDispatcher|IPv4 is available: True, IPv6 will be disabled
2025-08-01 01:27:08.2|Info|RssSyncService|Starting RSS Sync
2025-08-01 01:27:08.5|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/api/v2.0/indexers/limetorrents/results/torznab/api?t=movie&cat=2000,8000,100467,104627,112972,121527,127246,136409,146065,151062,5000,5070&extended=1&apikey=(removed)&offset=0&limit=100: 400.BadRequest (3990 bytes)
<?xml version="1.0" encoding="UTF-8"?>
<error code="900" description="Jackett.Common.IndexerException: Exception (limetorrents): Name does not resolve&#xA; ---&gt; System.Net.Http.HttpRequestException: Name does not resolve (www.limetorrents.lol:443)&#xA; ---&gt; System.Net.Sockets.SocketException (0xFFFDFFFF): Name does not resolve&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)&#xA;   at System.Net.Sockets.Socket.&lt;ConnectAsync&gt;g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   --- End of inner exception stack trace ---&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)&#xA;   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DecompressionHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at FlareSolverrSharp.ClearanceHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpClient.&lt;SendAsync&gt;g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)&#xA;   at Jackett.Common.Utils.Clients.HttpWebClient2.Run(WebRequest webRequest) in ./Jackett.Common/Utils/Clients/HttpWebClient2.cs:line 180&#xA;   at Jackett.Common.Utils.Clients.WebClient.GetResultAsync(WebRequest request) in ./Jackett.Common/Utils/Clients/WebClient.cs:line 188&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.RequestWithCookiesAsync(String url, String cookieOverride, RequestType method, String referer, IEnumerable`1 data, Dictionary`2 headers, String rawbody, Nullable`1 emulateBrowser) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 617&#xA;   at Jackett.Common.Indexers.Definitions.CardigannIndexer.PerformQuery(TorznabQuery query) in ./Jackett.Common/Indexers/Definitions/CardigannIndexer.cs:line 1563&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 378&#xA;   --- End of inner exception stack trace ---&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 403&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 816&#xA;   at Jackett.Server.Controllers.ResultsController.Torznab(TorznabRequest request) in ./Jackett.Server/Controllers/ResultsController.cs:line 437" />
2025-08-01 01:27:08.6|Warn|Torznab|LimeTorrents HTTP request failed: [400:BadRequest] [GET] at [http://jackett:9117/api/v2.0/indexers/limetorrents/results/torznab/api?t=movie&cat=2000,8000,100467,104627,112972,121527,127246,136409,146065,151062,5000,5070&extended=1&apikey=(removed)&offset=0&limit=100]
2025-08-01 01:27:08.6|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/31/2025 22:05:11 and 07/31/2025 23:03:08 UTC. Search may be required.
2025-08-01 01:27:08.7|Warn|Torznab|Indexer MoviesDVDR rss sync didn't cover the period between 07/31/2025 22:25:11 and 07/31/2025 23:27:08 UTC. Search may be required.
2025-08-01 01:27:08.9|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/31/2025 22:25:11 and 07/31/2025 23:27:08 UTC. Search may be required.
2025-08-01 01:27:12.7|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/31/2025 22:25:15 and 07/31/2025 23:27:12 UTC. Search may be required.
2025-08-01 01:27:14.9|Warn|Torznab|Indexer Jackett 1337x  rss sync didn't cover the period between 07/30/2025 22:00:00 and 07/30/2025 22:00:00 UTC. Search may be required.
2025-08-01 01:27:14.9|Info|DownloadDecisionMaker|Processing 614 releases
2025-08-01 01:27:37.2|Info|RssSyncService|RSS Sync Completed. Reports found: 614, Reports grabbed: 0
2025-08-01 01:57:38.2|Info|RssSyncService|Starting RSS Sync
2025-08-01 01:57:38.3|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/31/2025 23:27:08 and 07/31/2025 23:27:08 UTC. Search may be required.
2025-08-01 01:57:38.4|Warn|Torznab|Indexer MoviesDVDR rss sync didn't cover the period between 07/31/2025 23:27:08 and 07/31/2025 23:27:08 UTC. Search may be required.
2025-08-01 01:57:40.4|Warn|Torznab|Indexer LimeTorrents rss sync didn't cover the period between 07/31/2025 22:24:11 and 07/31/2025 23:13:38 UTC. Search may be required.
2025-08-01 01:57:40.4|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/31/2025 23:27:12 and 07/31/2025 23:27:12 UTC. Search may be required.
2025-08-01 01:57:40.5|Info|DownloadDecisionMaker|Processing 714 releases
2025-08-01 01:58:06.9|Info|RssSyncService|RSS Sync Completed. Reports found: 714, Reports grabbed: 0
2025-08-01 02:28:08.4|Info|RssSyncService|Starting RSS Sync
2025-08-01 02:28:09.1|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/31/2025 23:04:08 and 08/01/2025 00:07:09 UTC. Search may be required.
2025-08-01 02:28:09.2|Warn|Torznab|Indexer MoviesDVDR rss sync didn't cover the period between 07/31/2025 23:27:08 and 08/01/2025 00:28:09 UTC. Search may be required.
2025-08-01 02:28:09.3|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/31/2025 23:27:08 and 08/01/2025 00:28:09 UTC. Search may be required.
2025-08-01 02:28:13.4|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/31/2025 23:27:12 and 08/01/2025 00:28:13 UTC. Search may be required.
2025-08-01 02:28:15.2|Info|DownloadDecisionMaker|Processing 711 releases
2025-08-01 02:28:40.5|Info|RssSyncService|RSS Sync Completed. Reports found: 711, Reports grabbed: 0
2025-08-01 02:59:08.4|Info|RssSyncService|Starting RSS Sync
2025-08-01 02:59:08.5|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 08/01/2025 00:28:09 and 08/01/2025 00:28:09 UTC. Search may be required.
2025-08-01 02:59:08.6|Warn|Torznab|Indexer MoviesDVDR rss sync didn't cover the period between 08/01/2025 00:28:09 and 08/01/2025 00:28:09 UTC. Search may be required.
2025-08-01 02:59:10.5|Warn|Torznab|Indexer LimeTorrents rss sync didn't cover the period between 07/31/2025 23:43:38 and 07/31/2025 23:59:08 UTC. Search may be required.
2025-08-01 02:59:10.6|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 08/01/2025 00:28:13 and 08/01/2025 00:28:13 UTC. Search may be required.
2025-08-01 02:59:10.6|Info|DownloadDecisionMaker|Processing 711 releases
2025-08-01 02:59:36.9|Info|RssSyncService|RSS Sync Completed. Reports found: 711, Reports grabbed: 0
2025-08-01 03:29:38.5|Info|RssSyncService|Starting RSS Sync
2025-08-01 03:29:38.8|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 08/01/2025 00:08:09 and 08/01/2025 01:06:38 UTC. Search may be required.
2025-08-01 03:29:38.9|Warn|Torznab|Indexer MoviesDVDR rss sync didn't cover the period between 08/01/2025 00:28:09 and 08/01/2025 01:29:38 UTC. Search may be required.
2025-08-01 03:29:39.1|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 08/01/2025 00:28:09 and 08/01/2025 01:29:39 UTC. Search may be required.
2025-08-01 03:29:43.0|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 08/01/2025 00:28:13 and 08/01/2025 01:29:43 UTC. Search may be required.
2025-08-01 03:29:45.1|Info|DownloadDecisionMaker|Processing 711 releases
2025-08-01 03:30:11.2|Info|RssSyncService|RSS Sync Completed. Reports found: 711, Reports grabbed: 0
2025-08-01 04:00:38.6|Info|RssSyncService|Starting RSS Sync
2025-08-01 04:00:38.7|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 08/01/2025 01:29:39 and 08/01/2025 01:29:39 UTC. Search may be required.
2025-08-01 04:00:38.8|Warn|Torznab|Indexer MoviesDVDR rss sync didn't cover the period between 08/01/2025 01:29:38 and 08/01/2025 01:29:38 UTC. Search may be required.
2025-08-01 04:00:40.7|Warn|Torznab|Indexer LimeTorrents rss sync didn't cover the period between 08/01/2025 00:59:08 and 08/01/2025 01:00:38 UTC. Search may be required.
2025-08-01 04:00:40.8|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 08/01/2025 01:29:43 and 08/01/2025 01:29:43 UTC. Search may be required.
2025-08-01 04:00:40.8|Info|DownloadDecisionMaker|Processing 711 releases
2025-08-01 04:01:06.8|Info|RssSyncService|RSS Sync Completed. Reports found: 711, Reports grabbed: 0
2025-08-01 04:31:08.7|Info|RssSyncService|Starting RSS Sync
2025-08-01 04:31:09.0|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 08/01/2025 01:07:38 and 08/01/2025 02:10:09 UTC. Search may be required.
2025-08-01 04:31:09.3|Warn|Torznab|Indexer MoviesDVDR rss sync didn't cover the period between 08/01/2025 01:29:38 and 08/01/2025 02:31:09 UTC. Search may be required.
2025-08-01 04:31:09.6|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 08/01/2025 01:29:39 and 08/01/2025 02:31:09 UTC. Search may be required.
2025-08-01 04:31:13.4|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 08/01/2025 01:29:43 and 08/01/2025 02:31:13 UTC. Search may be required.
2025-08-01 04:31:15.2|Info|DownloadDecisionMaker|Processing 710 releases
2025-08-01 04:31:42.8|Info|RssSyncService|RSS Sync Completed. Reports found: 710, Reports grabbed: 0
2025-08-01 05:02:08.7|Info|RssSyncService|Starting RSS Sync
2025-08-01 05:02:08.8|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 08/01/2025 02:31:09 and 08/01/2025 02:31:09 UTC. Search may be required.
2025-08-01 05:02:08.9|Warn|Torznab|Indexer MoviesDVDR rss sync didn't cover the period between 08/01/2025 02:31:09 and 08/01/2025 02:31:09 UTC. Search may be required.
2025-08-01 05:02:08.9|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/api/v2.0/indexers/limetorrents/results/torznab/api?t=movie&cat=2000,8000,100467,104627,112972,121527,127246,136409,146065,151062,5000,5070&extended=1&apikey=(removed)&offset=0&limit=100: 400.BadRequest (3990 bytes)
<?xml version="1.0" encoding="UTF-8"?>
<error code="900" description="Jackett.Common.IndexerException: Exception (limetorrents): Name does not resolve&#xA; ---&gt; System.Net.Http.HttpRequestException: Name does not resolve (www.limetorrents.lol:443)&#xA; ---&gt; System.Net.Sockets.SocketException (0xFFFDFFFF): Name does not resolve&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)&#xA;   at System.Net.Sockets.Socket.&lt;ConnectAsync&gt;g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   --- End of inner exception stack trace ---&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)&#xA;   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DecompressionHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at FlareSolverrSharp.ClearanceHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpClient.&lt;SendAsync&gt;g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)&#xA;   at Jackett.Common.Utils.Clients.HttpWebClient2.Run(WebRequest webRequest) in ./Jackett.Common/Utils/Clients/HttpWebClient2.cs:line 180&#xA;   at Jackett.Common.Utils.Clients.WebClient.GetResultAsync(WebRequest request) in ./Jackett.Common/Utils/Clients/WebClient.cs:line 188&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.RequestWithCookiesAsync(String url, String cookieOverride, RequestType method, String referer, IEnumerable`1 data, Dictionary`2 headers, String rawbody, Nullable`1 emulateBrowser) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 617&#xA;   at Jackett.Common.Indexers.Definitions.CardigannIndexer.PerformQuery(TorznabQuery query) in ./Jackett.Common/Indexers/Definitions/CardigannIndexer.cs:line 1563&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 378&#xA;   --- End of inner exception stack trace ---&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 403&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 816&#xA;   at Jackett.Server.Controllers.ResultsController.Torznab(TorznabRequest request) in ./Jackett.Server/Controllers/ResultsController.cs:line 437" />
2025-08-01 05:02:09.0|Warn|Torznab|LimeTorrents HTTP request failed: [400:BadRequest] [GET] at [http://jackett:9117/api/v2.0/indexers/limetorrents/results/torznab/api?t=movie&cat=2000,8000,100467,104627,112972,121527,127246,136409,146065,151062,5000,5070&extended=1&apikey=(removed)&offset=0&limit=100]
2025-08-01 05:02:10.9|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 08/01/2025 02:31:13 and 08/01/2025 02:31:13 UTC. Search may be required.
2025-08-01 05:02:11.0|Info|DownloadDecisionMaker|Processing 610 releases
2025-08-01 05:02:33.7|Info|RssSyncService|RSS Sync Completed. Reports found: 610, Reports grabbed: 0
2025-08-01 05:32:38.8|Info|RssSyncService|Starting RSS Sync
2025-08-01 05:32:39.3|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 08/01/2025 02:11:09 and 08/01/2025 03:31:39 UTC. Search may be required.
2025-08-01 05:32:39.4|Warn|Torznab|Indexer MoviesDVDR rss sync didn't cover the period between 08/01/2025 02:31:09 and 08/01/2025 03:32:39 UTC. Search may be required.
2025-08-01 05:32:39.6|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 08/01/2025 02:31:09 and 08/01/2025 03:32:39 UTC. Search may be required.
2025-08-01 05:32:43.4|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 08/01/2025 02:31:13 and 08/01/2025 03:32:43 UTC. Search may be required.
2025-08-01 05:32:45.3|Info|DownloadDecisionMaker|Processing 711 releases
2025-08-01 05:33:12.3|Info|RssSyncService|RSS Sync Completed. Reports found: 711, Reports grabbed: 0
2025-08-01 06:03:39.1|Info|RssSyncService|Starting RSS Sync
2025-08-01 06:03:39.2|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 08/01/2025 03:32:39 and 08/01/2025 03:32:39 UTC. Search may be required.
2025-08-01 06:03:39.3|Warn|Torznab|Indexer MoviesDVDR rss sync didn't cover the period between 08/01/2025 03:32:39 and 08/01/2025 03:32:39 UTC. Search may be required.
2025-08-01 06:03:39.4|Warn|Torznab|Indexer Ilcorsaronero rss sync didn't cover the period between 07/30/2025 00:23:50 and 07/31/2025 19:14:39 UTC. Search may be required.
2025-08-01 06:03:41.3|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 08/01/2025 03:32:43 and 08/01/2025 03:32:43 UTC. Search may be required.
2025-08-01 06:03:41.3|Info|DownloadDecisionMaker|Processing 726 releases
2025-08-01 06:04:09.8|Info|RssSyncService|RSS Sync Completed. Reports found: 726, Reports grabbed: 0
2025-08-01 06:34:39.2|Info|RssSyncService|Starting RSS Sync
2025-08-01 06:34:39.8|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 08/01/2025 03:32:39 and 08/01/2025 04:32:39 UTC. Search may be required.
2025-08-01 06:34:39.8|Warn|Torznab|Indexer MoviesDVDR rss sync didn't cover the period between 08/01/2025 03:32:39 and 08/01/2025 04:34:39 UTC. Search may be required.
2025-08-01 06:34:39.8|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 08/01/2025 03:32:39 and 08/01/2025 04:34:39 UTC. Search may be required.
2025-08-01 06:34:41.3|Warn|Torznab|Indexer LimeTorrents rss sync didn't cover the period between 08/01/2025 03:30:39 and 08/01/2025 03:34:39 UTC. Search may be required.
2025-08-01 06:34:43.5|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 08/01/2025 03:32:43 and 08/01/2025 04:34:43 UTC. Search may be required.
2025-08-01 06:34:45.7|Info|DownloadDecisionMaker|Processing 726 releases
2025-08-01 06:35:14.6|Info|RssSyncService|RSS Sync Completed. Reports found: 726, Reports grabbed: 0
2025-08-01 07:05:39.3|Info|RssSyncService|Starting RSS Sync
2025-08-01 07:05:39.4|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 08/01/2025 04:34:39 and 08/01/2025 04:34:39 UTC. Search may be required.
2025-08-01 07:05:39.5|Warn|Torznab|Indexer MoviesDVDR rss sync didn't cover the period between 08/01/2025 04:34:39 and 08/01/2025 04:34:39 UTC. Search may be required.
2025-08-01 07:05:41.5|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 08/01/2025 04:34:43 and 08/01/2025 04:34:43 UTC. Search may be required.
2025-08-01 07:05:41.5|Info|DownloadDecisionMaker|Processing 726 releases
2025-08-01 07:06:10.4|Info|RssSyncService|RSS Sync Completed. Reports found: 726, Reports grabbed: 0
2025-08-01 07:36:39.4|Info|RssSyncService|Starting RSS Sync
2025-08-01 07:36:39.5|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/api/v2.0/indexers/limetorrents/results/torznab/api?t=movie&cat=2000,8000,100467,104627,112972,121527,127246,136409,146065,151062,5000,5070&extended=1&apikey=(removed)&offset=0&limit=100: 400.BadRequest (3990 bytes)
<?xml version="1.0" encoding="UTF-8"?>
<error code="900" description="Jackett.Common.IndexerException: Exception (limetorrents): Name does not resolve&#xA; ---&gt; System.Net.Http.HttpRequestException: Name does not resolve (www.limetorrents.lol:443)&#xA; ---&gt; System.Net.Sockets.SocketException (0xFFFDFFFF): Name does not resolve&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)&#xA;   at System.Net.Sockets.Socket.&lt;ConnectAsync&gt;g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   --- End of inner exception stack trace ---&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)&#xA;   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DecompressionHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at FlareSolverrSharp.ClearanceHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpClient.&lt;SendAsync&gt;g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)&#xA;   at Jackett.Common.Utils.Clients.HttpWebClient2.Run(WebRequest webRequest) in ./Jackett.Common/Utils/Clients/HttpWebClient2.cs:line 180&#xA;   at Jackett.Common.Utils.Clients.WebClient.GetResultAsync(WebRequest request) in ./Jackett.Common/Utils/Clients/WebClient.cs:line 188&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.RequestWithCookiesAsync(String url, String cookieOverride, RequestType method, String referer, IEnumerable`1 data, Dictionary`2 headers, String rawbody, Nullable`1 emulateBrowser) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 617&#xA;   at Jackett.Common.Indexers.Definitions.CardigannIndexer.PerformQuery(TorznabQuery query) in ./Jackett.Common/Indexers/Definitions/CardigannIndexer.cs:line 1563&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 378&#xA;   --- End of inner exception stack trace ---&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 403&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 816&#xA;   at Jackett.Server.Controllers.ResultsController.Torznab(TorznabRequest request) in ./Jackett.Server/Controllers/ResultsController.cs:line 437" />
2025-08-01 07:36:39.6|Warn|Torznab|LimeTorrents HTTP request failed: [400:BadRequest] [GET] at [http://jackett:9117/api/v2.0/indexers/limetorrents/results/torznab/api?t=movie&cat=2000,8000,100467,104627,112972,121527,127246,136409,146065,151062,5000,5070&extended=1&apikey=(removed)&offset=0&limit=100]
2025-08-01 07:36:39.7|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 08/01/2025 04:34:39 and 08/01/2025 05:35:39 UTC. Search may be required.
2025-08-01 07:36:39.9|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 08/01/2025 04:34:39 and 08/01/2025 05:36:39 UTC. Search may be required.
2025-08-01 07:36:40.0|Warn|Torznab|Indexer MoviesDVDR rss sync didn't cover the period between 08/01/2025 04:34:39 and 08/01/2025 05:36:39 UTC. Search may be required.
2025-08-01 07:36:43.9|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 08/01/2025 04:34:43 and 08/01/2025 05:36:43 UTC. Search may be required.
2025-08-01 07:36:46.1|Info|DownloadDecisionMaker|Processing 628 releases
2025-08-01 07:37:09.5|Info|RssSyncService|RSS Sync Completed. Reports found: 628, Reports grabbed: 0
2025-08-01 08:07:39.5|Info|RssSyncService|Starting RSS Sync
2025-08-01 08:07:39.6|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 08/01/2025 05:36:39 and 08/01/2025 05:36:39 UTC. Search may be required.
2025-08-01 08:07:39.7|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/api/v2.0/indexers/limetorrents/results/torznab/api?t=movie&cat=2000,8000,100467,104627,112972,121527,127246,136409,146065,151062,5000,5070&extended=1&apikey=(removed)&offset=0&limit=100: 400.BadRequest (3990 bytes)
<?xml version="1.0" encoding="UTF-8"?>
<error code="900" description="Jackett.Common.IndexerException: Exception (limetorrents): Name does not resolve&#xA; ---&gt; System.Net.Http.HttpRequestException: Name does not resolve (www.limetorrents.lol:443)&#xA; ---&gt; System.Net.Sockets.SocketException (0xFFFDFFFF): Name does not resolve&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)&#xA;   at System.Net.Sockets.Socket.&lt;ConnectAsync&gt;g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   --- End of inner exception stack trace ---&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)&#xA;   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DecompressionHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at FlareSolverrSharp.ClearanceHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpClient.&lt;SendAsync&gt;g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)&#xA;   at Jackett.Common.Utils.Clients.HttpWebClient2.Run(WebRequest webRequest) in ./Jackett.Common/Utils/Clients/HttpWebClient2.cs:line 180&#xA;   at Jackett.Common.Utils.Clients.WebClient.GetResultAsync(WebRequest request) in ./Jackett.Common/Utils/Clients/WebClient.cs:line 188&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.RequestWithCookiesAsync(String url, String cookieOverride, RequestType method, String referer, IEnumerable`1 data, Dictionary`2 headers, String rawbody, Nullable`1 emulateBrowser) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 617&#xA;   at Jackett.Common.Indexers.Definitions.CardigannIndexer.PerformQuery(TorznabQuery query) in ./Jackett.Common/Indexers/Definitions/CardigannIndexer.cs:line 1563&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 378&#xA;   --- End of inner exception stack trace ---&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 403&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 816&#xA;   at Jackett.Server.Controllers.ResultsController.Torznab(TorznabRequest request) in ./Jackett.Server/Controllers/ResultsController.cs:line 437" />
2025-08-01 08:07:39.7|Warn|Torznab|Indexer MoviesDVDR rss sync didn't cover the period between 08/01/2025 05:36:39 and 08/01/2025 05:36:39 UTC. Search may be required.
2025-08-01 08:07:39.8|Warn|Torznab|LimeTorrents HTTP request failed: [400:BadRequest] [GET] at [http://jackett:9117/api/v2.0/indexers/limetorrents/results/torznab/api?t=movie&cat=2000,8000,100467,104627,112972,121527,127246,136409,146065,151062,5000,5070&extended=1&apikey=(removed)&offset=0&limit=100]
2025-08-01 08:07:41.7|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 08/01/2025 05:36:43 and 08/01/2025 05:36:43 UTC. Search may be required.
2025-08-01 08:07:41.7|Info|DownloadDecisionMaker|Processing 628 releases
2025-08-01 08:08:06.1|Info|RssSyncService|RSS Sync Completed. Reports found: 628, Reports grabbed: 0
2025-08-01 08:38:09.6|Info|RssSyncService|Starting RSS Sync
2025-08-01 08:38:09.9|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 08/01/2025 05:36:39 and 08/01/2025 06:36:09 UTC. Search may be required.
2025-08-01 08:38:09.9|Warn|Torznab|Indexer MoviesDVDR rss sync didn't cover the period between 08/01/2025 05:36:39 and 08/01/2025 06:38:09 UTC. Search may be required.
2025-08-01 08:38:10.1|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 08/01/2025 05:36:39 and 08/01/2025 06:38:10 UTC. Search may be required.
2025-08-01 08:38:11.7|Warn|Torznab|Indexer LimeTorrents rss sync didn't cover the period between 08/01/2025 04:29:39 and 08/01/2025 05:38:09 UTC. Search may be required.
2025-08-01 08:38:14.2|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 08/01/2025 05:36:43 and 08/01/2025 06:38:14 UTC. Search may be required.
2025-08-01 08:38:16.1|Info|DownloadDecisionMaker|Processing 725 releases
2025-08-01 08:38:44.1|Info|RssSyncService|RSS Sync Completed. Reports found: 725, Reports grabbed: 0
2025-08-01 09:09:09.9|Info|RssSyncService|Starting RSS Sync
2025-08-01 09:09:10.0|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 08/01/2025 06:38:10 and 08/01/2025 06:38:10 UTC. Search may be required.
2025-08-01 09:09:10.1|Warn|Torznab|Indexer MoviesDVDR rss sync didn't cover the period between 08/01/2025 06:38:09 and 08/01/2025 06:38:09 UTC. Search may be required.
2025-08-01 09:09:10.4|Warn|Torznab|Indexer Ilcorsaronero rss sync didn't cover the period between 08/01/2025 05:58:17 and 08/01/2025 06:48:10 UTC. Search may be required.
2025-08-01 09:09:12.1|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 08/01/2025 06:38:14 and 08/01/2025 06:38:14 UTC. Search may be required.
2025-08-01 09:09:12.1|Info|DownloadDecisionMaker|Processing 725 releases
2025-08-01 09:09:41.7|Info|RssSyncService|RSS Sync Completed. Reports found: 725, Reports grabbed: 0
2025-08-01 09:40:10.0|Info|RssSyncService|Starting RSS Sync
2025-08-01 09:40:10.1|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/api/v2.0/indexers/limetorrents/results/torznab/api?t=movie&cat=2000,8000,100467,104627,112972,121527,127246,136409,146065,151062,5000,5070&extended=1&apikey=(removed)&offset=0&limit=100: 400.BadRequest (3990 bytes)
<?xml version="1.0" encoding="UTF-8"?>
<error code="900" description="Jackett.Common.IndexerException: Exception (limetorrents): Name does not resolve&#xA; ---&gt; System.Net.Http.HttpRequestException: Name does not resolve (www.limetorrents.lol:443)&#xA; ---&gt; System.Net.Sockets.SocketException (0xFFFDFFFF): Name does not resolve&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)&#xA;   at System.Net.Sockets.Socket.&lt;ConnectAsync&gt;g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   --- End of inner exception stack trace ---&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)&#xA;   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DecompressionHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at FlareSolverrSharp.ClearanceHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpClient.&lt;SendAsync&gt;g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)&#xA;   at Jackett.Common.Utils.Clients.HttpWebClient2.Run(WebRequest webRequest) in ./Jackett.Common/Utils/Clients/HttpWebClient2.cs:line 180&#xA;   at Jackett.Common.Utils.Clients.WebClient.GetResultAsync(WebRequest request) in ./Jackett.Common/Utils/Clients/WebClient.cs:line 188&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.RequestWithCookiesAsync(String url, String cookieOverride, RequestType method, String referer, IEnumerable`1 data, Dictionary`2 headers, String rawbody, Nullable`1 emulateBrowser) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 617&#xA;   at Jackett.Common.Indexers.Definitions.CardigannIndexer.PerformQuery(TorznabQuery query) in ./Jackett.Common/Indexers/Definitions/CardigannIndexer.cs:line 1563&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 378&#xA;   --- End of inner exception stack trace ---&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 403&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 816&#xA;   at Jackett.Server.Controllers.ResultsController.Torznab(TorznabRequest request) in ./Jackett.Server/Controllers/ResultsController.cs:line 437" />
2025-08-01 09:40:10.2|Warn|Torznab|LimeTorrents HTTP request failed: [400:BadRequest] [GET] at [http://jackett:9117/api/v2.0/indexers/limetorrents/results/torznab/api?t=movie&cat=2000,8000,100467,104627,112972,121527,127246,136409,146065,151062,5000,5070&extended=1&apikey=(removed)&offset=0&limit=100]
2025-08-01 09:40:10.3|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 08/01/2025 06:38:09 and 08/01/2025 07:39:10 UTC. Search may be required.
2025-08-01 09:40:10.3|Warn|Torznab|Indexer MoviesDVDR rss sync didn't cover the period between 08/01/2025 06:38:09 and 08/01/2025 07:40:10 UTC. Search may be required.
2025-08-01 09:40:10.6|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 08/01/2025 06:38:10 and 08/01/2025 07:40:10 UTC. Search may be required.
2025-08-01 09:40:12.1|Warn|Torznab|Indexer EZTV rss sync didn't cover the period between 08/01/2025 06:37:10 and 08/01/2025 06:39:10 UTC. Search may be required.
2025-08-01 09:40:14.3|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 08/01/2025 06:38:14 and 08/01/2025 07:40:14 UTC. Search may be required.
2025-08-01 09:40:16.5|Info|DownloadDecisionMaker|Processing 627 releases
2025-08-01 09:40:41.0|Info|RssSyncService|RSS Sync Completed. Reports found: 627, Reports grabbed: 0
2025-08-01 10:11:10.5|Info|RssSyncService|Starting RSS Sync
2025-08-01 10:11:10.6|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 08/01/2025 07:40:10 and 08/01/2025 07:40:10 UTC. Search may be required.
2025-08-01 10:11:10.7|Warn|Torznab|Indexer MoviesDVDR rss sync didn't cover the period between 08/01/2025 07:40:10 and 08/01/2025 07:40:10 UTC. Search may be required.
2025-08-01 10:11:10.7|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/api/v2.0/indexers/ilcorsaronero/results/torznab/api?t=movie&cat=2000,8000,101640,103227,105369,111386,113997,114721,117130,118333,118875,125683,125856,125992,126360,133225,137068,137795,140001,141522,145471,148013,149682,150195,151874,151991,152890,154513,5000,5070&extended=1&apikey=(removed)&offset=0&limit=100: 400.BadRequest (3989 bytes)
<?xml version="1.0" encoding="UTF-8"?>
<error code="900" description="Jackett.Common.IndexerException: Exception (ilcorsaronero): Name does not resolve&#xA; ---&gt; System.Net.Http.HttpRequestException: Name does not resolve (ilcorsaronero.link:443)&#xA; ---&gt; System.Net.Sockets.SocketException (0xFFFDFFFF): Name does not resolve&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)&#xA;   at System.Net.Sockets.Socket.&lt;ConnectAsync&gt;g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   --- End of inner exception stack trace ---&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)&#xA;   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DecompressionHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at FlareSolverrSharp.ClearanceHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpClient.&lt;SendAsync&gt;g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)&#xA;   at Jackett.Common.Utils.Clients.HttpWebClient2.Run(WebRequest webRequest) in ./Jackett.Common/Utils/Clients/HttpWebClient2.cs:line 180&#xA;   at Jackett.Common.Utils.Clients.WebClient.GetResultAsync(WebRequest request) in ./Jackett.Common/Utils/Clients/WebClient.cs:line 188&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.RequestWithCookiesAsync(String url, String cookieOverride, RequestType method, String referer, IEnumerable`1 data, Dictionary`2 headers, String rawbody, Nullable`1 emulateBrowser) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 617&#xA;   at Jackett.Common.Indexers.Definitions.CardigannIndexer.PerformQuery(TorznabQuery query) in ./Jackett.Common/Indexers/Definitions/CardigannIndexer.cs:line 1563&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 378&#xA;   --- End of inner exception stack trace ---&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 403&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 816&#xA;   at Jackett.Server.Controllers.ResultsController.Torznab(TorznabRequest request) in ./Jackett.Server/Controllers/ResultsController.cs:line 437" />
2025-08-01 10:11:10.8|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/api/v2.0/indexers/limetorrents/results/torznab/api?t=movie&cat=2000,8000,100467,104627,112972,121527,127246,136409,146065,151062,5000,5070&extended=1&apikey=(removed)&offset=0&limit=100: 400.BadRequest (3990 bytes)
<?xml version="1.0" encoding="UTF-8"?>
<error code="900" description="Jackett.Common.IndexerException: Exception (limetorrents): Name does not resolve&#xA; ---&gt; System.Net.Http.HttpRequestException: Name does not resolve (www.limetorrents.lol:443)&#xA; ---&gt; System.Net.Sockets.SocketException (0xFFFDFFFF): Name does not resolve&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)&#xA;   at System.Net.Sockets.Socket.&lt;ConnectAsync&gt;g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   --- End of inner exception stack trace ---&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)&#xA;   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DecompressionHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at FlareSolverrSharp.ClearanceHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpClient.&lt;SendAsync&gt;g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)&#xA;   at Jackett.Common.Utils.Clients.HttpWebClient2.Run(WebRequest webRequest) in ./Jackett.Common/Utils/Clients/HttpWebClient2.cs:line 180&#xA;   at Jackett.Common.Utils.Clients.WebClient.GetResultAsync(WebRequest request) in ./Jackett.Common/Utils/Clients/WebClient.cs:line 188&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.RequestWithCookiesAsync(String url, String cookieOverride, RequestType method, String referer, IEnumerable`1 data, Dictionary`2 headers, String rawbody, Nullable`1 emulateBrowser) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 617&#xA;   at Jackett.Common.Indexers.Definitions.CardigannIndexer.PerformQuery(TorznabQuery query) in ./Jackett.Common/Indexers/Definitions/CardigannIndexer.cs:line 1563&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 378&#xA;   --- End of inner exception stack trace ---&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 403&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 816&#xA;   at Jackett.Server.Controllers.ResultsController.Torznab(TorznabRequest request) in ./Jackett.Server/Controllers/ResultsController.cs:line 437" />
2025-08-01 10:11:10.9|Warn|Torznab|Ilcorsaronero HTTP request failed: [400:BadRequest] [GET] at [http://jackett:9117/api/v2.0/indexers/ilcorsaronero/results/torznab/api?t=movie&cat=2000,8000,101640,103227,105369,111386,113997,114721,117130,118333,118875,125683,125856,125992,126360,133225,137068,137795,140001,141522,145471,148013,149682,150195,151874,151991,152890,154513,5000,5070&extended=1&apikey=(removed)&offset=0&limit=100]
2025-08-01 10:11:10.9|Warn|Torznab|LimeTorrents HTTP request failed: [400:BadRequest] [GET] at [http://jackett:9117/api/v2.0/indexers/limetorrents/results/torznab/api?t=movie&cat=2000,8000,100467,104627,112972,121527,127246,136409,146065,151062,5000,5070&extended=1&apikey=(removed)&offset=0&limit=100]
2025-08-01 10:11:12.7|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 08/01/2025 07:40:14 and 08/01/2025 07:40:14 UTC. Search may be required.
2025-08-01 10:11:12.7|Info|DownloadDecisionMaker|Processing 612 releases
2025-08-01 10:11:39.6|Info|RssSyncService|RSS Sync Completed. Reports found: 612, Reports grabbed: 0
2025-08-01 10:24:23.3|Info|Bootstrap|Starting Radarr - /app/radarr/bin/Radarr - Version 5.26.2.10099
2025-08-01 10:24:23.9|Info|AppFolderInfo|Data directory is being overridden to [/config]
2025-08-01 10:24:24.0|Info|AppFolderInfo|Data directory is being overridden to [/config]
2025-08-01 10:24:26.3|Info|AppFolderInfo|Data directory is being overridden to [/config]
2025-08-01 10:24:27.5|Info|MigrationController|*** Migrating data source=/config/radarr.db;cache size=-20000;datetimekind=Utc;journal mode=Wal;pooling=True;version=3;busytimeout=100 ***
2025-08-01 10:24:28.0|Info|FluentMigrator.Runner.MigrationRunner|DatabaseEngineVersionCheck migrating
2025-08-01 10:24:28.1|Info|FluentMigrator.Runner.MigrationRunner|PerformDBOperation 
2025-08-01 10:24:28.1|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Performing DB Operation
2025-08-01 10:24:28.6|Info|DatabaseEngineVersionCheck|SQLite 3.49.2
2025-08-01 10:24:28.6|Info|FluentMigrator.Runner.MigrationRunner|=> 0.4796512s
2025-08-01 10:24:28.6|Info|FluentMigrator.Runner.MigrationRunner|DatabaseEngineVersionCheck migrated
2025-08-01 10:24:28.6|Info|FluentMigrator.Runner.MigrationRunner|=> 0.5069171s
2025-08-01 10:24:28.9|Info|MigrationController|*** Migrating data source=/config/logs.db;cache size=-20000;datetimekind=Utc;journal mode=Wal;pooling=True;version=3;busytimeout=100 ***
2025-08-01 10:24:29.0|Info|FluentMigrator.Runner.MigrationRunner|DatabaseEngineVersionCheck migrating
2025-08-01 10:24:29.0|Info|FluentMigrator.Runner.MigrationRunner|PerformDBOperation 
2025-08-01 10:24:29.0|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Performing DB Operation
2025-08-01 10:24:29.1|Info|DatabaseEngineVersionCheck|SQLite 3.49.2
2025-08-01 10:24:29.1|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0720956s
2025-08-01 10:24:29.1|Info|FluentMigrator.Runner.MigrationRunner|DatabaseEngineVersionCheck migrated
2025-08-01 10:24:29.1|Info|FluentMigrator.Runner.MigrationRunner|=> 0.08981s
2025-08-01 10:24:30.5|Info|Microsoft.Hosting.Lifetime|Now listening on: http://[::]:7878
2025-08-01 10:24:33.1|Info|CommandExecutor|Starting 2 threads for tasks.
2025-08-01 10:24:43.0|Info|Microsoft.Hosting.Lifetime|Application started. Press Ctrl+C to shut down.
2025-08-01 10:24:43.1|Info|Microsoft.Hosting.Lifetime|Hosting environment: Production
2025-08-01 10:24:43.2|Info|Microsoft.Hosting.Lifetime|Content root path: /app/radarr/bin
2025-08-01 10:24:44.4|Info|ManagedHttpDispatcher|IPv4 is available: True, IPv6 will be disabled
2025-08-01 10:37:50.0|Info|Microsoft.Hosting.Lifetime|Application is shutting down...
2025-08-01 10:37:51.2|Info|ConsoleApp|Exiting main.
2025-08-01 10:38:03.1|Info|Bootstrap|Starting Radarr - /app/radarr/bin/Radarr - Version 5.26.2.10099
2025-08-01 10:38:03.6|Info|AppFolderInfo|Data directory is being overridden to [/config]
2025-08-01 10:38:03.7|Info|AppFolderInfo|Data directory is being overridden to [/config]
2025-08-01 10:38:04.9|Info|AppFolderInfo|Data directory is being overridden to [/config]
2025-08-01 10:38:05.6|Info|MigrationController|*** Migrating data source=/config/radarr.db;cache size=-20000;datetimekind=Utc;journal mode=Wal;pooling=True;version=3;busytimeout=100 ***
2025-08-01 10:38:06.0|Info|FluentMigrator.Runner.MigrationRunner|DatabaseEngineVersionCheck migrating
2025-08-01 10:38:06.2|Info|FluentMigrator.Runner.MigrationRunner|PerformDBOperation 
2025-08-01 10:38:06.2|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Performing DB Operation
2025-08-01 10:38:06.4|Info|DatabaseEngineVersionCheck|SQLite 3.49.2
2025-08-01 10:38:06.4|Info|FluentMigrator.Runner.MigrationRunner|=> 0.2466219s
2025-08-01 10:38:06.4|Info|FluentMigrator.Runner.MigrationRunner|DatabaseEngineVersionCheck migrated
2025-08-01 10:38:06.5|Info|FluentMigrator.Runner.MigrationRunner|=> 0.2705224s
2025-08-01 10:38:06.7|Info|MigrationController|*** Migrating data source=/config/logs.db;cache size=-20000;datetimekind=Utc;journal mode=Wal;pooling=True;version=3;busytimeout=100 ***
2025-08-01 10:38:06.8|Info|FluentMigrator.Runner.MigrationRunner|DatabaseEngineVersionCheck migrating
2025-08-01 10:38:06.8|Info|FluentMigrator.Runner.MigrationRunner|PerformDBOperation 
2025-08-01 10:38:06.8|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Performing DB Operation
2025-08-01 10:38:06.9|Info|DatabaseEngineVersionCheck|SQLite 3.49.2
2025-08-01 10:38:06.9|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0687628s
2025-08-01 10:38:06.9|Info|FluentMigrator.Runner.MigrationRunner|DatabaseEngineVersionCheck migrated
2025-08-01 10:38:06.9|Info|FluentMigrator.Runner.MigrationRunner|=> 0.087061s
2025-08-01 10:38:08.8|Info|Microsoft.Hosting.Lifetime|Now listening on: http://[::]:7878
2025-08-01 10:38:10.6|Info|CommandExecutor|Starting 2 threads for tasks.
2025-08-01 10:38:11.5|Info|Microsoft.Hosting.Lifetime|Application started. Press Ctrl+C to shut down.
2025-08-01 10:38:11.5|Info|Microsoft.Hosting.Lifetime|Hosting environment: Production
2025-08-01 10:38:11.5|Info|Microsoft.Hosting.Lifetime|Content root path: /app/radarr/bin
2025-08-01 10:38:11.8|Info|ManagedHttpDispatcher|IPv4 is available: True, IPv6 will be disabled
2025-08-01 10:41:41.3|Info|RssSyncService|Starting RSS Sync
2025-08-01 10:41:42.6|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/api/v2.0/indexers/ilcorsaronero/results/torznab/api?t=movie&cat=2000,8000,101640,103227,105369,111386,113997,114721,117130,118333,118875,125683,125856,125992,126360,133225,137068,137795,140001,141522,145471,148013,149682,150195,151874,151991,152890,154513,5000,5070&extended=1&apikey=(removed)&offset=0&limit=100: 400.BadRequest (3989 bytes)
<?xml version="1.0" encoding="UTF-8"?>
<error code="900" description="Jackett.Common.IndexerException: Exception (ilcorsaronero): Name does not resolve&#xA; ---&gt; System.Net.Http.HttpRequestException: Name does not resolve (ilcorsaronero.link:443)&#xA; ---&gt; System.Net.Sockets.SocketException (0xFFFDFFFF): Name does not resolve&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)&#xA;   at System.Net.Sockets.Socket.&lt;ConnectAsync&gt;g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   --- End of inner exception stack trace ---&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)&#xA;   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DecompressionHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at FlareSolverrSharp.ClearanceHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpClient.&lt;SendAsync&gt;g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)&#xA;   at Jackett.Common.Utils.Clients.HttpWebClient2.Run(WebRequest webRequest) in ./Jackett.Common/Utils/Clients/HttpWebClient2.cs:line 180&#xA;   at Jackett.Common.Utils.Clients.WebClient.GetResultAsync(WebRequest request) in ./Jackett.Common/Utils/Clients/WebClient.cs:line 188&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.RequestWithCookiesAsync(String url, String cookieOverride, RequestType method, String referer, IEnumerable`1 data, Dictionary`2 headers, String rawbody, Nullable`1 emulateBrowser) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 617&#xA;   at Jackett.Common.Indexers.Definitions.CardigannIndexer.PerformQuery(TorznabQuery query) in ./Jackett.Common/Indexers/Definitions/CardigannIndexer.cs:line 1563&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 378&#xA;   --- End of inner exception stack trace ---&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 403&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 816&#xA;   at Jackett.Server.Controllers.ResultsController.Torznab(TorznabRequest request) in ./Jackett.Server/Controllers/ResultsController.cs:line 437" />
2025-08-01 10:41:42.7|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 08/01/2025 07:40:10 and 08/01/2025 08:39:42 UTC. Search may be required.
2025-08-01 10:41:42.9|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 08/01/2025 07:40:10 and 08/01/2025 08:41:42 UTC. Search may be required.
2025-08-01 10:41:43.0|Warn|Torznab|Ilcorsaronero HTTP request failed: [400:BadRequest] [GET] at [http://jackett:9117/api/v2.0/indexers/ilcorsaronero/results/torznab/api?t=movie&cat=2000,8000,101640,103227,105369,111386,113997,114721,117130,118333,118875,125683,125856,125992,126360,133225,137068,137795,140001,141522,145471,148013,149682,150195,151874,151991,152890,154513,5000,5070&extended=1&apikey=(removed)&offset=0&limit=100]
2025-08-01 10:41:44.8|Warn|Torznab|Indexer MoviesDVDR rss sync didn't cover the period between 08/01/2025 07:40:10 and 08/01/2025 08:41:44 UTC. Search may be required.
2025-08-01 10:41:45.1|Warn|Torznab|Indexer LimeTorrents rss sync didn't cover the period between 08/01/2025 06:33:09 and 08/01/2025 08:15:44 UTC. Search may be required.
2025-08-01 10:41:48.6|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 08/01/2025 07:40:14 and 08/01/2025 08:41:48 UTC. Search may be required.
2025-08-01 10:41:48.8|Info|DownloadDecisionMaker|Processing 712 releases
2025-08-01 10:43:02.3|Info|RssSyncService|RSS Sync Completed. Reports found: 712, Reports grabbed: 0
2025-08-01 11:06:18.8|Info|Bootstrap|Starting Radarr - /app/radarr/bin/Radarr - Version 5.26.2.10099
2025-08-01 11:06:19.2|Info|AppFolderInfo|Data directory is being overridden to [/config]
2025-08-01 11:06:19.4|Info|AppFolderInfo|Data directory is being overridden to [/config]
2025-08-01 11:06:22.1|Info|AppFolderInfo|Data directory is being overridden to [/config]
2025-08-01 11:06:22.9|Info|MigrationController|*** Migrating data source=/config/radarr.db;cache size=-20000;datetimekind=Utc;journal mode=Wal;pooling=True;version=3;busytimeout=100 ***
2025-08-01 11:06:23.3|Info|FluentMigrator.Runner.MigrationRunner|DatabaseEngineVersionCheck migrating
2025-08-01 11:06:23.4|Info|FluentMigrator.Runner.MigrationRunner|PerformDBOperation 
2025-08-01 11:06:23.4|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Performing DB Operation
2025-08-01 11:06:24.5|Info|DatabaseEngineVersionCheck|SQLite 3.49.2
2025-08-01 11:06:24.6|Info|FluentMigrator.Runner.MigrationRunner|=> 1.1147004s
2025-08-01 11:06:24.6|Info|FluentMigrator.Runner.MigrationRunner|DatabaseEngineVersionCheck migrated
2025-08-01 11:06:24.6|Info|FluentMigrator.Runner.MigrationRunner|=> 1.1496671s
2025-08-01 11:06:24.8|Info|MigrationController|*** Migrating data source=/config/logs.db;cache size=-20000;datetimekind=Utc;journal mode=Wal;pooling=True;version=3;busytimeout=100 ***
2025-08-01 11:06:24.9|Info|FluentMigrator.Runner.MigrationRunner|DatabaseEngineVersionCheck migrating
2025-08-01 11:06:24.9|Info|FluentMigrator.Runner.MigrationRunner|PerformDBOperation 
2025-08-01 11:06:25.0|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Performing DB Operation
2025-08-01 11:06:25.0|Info|DatabaseEngineVersionCheck|SQLite 3.49.2
2025-08-01 11:06:25.0|Info|FluentMigrator.Runner.MigrationRunner|=> 0.051145s
2025-08-01 11:06:25.0|Info|FluentMigrator.Runner.MigrationRunner|DatabaseEngineVersionCheck migrated
2025-08-01 11:06:25.0|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0657583s
2025-08-01 11:06:27.2|Info|Microsoft.Hosting.Lifetime|Now listening on: http://[::]:7878
2025-08-01 11:06:28.9|Info|CommandExecutor|Starting 2 threads for tasks.
2025-08-01 11:06:29.3|Info|Microsoft.Hosting.Lifetime|Application started. Press Ctrl+C to shut down.
2025-08-01 11:06:29.4|Info|Microsoft.Hosting.Lifetime|Hosting environment: Production
2025-08-01 11:06:29.4|Info|Microsoft.Hosting.Lifetime|Content root path: /app/radarr/bin
2025-08-01 11:06:30.7|Info|ManagedHttpDispatcher|IPv4 is available: True, IPv6 will be disabled
2025-08-01 11:13:29.2|Info|RssSyncService|Starting RSS Sync
2025-08-01 11:13:29.6|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 08/01/2025 08:41:42 and 08/01/2025 09:02:29 UTC. Search may be required.
2025-08-01 11:13:29.8|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 08/01/2025 08:41:42 and 08/01/2025 09:13:29 UTC. Search may be required.
2025-08-01 11:13:29.9|Warn|Torznab|Indexer MoviesDVDR rss sync didn't cover the period between 08/01/2025 08:41:44 and 08/01/2025 09:13:29 UTC. Search may be required.
2025-08-01 11:13:33.8|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 08/01/2025 08:41:48 and 08/01/2025 09:13:33 UTC. Search may be required.
2025-08-01 11:13:36.1|Info|DownloadDecisionMaker|Processing 723 releases
2025-08-01 11:14:11.5|Info|RssSyncService|RSS Sync Completed. Reports found: 723, Reports grabbed: 0
2025-08-01 11:39:18.7|Warn|Microsoft.AspNetCore.Server.Kestrel|As of "08/01/2025 09:37:59 +00:00", the heartbeat has been running for "00:00:25.9994677" which is longer than "00:00:01". This could be caused by thread pool starvation.
2025-08-01 11:45:21.2|Info|Bootstrap|Starting Radarr - /app/radarr/bin/Radarr - Version 5.26.2.10099
2025-08-01 11:45:21.9|Info|AppFolderInfo|Data directory is being overridden to [/config]
2025-08-01 11:45:22.1|Info|AppFolderInfo|Data directory is being overridden to [/config]
2025-08-01 11:45:24.7|Info|AppFolderInfo|Data directory is being overridden to [/config]
2025-08-01 11:45:26.0|Info|MigrationController|*** Migrating data source=/config/radarr.db;cache size=-20000;datetimekind=Utc;journal mode=Wal;pooling=True;version=3;busytimeout=100 ***
2025-08-01 11:45:26.4|Info|FluentMigrator.Runner.MigrationRunner|DatabaseEngineVersionCheck migrating
2025-08-01 11:45:26.5|Info|FluentMigrator.Runner.MigrationRunner|PerformDBOperation 
2025-08-01 11:45:26.6|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Performing DB Operation
2025-08-01 11:45:27.1|Info|DatabaseEngineVersionCheck|SQLite 3.49.2
2025-08-01 11:45:27.1|Info|FluentMigrator.Runner.MigrationRunner|=> 0.585568s
2025-08-01 11:45:27.2|Info|FluentMigrator.Runner.MigrationRunner|DatabaseEngineVersionCheck migrated
2025-08-01 11:45:27.2|Info|FluentMigrator.Runner.MigrationRunner|=> 0.6074158s
2025-08-01 11:45:27.5|Info|MigrationController|*** Migrating data source=/config/logs.db;cache size=-20000;datetimekind=Utc;journal mode=Wal;pooling=True;version=3;busytimeout=100 ***
2025-08-01 11:45:27.6|Info|FluentMigrator.Runner.MigrationRunner|DatabaseEngineVersionCheck migrating
2025-08-01 11:45:27.6|Info|FluentMigrator.Runner.MigrationRunner|PerformDBOperation 
2025-08-01 11:45:27.7|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Performing DB Operation
2025-08-01 11:45:27.7|Info|DatabaseEngineVersionCheck|SQLite 3.49.2
2025-08-01 11:45:27.7|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0756106s
2025-08-01 11:45:27.7|Info|FluentMigrator.Runner.MigrationRunner|DatabaseEngineVersionCheck migrated
2025-08-01 11:45:27.8|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0976813s
2025-08-01 11:45:30.1|Info|Microsoft.Hosting.Lifetime|Now listening on: http://[::]:7878
2025-08-01 11:45:32.9|Info|CommandExecutor|Starting 2 threads for tasks.
2025-08-01 11:45:34.9|Info|Microsoft.Hosting.Lifetime|Application started. Press Ctrl+C to shut down.
2025-08-01 11:45:35.0|Info|Microsoft.Hosting.Lifetime|Hosting environment: Production
2025-08-01 11:45:35.1|Info|Microsoft.Hosting.Lifetime|Content root path: /app/radarr/bin
2025-08-01 11:45:36.6|Info|ManagedHttpDispatcher|IPv4 is available: True, IPv6 will be disabled
2025-08-01 11:46:04.5|Info|RssSyncService|Starting RSS Sync
2025-08-01 11:46:08.8|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 08/01/2025 09:03:29 and 08/01/2025 09:35:08 UTC. Search may be required.
2025-08-01 11:46:11.4|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 08/01/2025 09:13:29 and 08/01/2025 09:46:11 UTC. Search may be required.
2025-08-01 11:46:13.3|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/api/v2.0/indexers/limetorrents/results/torznab/api?t=movie&cat=2000,8000,100467,104627,112972,121527,127246,136409,146065,151062,5000,5070&extended=1&apikey=(removed)&offset=0&limit=100: 400.BadRequest (3990 bytes)
<?xml version="1.0" encoding="UTF-8"?>
<error code="900" description="Jackett.Common.IndexerException: Exception (limetorrents): Name does not resolve&#xA; ---&gt; System.Net.Http.HttpRequestException: Name does not resolve (www.limetorrents.lol:443)&#xA; ---&gt; System.Net.Sockets.SocketException (0xFFFDFFFF): Name does not resolve&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)&#xA;   at System.Net.Sockets.Socket.&lt;ConnectAsync&gt;g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   --- End of inner exception stack trace ---&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)&#xA;   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DecompressionHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at FlareSolverrSharp.ClearanceHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpClient.&lt;SendAsync&gt;g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)&#xA;   at Jackett.Common.Utils.Clients.HttpWebClient2.Run(WebRequest webRequest) in ./Jackett.Common/Utils/Clients/HttpWebClient2.cs:line 180&#xA;   at Jackett.Common.Utils.Clients.WebClient.GetResultAsync(WebRequest request) in ./Jackett.Common/Utils/Clients/WebClient.cs:line 188&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.RequestWithCookiesAsync(String url, String cookieOverride, RequestType method, String referer, IEnumerable`1 data, Dictionary`2 headers, String rawbody, Nullable`1 emulateBrowser) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 617&#xA;   at Jackett.Common.Indexers.Definitions.CardigannIndexer.PerformQuery(TorznabQuery query) in ./Jackett.Common/Indexers/Definitions/CardigannIndexer.cs:line 1563&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 378&#xA;   --- End of inner exception stack trace ---&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 403&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 816&#xA;   at Jackett.Server.Controllers.ResultsController.Torznab(TorznabRequest request) in ./Jackett.Server/Controllers/ResultsController.cs:line 437" />
2025-08-01 11:46:13.5|Warn|Torznab|LimeTorrents HTTP request failed: [400:BadRequest] [GET] at [http://jackett:9117/api/v2.0/indexers/limetorrents/results/torznab/api?t=movie&cat=2000,8000,100467,104627,112972,121527,127246,136409,146065,151062,5000,5070&extended=1&apikey=(removed)&offset=0&limit=100]
2025-08-01 11:46:14.7|Warn|Torznab|Indexer MoviesDVDR rss sync didn't cover the period between 08/01/2025 09:13:29 and 08/01/2025 09:46:14 UTC. Search may be required.
2025-08-01 11:46:18.7|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 08/01/2025 09:13:33 and 08/01/2025 09:46:18 UTC. Search may be required.
2025-08-01 11:46:18.8|Info|DownloadDecisionMaker|Processing 627 releases
2025-08-01 11:47:10.4|Info|RssSyncService|RSS Sync Completed. Reports found: 627, Reports grabbed: 0
2025-08-01 12:06:51.8|Info|Bootstrap|Starting Radarr - /app/radarr/bin/Radarr - Version 5.26.2.10099
2025-08-01 12:06:52.5|Info|AppFolderInfo|Data directory is being overridden to [/config]
2025-08-01 12:06:52.9|Info|AppFolderInfo|Data directory is being overridden to [/config]
2025-08-01 12:06:56.1|Info|AppFolderInfo|Data directory is being overridden to [/config]
2025-08-01 12:06:57.5|Info|MigrationController|*** Migrating data source=/config/radarr.db;cache size=-20000;datetimekind=Utc;journal mode=Wal;pooling=True;version=3;busytimeout=100 ***
2025-08-01 12:06:58.2|Info|FluentMigrator.Runner.MigrationRunner|DatabaseEngineVersionCheck migrating
2025-08-01 12:06:58.5|Info|FluentMigrator.Runner.MigrationRunner|PerformDBOperation 
2025-08-01 12:06:58.5|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Performing DB Operation
2025-08-01 12:06:59.0|Info|DatabaseEngineVersionCheck|SQLite 3.49.2
2025-08-01 12:06:59.1|Info|FluentMigrator.Runner.MigrationRunner|=> 0.5540672s
2025-08-01 12:06:59.1|Info|FluentMigrator.Runner.MigrationRunner|DatabaseEngineVersionCheck migrated
2025-08-01 12:06:59.1|Info|FluentMigrator.Runner.MigrationRunner|=> 0.603068s
2025-08-01 12:06:59.4|Info|MigrationController|*** Migrating data source=/config/logs.db;cache size=-20000;datetimekind=Utc;journal mode=Wal;pooling=True;version=3;busytimeout=100 ***
2025-08-01 12:06:59.6|Info|FluentMigrator.Runner.MigrationRunner|DatabaseEngineVersionCheck migrating
2025-08-01 12:06:59.7|Info|FluentMigrator.Runner.MigrationRunner|PerformDBOperation 
2025-08-01 12:06:59.7|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Performing DB Operation
2025-08-01 12:06:59.7|Info|DatabaseEngineVersionCheck|SQLite 3.49.2
2025-08-01 12:06:59.7|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0449793s
2025-08-01 12:06:59.7|Info|FluentMigrator.Runner.MigrationRunner|DatabaseEngineVersionCheck migrated
2025-08-01 12:06:59.8|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0634291s
2025-08-01 12:07:02.8|Info|Microsoft.Hosting.Lifetime|Now listening on: http://[::]:7878
2025-08-01 12:07:05.5|Info|CommandExecutor|Starting 2 threads for tasks.
2025-08-01 12:07:06.9|Info|Microsoft.Hosting.Lifetime|Application started. Press Ctrl+C to shut down.
2025-08-01 12:07:06.9|Info|Microsoft.Hosting.Lifetime|Hosting environment: Production
2025-08-01 12:07:07.0|Info|Microsoft.Hosting.Lifetime|Content root path: /app/radarr/bin
2025-08-01 12:07:07.2|Info|ManagedHttpDispatcher|IPv4 is available: True, IPv6 will be disabled
2025-08-01 12:07:07.2|Info|ManagedHttpDispatcher|IPv4 is available: True, IPv6 will be disabled
2025-08-01 12:07:22.3|Info|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was challenged.
2025-08-01 12:07:22.3|Info|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was challenged.
2025-08-01 12:07:22.3|Info|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was challenged.
2025-08-01 12:17:36.1|Info|RssSyncService|Starting RSS Sync
2025-08-01 12:17:36.5|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/api/v2.0/indexers/limetorrents/results/torznab/api?t=movie&cat=2000,8000,100467,104627,112972,121527,127246,136409,146065,151062,5000,5070&extended=1&apikey=(removed)&offset=0&limit=100: 400.BadRequest (3990 bytes)
<?xml version="1.0" encoding="UTF-8"?>
<error code="900" description="Jackett.Common.IndexerException: Exception (limetorrents): Name does not resolve&#xA; ---&gt; System.Net.Http.HttpRequestException: Name does not resolve (www.limetorrents.lol:443)&#xA; ---&gt; System.Net.Sockets.SocketException (0xFFFDFFFF): Name does not resolve&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)&#xA;   at System.Net.Sockets.Socket.&lt;ConnectAsync&gt;g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   --- End of inner exception stack trace ---&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)&#xA;   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DecompressionHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at FlareSolverrSharp.ClearanceHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpClient.&lt;SendAsync&gt;g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)&#xA;   at Jackett.Common.Utils.Clients.HttpWebClient2.Run(WebRequest webRequest) in ./Jackett.Common/Utils/Clients/HttpWebClient2.cs:line 180&#xA;   at Jackett.Common.Utils.Clients.WebClient.GetResultAsync(WebRequest request) in ./Jackett.Common/Utils/Clients/WebClient.cs:line 188&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.RequestWithCookiesAsync(String url, String cookieOverride, RequestType method, String referer, IEnumerable`1 data, Dictionary`2 headers, String rawbody, Nullable`1 emulateBrowser) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 617&#xA;   at Jackett.Common.Indexers.Definitions.CardigannIndexer.PerformQuery(TorznabQuery query) in ./Jackett.Common/Indexers/Definitions/CardigannIndexer.cs:line 1563&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 378&#xA;   --- End of inner exception stack trace ---&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 403&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 816&#xA;   at Jackett.Server.Controllers.ResultsController.Torznab(TorznabRequest request) in ./Jackett.Server/Controllers/ResultsController.cs:line 437" />
2025-08-01 12:17:36.5|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/api/v2.0/indexers/ilcorsaronero/results/torznab/api?t=movie&cat=2000,8000,101640,103227,105369,111386,113997,114721,117130,118333,118875,125683,125856,125992,126360,133225,137068,137795,140001,141522,145471,148013,149682,150195,151874,151991,152890,154513,5000,5070&extended=1&apikey=(removed)&offset=0&limit=100: 400.BadRequest (3976 bytes)
<?xml version="1.0" encoding="UTF-8"?>
<error code="900" description="Jackett.Common.IndexerException: Exception (ilcorsaronero): Network unreachable&#xA; ---&gt; System.Net.Http.HttpRequestException: Network unreachable (ilcorsaronero.link:443)&#xA; ---&gt; System.Net.Sockets.SocketException (101): Network unreachable&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)&#xA;   at System.Net.Sockets.Socket.&lt;ConnectAsync&gt;g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   --- End of inner exception stack trace ---&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)&#xA;   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DecompressionHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at FlareSolverrSharp.ClearanceHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpClient.&lt;SendAsync&gt;g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)&#xA;   at Jackett.Common.Utils.Clients.HttpWebClient2.Run(WebRequest webRequest) in ./Jackett.Common/Utils/Clients/HttpWebClient2.cs:line 180&#xA;   at Jackett.Common.Utils.Clients.WebClient.GetResultAsync(WebRequest request) in ./Jackett.Common/Utils/Clients/WebClient.cs:line 188&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.RequestWithCookiesAsync(String url, String cookieOverride, RequestType method, String referer, IEnumerable`1 data, Dictionary`2 headers, String rawbody, Nullable`1 emulateBrowser) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 617&#xA;   at Jackett.Common.Indexers.Definitions.CardigannIndexer.PerformQuery(TorznabQuery query) in ./Jackett.Common/Indexers/Definitions/CardigannIndexer.cs:line 1563&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 378&#xA;   --- End of inner exception stack trace ---&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 403&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 816&#xA;   at Jackett.Server.Controllers.ResultsController.Torznab(TorznabRequest request) in ./Jackett.Server/Controllers/ResultsController.cs:line 437" />
2025-08-01 12:17:36.5|Warn|Torznab|LimeTorrents HTTP request failed: [400:BadRequest] [GET] at [http://jackett:9117/api/v2.0/indexers/limetorrents/results/torznab/api?t=movie&cat=2000,8000,100467,104627,112972,121527,127246,136409,146065,151062,5000,5070&extended=1&apikey=(removed)&offset=0&limit=100]
2025-08-01 12:17:36.6|Warn|Torznab|Ilcorsaronero HTTP request failed: [400:BadRequest] [GET] at [http://jackett:9117/api/v2.0/indexers/ilcorsaronero/results/torznab/api?t=movie&cat=2000,8000,101640,103227,105369,111386,113997,114721,117130,118333,118875,125683,125856,125992,126360,133225,137068,137795,140001,141522,145471,148013,149682,150195,151874,151991,152890,154513,5000,5070&extended=1&apikey=(removed)&offset=0&limit=100]
2025-08-01 12:17:36.7|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 08/01/2025 09:36:08 and 08/01/2025 10:04:36 UTC. Search may be required.
2025-08-01 12:17:36.7|Warn|Torznab|Indexer MoviesDVDR rss sync didn't cover the period between 08/01/2025 09:46:14 and 08/01/2025 10:17:36 UTC. Search may be required.
2025-08-01 12:17:36.8|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 08/01/2025 09:46:11 and 08/01/2025 10:17:36 UTC. Search may be required.
2025-08-01 12:17:40.8|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 08/01/2025 09:46:18 and 08/01/2025 10:17:40 UTC. Search may be required.
2025-08-01 12:17:42.9|Info|DownloadDecisionMaker|Processing 610 releases
2025-08-01 12:18:12.7|Info|RssSyncService|RSS Sync Completed. Reports found: 610, Reports grabbed: 0
2025-08-01 12:48:59.8|Info|Bootstrap|Starting Radarr - /app/radarr/bin/Radarr - Version 5.26.2.10099
2025-08-01 12:49:00.5|Info|AppFolderInfo|Data directory is being overridden to [/config]
2025-08-01 12:49:00.7|Info|AppFolderInfo|Data directory is being overridden to [/config]
2025-08-01 12:49:03.8|Info|AppFolderInfo|Data directory is being overridden to [/config]
2025-08-01 12:49:04.9|Info|MigrationController|*** Migrating data source=/config/radarr.db;cache size=-20000;datetimekind=Utc;journal mode=Wal;pooling=True;version=3;busytimeout=100 ***
2025-08-01 12:49:05.4|Info|FluentMigrator.Runner.MigrationRunner|DatabaseEngineVersionCheck migrating
2025-08-01 12:49:05.6|Info|FluentMigrator.Runner.MigrationRunner|PerformDBOperation 
2025-08-01 12:49:05.6|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Performing DB Operation
2025-08-01 12:49:06.1|Info|DatabaseEngineVersionCheck|SQLite 3.49.2
2025-08-01 12:49:06.1|Info|FluentMigrator.Runner.MigrationRunner|=> 0.4979875s
2025-08-01 12:49:06.2|Info|FluentMigrator.Runner.MigrationRunner|DatabaseEngineVersionCheck migrated
2025-08-01 12:49:06.2|Info|FluentMigrator.Runner.MigrationRunner|=> 0.5336814s
2025-08-01 12:49:06.4|Info|MigrationController|*** Migrating data source=/config/logs.db;cache size=-20000;datetimekind=Utc;journal mode=Wal;pooling=True;version=3;busytimeout=100 ***
2025-08-01 12:49:06.5|Info|FluentMigrator.Runner.MigrationRunner|DatabaseEngineVersionCheck migrating
2025-08-01 12:49:06.5|Info|FluentMigrator.Runner.MigrationRunner|PerformDBOperation 
2025-08-01 12:49:06.5|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Performing DB Operation
2025-08-01 12:49:06.6|Info|DatabaseEngineVersionCheck|SQLite 3.49.2
2025-08-01 12:49:06.7|Info|FluentMigrator.Runner.MigrationRunner|=> 0.158447s
2025-08-01 12:49:06.7|Info|FluentMigrator.Runner.MigrationRunner|DatabaseEngineVersionCheck migrated
2025-08-01 12:49:06.7|Info|FluentMigrator.Runner.MigrationRunner|=> 0.188105s
2025-08-01 12:49:09.1|Info|Microsoft.Hosting.Lifetime|Now listening on: http://[::]:7878
2025-08-01 12:49:10.4|Info|CommandExecutor|Starting 2 threads for tasks.
2025-08-01 12:49:11.6|Info|Microsoft.Hosting.Lifetime|Application started. Press Ctrl+C to shut down.
2025-08-01 12:49:11.6|Info|Microsoft.Hosting.Lifetime|Hosting environment: Production
2025-08-01 12:49:11.7|Info|Microsoft.Hosting.Lifetime|Content root path: /app/radarr/bin
2025-08-01 12:49:13.2|Info|ManagedHttpDispatcher|IPv4 is available: True, IPv6 will be disabled
2025-08-01 12:49:45.3|Info|RssSyncService|Starting RSS Sync
2025-08-01 12:49:53.7|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/api/v2.0/indexers/ilcorsaronero/results/torznab/api?t=movie&cat=2000,8000,101640,103227,105369,111386,113997,114721,117130,118333,118875,125683,125856,125992,126360,133225,137068,137795,140001,141522,145471,148013,149682,150195,151874,151991,152890,154513,5000,5070&extended=1&apikey=(removed)&offset=0&limit=100: 400.BadRequest (3989 bytes)
<?xml version="1.0" encoding="UTF-8"?>
<error code="900" description="Jackett.Common.IndexerException: Exception (ilcorsaronero): Name does not resolve&#xA; ---&gt; System.Net.Http.HttpRequestException: Name does not resolve (ilcorsaronero.link:443)&#xA; ---&gt; System.Net.Sockets.SocketException (0xFFFDFFFF): Name does not resolve&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)&#xA;   at System.Net.Sockets.Socket.&lt;ConnectAsync&gt;g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   --- End of inner exception stack trace ---&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)&#xA;   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DecompressionHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at FlareSolverrSharp.ClearanceHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpClient.&lt;SendAsync&gt;g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)&#xA;   at Jackett.Common.Utils.Clients.HttpWebClient2.Run(WebRequest webRequest) in ./Jackett.Common/Utils/Clients/HttpWebClient2.cs:line 180&#xA;   at Jackett.Common.Utils.Clients.WebClient.GetResultAsync(WebRequest request) in ./Jackett.Common/Utils/Clients/WebClient.cs:line 188&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.RequestWithCookiesAsync(String url, String cookieOverride, RequestType method, String referer, IEnumerable`1 data, Dictionary`2 headers, String rawbody, Nullable`1 emulateBrowser) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 617&#xA;   at Jackett.Common.Indexers.Definitions.CardigannIndexer.PerformQuery(TorznabQuery query) in ./Jackett.Common/Indexers/Definitions/CardigannIndexer.cs:line 1563&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 378&#xA;   --- End of inner exception stack trace ---&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 403&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 816&#xA;   at Jackett.Server.Controllers.ResultsController.Torznab(TorznabRequest request) in ./Jackett.Server/Controllers/ResultsController.cs:line 437" />
2025-08-01 12:49:53.7|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/api/v2.0/indexers/limetorrents/results/torznab/api?t=movie&cat=2000,8000,100467,104627,112972,121527,127246,136409,146065,151062,5000,5070&extended=1&apikey=(removed)&offset=0&limit=100: 400.BadRequest (3990 bytes)
<?xml version="1.0" encoding="UTF-8"?>
<error code="900" description="Jackett.Common.IndexerException: Exception (limetorrents): Name does not resolve&#xA; ---&gt; System.Net.Http.HttpRequestException: Name does not resolve (www.limetorrents.lol:443)&#xA; ---&gt; System.Net.Sockets.SocketException (0xFFFDFFFF): Name does not resolve&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)&#xA;   at System.Net.Sockets.Socket.&lt;ConnectAsync&gt;g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   --- End of inner exception stack trace ---&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)&#xA;   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DecompressionHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at FlareSolverrSharp.ClearanceHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpClient.&lt;SendAsync&gt;g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)&#xA;   at Jackett.Common.Utils.Clients.HttpWebClient2.Run(WebRequest webRequest) in ./Jackett.Common/Utils/Clients/HttpWebClient2.cs:line 180&#xA;   at Jackett.Common.Utils.Clients.WebClient.GetResultAsync(WebRequest request) in ./Jackett.Common/Utils/Clients/WebClient.cs:line 188&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.RequestWithCookiesAsync(String url, String cookieOverride, RequestType method, String referer, IEnumerable`1 data, Dictionary`2 headers, String rawbody, Nullable`1 emulateBrowser) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 617&#xA;   at Jackett.Common.Indexers.Definitions.CardigannIndexer.PerformQuery(TorznabQuery query) in ./Jackett.Common/Indexers/Definitions/CardigannIndexer.cs:line 1563&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 378&#xA;   --- End of inner exception stack trace ---&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 403&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 816&#xA;   at Jackett.Server.Controllers.ResultsController.Torznab(TorznabRequest request) in ./Jackett.Server/Controllers/ResultsController.cs:line 437" />
2025-08-01 12:49:54.0|Warn|Torznab|Ilcorsaronero HTTP request failed: [400:BadRequest] [GET] at [http://jackett:9117/api/v2.0/indexers/ilcorsaronero/results/torznab/api?t=movie&cat=2000,8000,101640,103227,105369,111386,113997,114721,117130,118333,118875,125683,125856,125992,126360,133225,137068,137795,140001,141522,145471,148013,149682,150195,151874,151991,152890,154513,5000,5070&extended=1&apikey=(removed)&offset=0&limit=100]
2025-08-01 12:49:54.0|Warn|Torznab|LimeTorrents HTTP request failed: [400:BadRequest] [GET] at [http://jackett:9117/api/v2.0/indexers/limetorrents/results/torznab/api?t=movie&cat=2000,8000,100467,104627,112972,121527,127246,136409,146065,151062,5000,5070&extended=1&apikey=(removed)&offset=0&limit=100]
2025-08-01 12:49:55.2|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 08/01/2025 10:06:36 and 08/01/2025 10:38:54 UTC. Search may be required.
2025-08-01 12:49:55.2|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 08/01/2025 10:17:36 and 08/01/2025 10:49:54 UTC. Search may be required.
2025-08-01 12:49:58.5|Warn|Torznab|Indexer MoviesDVDR rss sync didn't cover the period between 08/01/2025 10:17:36 and 08/01/2025 10:49:57 UTC. Search may be required.
2025-08-01 12:50:01.9|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 08/01/2025 10:17:40 and 08/01/2025 10:50:01 UTC. Search may be required.
2025-08-01 12:50:05.5|Info|DownloadDecisionMaker|Processing 612 releases
2025-08-01 12:50:47.5|Info|RssSyncService|RSS Sync Completed. Reports found: 612, Reports grabbed: 0
2025-08-01 12:54:51.7|Info|Bootstrap|Starting Radarr - /app/radarr/bin/Radarr - Version 5.26.2.10099
2025-08-01 12:54:52.0|Info|AppFolderInfo|Data directory is being overridden to [/config]
2025-08-01 12:54:52.5|Info|AppFolderInfo|Data directory is being overridden to [/config]
2025-08-01 12:54:56.1|Info|AppFolderInfo|Data directory is being overridden to [/config]
2025-08-01 12:54:57.2|Info|MigrationController|*** Migrating data source=/config/radarr.db;cache size=-20000;datetimekind=Utc;journal mode=Wal;pooling=True;version=3;busytimeout=100 ***
2025-08-01 12:54:57.7|Info|FluentMigrator.Runner.MigrationRunner|DatabaseEngineVersionCheck migrating
2025-08-01 12:54:57.8|Info|FluentMigrator.Runner.MigrationRunner|PerformDBOperation 
2025-08-01 12:54:57.8|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Performing DB Operation
2025-08-01 12:54:58.3|Info|DatabaseEngineVersionCheck|SQLite 3.49.2
2025-08-01 12:54:58.3|Info|FluentMigrator.Runner.MigrationRunner|=> 0.4701668s
2025-08-01 12:54:58.3|Info|FluentMigrator.Runner.MigrationRunner|DatabaseEngineVersionCheck migrated
2025-08-01 12:54:58.4|Info|FluentMigrator.Runner.MigrationRunner|=> 0.4894658s
2025-08-01 12:54:58.7|Info|MigrationController|*** Migrating data source=/config/logs.db;cache size=-20000;datetimekind=Utc;journal mode=Wal;pooling=True;version=3;busytimeout=100 ***
2025-08-01 12:54:58.8|Info|FluentMigrator.Runner.MigrationRunner|DatabaseEngineVersionCheck migrating
2025-08-01 12:54:58.8|Info|FluentMigrator.Runner.MigrationRunner|PerformDBOperation 
2025-08-01 12:54:58.9|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Performing DB Operation
2025-08-01 12:54:58.9|Info|DatabaseEngineVersionCheck|SQLite 3.49.2
2025-08-01 12:54:58.9|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0744666s
2025-08-01 12:54:59.0|Info|FluentMigrator.Runner.MigrationRunner|DatabaseEngineVersionCheck migrated
2025-08-01 12:54:59.0|Info|FluentMigrator.Runner.MigrationRunner|=> 0.1001723s
2025-08-01 12:55:01.3|Info|Microsoft.Hosting.Lifetime|Now listening on: http://[::]:7878
2025-08-01 12:55:03.3|Info|CommandExecutor|Starting 2 threads for tasks.
2025-08-01 12:55:03.9|Info|Microsoft.Hosting.Lifetime|Application started. Press Ctrl+C to shut down.
2025-08-01 12:55:03.9|Info|Microsoft.Hosting.Lifetime|Hosting environment: Production
2025-08-01 12:55:03.9|Info|Microsoft.Hosting.Lifetime|Content root path: /app/radarr/bin
2025-08-01 12:55:05.3|Info|ManagedHttpDispatcher|IPv4 is available: True, IPv6 will be disabled
2025-08-01 13:21:06.1|Info|RssSyncService|Starting RSS Sync
2025-08-01 13:21:06.6|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/api/v2.0/indexers/ilcorsaronero/results/torznab/api?t=movie&cat=2000,8000,101640,103227,105369,111386,113997,114721,117130,118333,118875,125683,125856,125992,126360,133225,137068,137795,140001,141522,145471,148013,149682,150195,151874,151991,152890,154513,5000,5070&extended=1&apikey=(removed)&offset=0&limit=100: 400.BadRequest (3989 bytes)
<?xml version="1.0" encoding="UTF-8"?>
<error code="900" description="Jackett.Common.IndexerException: Exception (ilcorsaronero): Name does not resolve&#xA; ---&gt; System.Net.Http.HttpRequestException: Name does not resolve (ilcorsaronero.link:443)&#xA; ---&gt; System.Net.Sockets.SocketException (0xFFFDFFFF): Name does not resolve&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)&#xA;   at System.Net.Sockets.Socket.&lt;ConnectAsync&gt;g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   --- End of inner exception stack trace ---&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)&#xA;   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DecompressionHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at FlareSolverrSharp.ClearanceHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpClient.&lt;SendAsync&gt;g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)&#xA;   at Jackett.Common.Utils.Clients.HttpWebClient2.Run(WebRequest webRequest) in ./Jackett.Common/Utils/Clients/HttpWebClient2.cs:line 180&#xA;   at Jackett.Common.Utils.Clients.WebClient.GetResultAsync(WebRequest request) in ./Jackett.Common/Utils/Clients/WebClient.cs:line 188&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.RequestWithCookiesAsync(String url, String cookieOverride, RequestType method, String referer, IEnumerable`1 data, Dictionary`2 headers, String rawbody, Nullable`1 emulateBrowser) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 617&#xA;   at Jackett.Common.Indexers.Definitions.CardigannIndexer.PerformQuery(TorznabQuery query) in ./Jackett.Common/Indexers/Definitions/CardigannIndexer.cs:line 1563&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 378&#xA;   --- End of inner exception stack trace ---&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 403&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 816&#xA;   at Jackett.Server.Controllers.ResultsController.Torznab(TorznabRequest request) in ./Jackett.Server/Controllers/ResultsController.cs:line 437" />
2025-08-01 13:21:06.7|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/api/v2.0/indexers/limetorrents/results/torznab/api?t=movie&cat=2000,8000,100467,104627,112972,121527,127246,136409,146065,151062,5000,5070&extended=1&apikey=(removed)&offset=0&limit=100: 400.BadRequest (3990 bytes)
<?xml version="1.0" encoding="UTF-8"?>
<error code="900" description="Jackett.Common.IndexerException: Exception (limetorrents): Name does not resolve&#xA; ---&gt; System.Net.Http.HttpRequestException: Name does not resolve (www.limetorrents.lol:443)&#xA; ---&gt; System.Net.Sockets.SocketException (0xFFFDFFFF): Name does not resolve&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)&#xA;   at System.Net.Sockets.Socket.&lt;ConnectAsync&gt;g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   --- End of inner exception stack trace ---&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)&#xA;   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DecompressionHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at FlareSolverrSharp.ClearanceHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpClient.&lt;SendAsync&gt;g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)&#xA;   at Jackett.Common.Utils.Clients.HttpWebClient2.Run(WebRequest webRequest) in ./Jackett.Common/Utils/Clients/HttpWebClient2.cs:line 180&#xA;   at Jackett.Common.Utils.Clients.WebClient.GetResultAsync(WebRequest request) in ./Jackett.Common/Utils/Clients/WebClient.cs:line 188&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.RequestWithCookiesAsync(String url, String cookieOverride, RequestType method, String referer, IEnumerable`1 data, Dictionary`2 headers, String rawbody, Nullable`1 emulateBrowser) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 617&#xA;   at Jackett.Common.Indexers.Definitions.CardigannIndexer.PerformQuery(TorznabQuery query) in ./Jackett.Common/Indexers/Definitions/CardigannIndexer.cs:line 1563&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 378&#xA;   --- End of inner exception stack trace ---&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 403&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 816&#xA;   at Jackett.Server.Controllers.ResultsController.Torznab(TorznabRequest request) in ./Jackett.Server/Controllers/ResultsController.cs:line 437" />
2025-08-01 13:21:06.7|Warn|Torznab|Ilcorsaronero HTTP request failed: [400:BadRequest] [GET] at [http://jackett:9117/api/v2.0/indexers/ilcorsaronero/results/torznab/api?t=movie&cat=2000,8000,101640,103227,105369,111386,113997,114721,117130,118333,118875,125683,125856,125992,126360,133225,137068,137795,140001,141522,145471,148013,149682,150195,151874,151991,152890,154513,5000,5070&extended=1&apikey=(removed)&offset=0&limit=100]
2025-08-01 13:21:06.7|Warn|Torznab|LimeTorrents HTTP request failed: [400:BadRequest] [GET] at [http://jackett:9117/api/v2.0/indexers/limetorrents/results/torznab/api?t=movie&cat=2000,8000,100467,104627,112972,121527,127246,136409,146065,151062,5000,5070&extended=1&apikey=(removed)&offset=0&limit=100]
2025-08-01 13:21:06.9|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 08/01/2025 10:39:54 and 08/01/2025 11:09:06 UTC. Search may be required.
2025-08-01 13:21:06.9|Warn|Torznab|Indexer MoviesDVDR rss sync didn't cover the period between 08/01/2025 10:49:57 and 08/01/2025 11:21:06 UTC. Search may be required.
2025-08-01 13:21:07.1|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 08/01/2025 10:49:54 and 08/01/2025 11:21:07 UTC. Search may be required.
2025-08-01 13:21:11.3|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 08/01/2025 10:50:01 and 08/01/2025 11:21:11 UTC. Search may be required.
2025-08-01 13:21:13.1|Info|DownloadDecisionMaker|Processing 613 releases
2025-08-01 13:21:43.1|Info|RssSyncService|RSS Sync Completed. Reports found: 613, Reports grabbed: 0
2025-08-01 13:51:58.8|Info|RssSyncService|Starting RSS Sync
2025-08-01 13:51:59.2|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 08/01/2025 11:21:07 and 08/01/2025 11:21:07 UTC. Search may be required.
2025-08-01 13:52:00.9|Warn|Torznab|Indexer MoviesDVDR rss sync didn't cover the period between 08/01/2025 11:21:06 and 08/01/2025 11:21:06 UTC. Search may be required.
2025-08-01 13:52:02.8|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 08/01/2025 11:21:11 and 08/01/2025 11:21:11 UTC. Search may be required.
2025-08-01 13:52:04.5|Warn|Torznab|Indexer LimeTorrents rss sync didn't cover the period between 08/01/2025 09:12:29 and 08/01/2025 11:20:04 UTC. Search may be required.
2025-08-01 13:52:04.6|Info|DownloadDecisionMaker|Processing 728 releases
2025-08-01 13:52:41.1|Info|RssSyncService|RSS Sync Completed. Reports found: 728, Reports grabbed: 0
2025-08-01 14:23:00.5|Info|RssSyncService|Starting RSS Sync
2025-08-01 14:23:02.1|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 08/01/2025 11:11:06 and 08/01/2025 12:02:01 UTC. Search may be required.
2025-08-01 14:23:05.0|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 08/01/2025 11:21:07 and 08/01/2025 12:23:01 UTC. Search may be required.
2025-08-01 14:23:08.8|Warn|Torznab|Indexer MoviesDVDR rss sync didn't cover the period between 08/01/2025 11:21:06 and 08/01/2025 12:23:02 UTC. Search may be required.
2025-08-01 14:23:22.7|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 08/01/2025 11:21:11 and 08/01/2025 12:23:10 UTC. Search may be required.
2025-08-01 14:23:22.9|Info|DownloadDecisionMaker|Processing 726 releases
2025-08-01 14:23:53.3|Info|RssSyncService|RSS Sync Completed. Reports found: 726, Reports grabbed: 0
2025-08-01 14:54:01.0|Info|RssSyncService|Starting RSS Sync
2025-08-01 14:54:01.6|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 08/01/2025 12:23:01 and 08/01/2025 12:23:01 UTC. Search may be required.
2025-08-01 14:54:02.0|Warn|Torznab|Indexer MoviesDVDR rss sync didn't cover the period between 08/01/2025 12:23:02 and 08/01/2025 12:23:02 UTC. Search may be required.
2025-08-01 14:54:04.0|Warn|Torznab|Indexer LimeTorrents rss sync didn't cover the period between 08/01/2025 11:50:04 and 08/01/2025 11:54:02 UTC. Search may be required.
2025-08-01 14:54:04.1|Info|DownloadDecisionMaker|Processing 726 releases
2025-08-01 14:54:35.5|Info|RssSyncService|RSS Sync Completed. Reports found: 726, Reports grabbed: 0
2025-08-01 15:25:03.2|Info|RssSyncService|Starting RSS Sync
2025-08-01 15:25:03.5|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 08/01/2025 12:03:01 and 08/01/2025 13:03:03 UTC. Search may be required.
2025-08-01 15:25:03.6|Warn|Torznab|Indexer MoviesDVDR rss sync didn't cover the period between 08/01/2025 12:23:02 and 08/01/2025 13:25:03 UTC. Search may be required.
2025-08-01 15:25:03.7|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 08/01/2025 12:23:01 and 08/01/2025 13:25:03 UTC. Search may be required.
2025-08-01 15:25:07.7|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 08/01/2025 12:23:11 and 08/01/2025 13:25:07 UTC. Search may be required.
2025-08-01 15:25:09.9|Info|DownloadDecisionMaker|Processing 726 releases
2025-08-01 15:25:40.6|Info|RssSyncService|RSS Sync Completed. Reports found: 726, Reports grabbed: 0
2025-08-01 15:56:08.1|Info|RssSyncService|Starting RSS Sync
2025-08-01 15:56:09.6|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 08/01/2025 13:25:03 and 08/01/2025 13:25:03 UTC. Search may be required.
2025-08-01 15:56:10.7|Warn|Torznab|Indexer MoviesDVDR rss sync didn't cover the period between 08/01/2025 13:25:03 and 08/01/2025 13:25:03 UTC. Search may be required.
2025-08-01 15:56:10.8|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/api/v2.0/indexers/limetorrents/results/torznab/api?t=movie&cat=2000,8000,100467,104627,112972,121527,127246,136409,146065,151062,5000,5070&extended=1&apikey=(removed)&offset=0&limit=100: 400.BadRequest (3990 bytes)
<?xml version="1.0" encoding="UTF-8"?>
<error code="900" description="Jackett.Common.IndexerException: Exception (limetorrents): Name does not resolve&#xA; ---&gt; System.Net.Http.HttpRequestException: Name does not resolve (www.limetorrents.lol:443)&#xA; ---&gt; System.Net.Sockets.SocketException (0xFFFDFFFF): Name does not resolve&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)&#xA;   at System.Net.Sockets.Socket.&lt;ConnectAsync&gt;g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   --- End of inner exception stack trace ---&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)&#xA;   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DecompressionHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at FlareSolverrSharp.ClearanceHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpClient.&lt;SendAsync&gt;g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)&#xA;   at Jackett.Common.Utils.Clients.HttpWebClient2.Run(WebRequest webRequest) in ./Jackett.Common/Utils/Clients/HttpWebClient2.cs:line 180&#xA;   at Jackett.Common.Utils.Clients.WebClient.GetResultAsync(WebRequest request) in ./Jackett.Common/Utils/Clients/WebClient.cs:line 188&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.RequestWithCookiesAsync(String url, String cookieOverride, RequestType method, String referer, IEnumerable`1 data, Dictionary`2 headers, String rawbody, Nullable`1 emulateBrowser) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 617&#xA;   at Jackett.Common.Indexers.Definitions.CardigannIndexer.PerformQuery(TorznabQuery query) in ./Jackett.Common/Indexers/Definitions/CardigannIndexer.cs:line 1563&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 378&#xA;   --- End of inner exception stack trace ---&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 403&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 816&#xA;   at Jackett.Server.Controllers.ResultsController.Torznab(TorznabRequest request) in ./Jackett.Server/Controllers/ResultsController.cs:line 437" />
2025-08-01 15:56:11.4|Warn|Torznab|LimeTorrents HTTP request failed: [400:BadRequest] [GET] at [http://jackett:9117/api/v2.0/indexers/limetorrents/results/torznab/api?t=movie&cat=2000,8000,100467,104627,112972,121527,127246,136409,146065,151062,5000,5070&extended=1&apikey=(removed)&offset=0&limit=100]
2025-08-01 15:56:11.7|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 08/01/2025 13:25:07 and 08/01/2025 13:25:07 UTC. Search may be required.
2025-08-01 15:56:11.8|Info|DownloadDecisionMaker|Processing 626 releases
2025-08-01 15:56:41.3|Info|RssSyncService|RSS Sync Completed. Reports found: 626, Reports grabbed: 0
2025-08-01 16:27:08.6|Info|RssSyncService|Starting RSS Sync
2025-08-01 16:27:09.8|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 08/01/2025 13:05:03 and 08/01/2025 14:01:09 UTC. Search may be required.
2025-08-01 16:27:09.8|Warn|Torznab|Indexer MoviesDVDR rss sync didn't cover the period between 08/01/2025 13:25:03 and 08/01/2025 14:27:09 UTC. Search may be required.
2025-08-01 16:27:09.9|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 08/01/2025 13:25:03 and 08/01/2025 14:27:09 UTC. Search may be required.
2025-08-01 16:27:10.7|Warn|Torznab|Indexer LimeTorrents rss sync didn't cover the period between 08/01/2025 12:54:02 and 08/01/2025 13:27:09 UTC. Search may be required.
2025-08-01 16:27:13.7|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 08/01/2025 13:25:07 and 08/01/2025 14:27:13 UTC. Search may be required.
2025-08-01 16:27:15.6|Info|DownloadDecisionMaker|Processing 726 releases
2025-08-01 16:27:52.3|Info|RssSyncService|RSS Sync Completed. Reports found: 726, Reports grabbed: 0
2025-08-01 16:58:08.6|Info|RssSyncService|Starting RSS Sync
2025-08-01 16:58:08.7|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 08/01/2025 14:27:09 and 08/01/2025 14:27:09 UTC. Search may be required.
2025-08-01 16:58:09.1|Warn|Torznab|Indexer MoviesDVDR rss sync didn't cover the period between 08/01/2025 14:27:09 and 08/01/2025 14:27:09 UTC. Search may be required.
2025-08-01 16:58:10.9|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 08/01/2025 14:27:13 and 08/01/2025 14:27:13 UTC. Search may be required.
2025-08-01 16:58:10.9|Info|DownloadDecisionMaker|Processing 726 releases
2025-08-01 16:58:39.0|Info|RssSyncService|RSS Sync Completed. Reports found: 726, Reports grabbed: 0
2025-08-01 17:05:52.5|Warn|Microsoft.AspNetCore.Server.Kestrel|As of "08/01/2025 15:02:46 +00:00", the heartbeat has been running for "00:00:01.1877033" which is longer than "00:00:01". This could be caused by thread pool starvation.
2025-08-01 17:06:01.1|Warn|Microsoft.AspNetCore.Server.Kestrel|As of "08/01/2025 15:05:59 +00:00", the heartbeat has been running for "00:00:01.5730449" which is longer than "00:00:01". This could be caused by thread pool starvation.
2025-08-01 17:28:55.1|Info|RssSyncService|Starting RSS Sync
2025-08-01 17:28:59.5|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 08/01/2025 14:04:09 and 08/01/2025 15:03:56 UTC. Search may be required.
2025-08-01 17:29:00.1|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 08/01/2025 14:27:09 and 08/01/2025 15:28:57 UTC. Search may be required.
2025-08-01 17:29:03.4|Warn|Torznab|Indexer MoviesDVDR rss sync didn't cover the period between 08/01/2025 14:27:09 and 08/01/2025 15:28:59 UTC. Search may be required.
2025-08-01 17:29:06.6|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/api/v2.0/indexers/limetorrents/results/torznab/api?t=movie&cat=2000,8000,100467,104627,112972,121527,127246,136409,146065,151062,5000,5070&extended=1&apikey=(removed)&offset=0&limit=100: 400.BadRequest (3990 bytes)
<?xml version="1.0" encoding="UTF-8"?>
<error code="900" description="Jackett.Common.IndexerException: Exception (limetorrents): Name does not resolve&#xA; ---&gt; System.Net.Http.HttpRequestException: Name does not resolve (www.limetorrents.lol:443)&#xA; ---&gt; System.Net.Sockets.SocketException (0xFFFDFFFF): Name does not resolve&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)&#xA;   at System.Net.Sockets.Socket.&lt;ConnectAsync&gt;g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   --- End of inner exception stack trace ---&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)&#xA;   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DecompressionHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at FlareSolverrSharp.ClearanceHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpClient.&lt;SendAsync&gt;g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)&#xA;   at Jackett.Common.Utils.Clients.HttpWebClient2.Run(WebRequest webRequest) in ./Jackett.Common/Utils/Clients/HttpWebClient2.cs:line 180&#xA;   at Jackett.Common.Utils.Clients.WebClient.GetResultAsync(WebRequest request) in ./Jackett.Common/Utils/Clients/WebClient.cs:line 188&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.RequestWithCookiesAsync(String url, String cookieOverride, RequestType method, String referer, IEnumerable`1 data, Dictionary`2 headers, String rawbody, Nullable`1 emulateBrowser) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 617&#xA;   at Jackett.Common.Indexers.Definitions.CardigannIndexer.PerformQuery(TorznabQuery query) in ./Jackett.Common/Indexers/Definitions/CardigannIndexer.cs:line 1563&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 378&#xA;   --- End of inner exception stack trace ---&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 403&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 816&#xA;   at Jackett.Server.Controllers.ResultsController.Torznab(TorznabRequest request) in ./Jackett.Server/Controllers/ResultsController.cs:line 437" />
2025-08-01 17:29:06.9|Warn|Torznab|LimeTorrents HTTP request failed: [400:BadRequest] [GET] at [http://jackett:9117/api/v2.0/indexers/limetorrents/results/torznab/api?t=movie&cat=2000,8000,100467,104627,112972,121527,127246,136409,146065,151062,5000,5070&extended=1&apikey=(removed)&offset=0&limit=100]
2025-08-01 17:29:08.1|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 08/01/2025 14:27:13 and 08/01/2025 15:29:07 UTC. Search may be required.
2025-08-01 17:29:09.2|Info|DownloadDecisionMaker|Processing 633 releases
2025-08-01 17:29:44.4|Info|RssSyncService|RSS Sync Completed. Reports found: 633, Reports grabbed: 0
2025-08-01 17:56:09.0|Warn|Microsoft.AspNetCore.Server.Kestrel|As of "08/01/2025 15:56:06 +00:00", the heartbeat has been running for "00:00:01.9334982" which is longer than "00:00:01". This could be caused by thread pool starvation.
2025-08-01 18:00:06.0|Info|RssSyncService|Starting RSS Sync
2025-08-01 18:00:08.7|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 08/01/2025 15:28:57 and 08/01/2025 15:28:57 UTC. Search may be required.
2025-08-01 18:00:12.1|Warn|Torznab|Indexer LimeTorrents rss sync didn't cover the period between 08/01/2025 14:19:09 and 08/01/2025 15:13:10 UTC. Search may be required.
2025-08-01 18:00:12.1|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 08/01/2025 15:29:07 and 08/01/2025 15:29:07 UTC. Search may be required.
2025-08-01 18:00:12.8|Info|DownloadDecisionMaker|Processing 733 releases
2025-08-01 18:00:53.9|Info|RssSyncService|RSS Sync Completed. Reports found: 733, Reports grabbed: 0
2025-08-01 18:31:06.9|Info|RssSyncService|Starting RSS Sync
2025-08-01 18:31:08.0|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 08/01/2025 15:05:56 and 08/01/2025 16:08:07 UTC. Search may be required.
2025-08-01 18:31:08.2|Warn|Torznab|Indexer MoviesDVDR rss sync didn't cover the period between 08/01/2025 15:29:00 and 08/01/2025 16:31:08 UTC. Search may be required.
2025-08-01 18:31:08.4|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 08/01/2025 15:28:57 and 08/01/2025 16:31:08 UTC. Search may be required.
2025-08-01 18:31:13.3|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 08/01/2025 15:29:07 and 08/01/2025 16:31:12 UTC. Search may be required.
2025-08-01 18:31:15.1|Info|DownloadDecisionMaker|Processing 732 releases
2025-08-01 18:31:48.3|Info|RssSyncService|RSS Sync Completed. Reports found: 732, Reports grabbed: 0
