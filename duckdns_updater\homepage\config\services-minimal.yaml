# Homepage Services Configuration - Configuración Mínima
# Configuración básica sin widgets de Docker para solucionar problemas

# === SERVICIOS DE MEDIA ===
- Media:
    - Jellyfin:
        icon: "jellyfin.png"
        href: "https://tankeflix.duckdns.org"
        description: "Servidor multimedia personal"

    - Jellyseerr:
        icon: "jellyseerr.png"
        href: "https://tankejellyseerr.duckdns.org"
        description: "Gestión de solicitudes de contenido"

    - qBittorrent:
        icon: "qbittorrent.png"
        href: "https://tanketorrent.duckdns.org"
        description: "Cliente torrent"

# === SERVICIOS DE AUTOMATIZACIÓN ===
- Automatización:
    - AdGuard Home:
        icon: "adguard-home.png"
        href: "https://tankeguard.duckdns.org"
        description: "Filtrado DNS y bloqueo de anuncios"

# === SERVICIOS DE DESCARGAS (*ARR STACK) ===
- Descargas:
    - Sonarr:
        icon: "sonarr.png"
        href: "https://tankesonarr.duckdns.org"
        description: "Gestión automática de series"

    - Radarr:
        icon: "radarr.png"
        href: "https://tankeradarr.duckdns.org"
        description: "Gestión automática de películas"

    - Jackett:
        icon: "jackett.png"
        href: "https://tankejackett.duckdns.org"
        description: "Indexador de torrents"

# === SERVICIOS DE SISTEMA ===
- Sistema:
    - Portainer:
        icon: "portainer.png"
        href: "http://host.docker.internal:9000"
        description: "Gestión de contenedores Docker"
