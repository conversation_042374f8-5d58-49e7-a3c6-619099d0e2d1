{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T22:00:00.014Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: <PERSON><PERSON><PERSON> Recently Added Scan","timestamp":"2025-07-31T22:00:00.084Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"1752e5ad-6542-4991-bce9-39e4a30c45a5","timestamp":"2025-07-31T22:00:00.084Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T22:00:00.093Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T22:00:00.104Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T22:00:00.105Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T22:01:00.007Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T22:02:00.012Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T22:03:00.012Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T22:04:00.010Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T22:05:00.014Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T22:05:00.019Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"d6160818-29f9-43a6-a1af-26ddcf2cdcf9","timestamp":"2025-07-31T22:05:00.019Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T22:05:00.028Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T22:05:00.034Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T22:05:00.035Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T22:06:00.016Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T22:07:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T22:08:00.014Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T22:09:00.012Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T22:10:00.013Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T22:10:00.016Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"1f1149ca-ba5b-4f00-a51b-1c2a5e5bf98a","timestamp":"2025-07-31T22:10:00.016Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T22:10:00.024Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T22:10:00.031Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T22:10:00.031Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T22:11:00.020Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T22:12:00.013Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T22:13:00.012Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T22:14:00.015Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T22:15:00.015Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T22:15:00.023Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"627c525b-fe6c-486e-bc4e-ee776c5cd31f","timestamp":"2025-07-31T22:15:00.024Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T22:15:00.038Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T22:15:00.046Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T22:15:00.048Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T22:16:00.015Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T22:17:00.012Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T22:18:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T22:19:00.014Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T22:20:00.012Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T22:20:00.016Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"b7ee051a-2e4b-4f9a-aeb8-6e5006a88f07","timestamp":"2025-07-31T22:20:00.016Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T22:20:00.024Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T22:20:00.031Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T22:20:00.032Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T22:21:00.011Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T22:22:00.012Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T22:23:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T22:24:00.007Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T22:25:00.012Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T22:25:00.018Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"792dd214-20cd-408a-9077-c816df0baf75","timestamp":"2025-07-31T22:25:00.018Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T22:25:00.024Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T22:25:00.028Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T22:25:00.028Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T22:26:00.013Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T22:27:00.012Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T22:28:00.013Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T22:29:00.014Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T22:30:00.013Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T22:30:00.016Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"3b933bc6-8b73-419f-9bd4-3b08565d0b4d","timestamp":"2025-07-31T22:30:00.016Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T22:30:00.025Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T22:30:00.033Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T22:30:00.033Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T22:31:00.014Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T22:32:00.012Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T22:33:00.005Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T22:34:00.016Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T22:35:00.013Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T22:35:00.016Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"11662910-eb8b-4bfc-9106-04926e3c7b16","timestamp":"2025-07-31T22:35:00.017Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T22:35:00.026Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T22:35:00.038Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T22:35:00.039Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T22:36:00.011Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T22:37:00.012Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T22:38:00.014Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T22:39:00.008Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T22:40:00.014Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T22:40:00.019Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"d3324dda-2006-4387-a44e-7d4bffde1d5c","timestamp":"2025-07-31T22:40:00.019Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T22:40:00.025Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T22:40:00.030Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T22:40:00.031Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T22:41:00.015Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T22:42:00.008Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T22:43:00.013Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T22:44:00.011Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T22:45:00.015Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T22:45:00.017Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"6e73338d-c085-40c2-91fa-76fe1115344e","timestamp":"2025-07-31T22:45:00.017Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T22:45:00.025Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T22:45:00.032Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T22:45:00.033Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T22:46:00.013Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T22:47:00.014Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T22:48:00.014Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T22:49:00.015Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T22:50:00.014Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T22:50:00.022Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"578e3eae-e603-4b08-a13e-b8c44403c976","timestamp":"2025-07-31T22:50:00.023Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T22:50:00.033Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T22:50:00.073Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T22:50:00.074Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T22:51:00.011Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T22:52:00.015Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T22:53:00.015Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T22:54:00.013Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T22:55:00.013Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T22:55:00.017Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"ee5b627e-3c50-4a6f-b5be-cb240d083568","timestamp":"2025-07-31T22:55:00.018Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T22:55:00.031Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T22:55:00.077Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T22:55:00.078Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T22:56:00.010Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T22:57:00.015Z"}
{"level":"info","message":"Commit Tag: $GIT_SHA","timestamp":"2025-07-31T22:59:08.911Z"}
{"level":"info","message":"Starting Jellyseerr version 2.7.2","timestamp":"2025-07-31T22:59:11.139Z"}
{"label":"Settings Migrator","level":"debug","message":"Checking migration '0001_migrate_hostname.js'...","timestamp":"2025-07-31T22:59:14.722Z"}
{"label":"Settings Migrator","level":"debug","message":"Checking migration '0002_migrate_apitokens.js'...","timestamp":"2025-07-31T22:59:14.730Z"}
{"label":"Settings Migrator","level":"debug","message":"Checking migration '0003_emby_media_server_type.js'...","timestamp":"2025-07-31T22:59:14.736Z"}
{"label":"Settings Migrator","level":"debug","message":"Checking migration '0004_migrate_region_setting.js'...","timestamp":"2025-07-31T22:59:14.744Z"}
{"label":"Settings Migrator","level":"debug","message":"Checking migration '0005_migrate_network_settings.js'...","timestamp":"2025-07-31T22:59:14.747Z"}
{"label":"Settings Migrator","level":"debug","message":"Checking migration '0006_remove_lunasea.js'...","timestamp":"2025-07-31T22:59:14.749Z"}
{"label":"Notifications","level":"info","message":"Registered notification agents","timestamp":"2025-07-31T22:59:14.760Z"}
{"label":"Jobs","level":"info","message":"Scheduled jobs loaded","timestamp":"2025-07-31T22:59:14.924Z"}
{"label":"Server","level":"info","message":"Server ready on port 5055","timestamp":"2025-07-31T22:59:15.671Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Download Sync Reset","timestamp":"2025-07-31T23:00:00.013Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T23:00:00.022Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T23:00:00.050Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"95a56f15-074b-4016-992a-ea4dbc0fed1c","timestamp":"2025-07-31T23:00:00.051Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T23:00:00.063Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T23:00:00.146Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T23:00:00.151Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T23:01:00.012Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T23:02:00.007Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T23:03:00.011Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T23:04:00.007Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T23:05:00.012Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T23:05:00.016Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"77c5d22e-a3c6-42d3-b395-9df0d32ab4a0","timestamp":"2025-07-31T23:05:00.017Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T23:05:00.031Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T23:05:00.064Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T23:05:00.065Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T23:06:00.004Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T23:07:00.014Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T23:08:00.014Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T23:09:00.015Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T23:10:00.011Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T23:10:00.016Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"44895134-6f15-486a-bc28-4571afccaf02","timestamp":"2025-07-31T23:10:00.017Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T23:10:00.024Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T23:10:00.053Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T23:10:00.054Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T23:11:00.010Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T23:12:00.008Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T23:13:00.012Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T23:14:00.014Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T23:15:00.013Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T23:15:00.017Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"8c39460d-f353-4a4d-90cc-10d3ba88bd38","timestamp":"2025-07-31T23:15:00.017Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T23:15:00.024Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T23:15:00.036Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T23:15:00.037Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T23:16:00.014Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T23:17:00.012Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T23:18:00.010Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T23:19:00.012Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T23:20:00.003Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T23:20:00.006Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"eb3219af-7cfa-48d0-a8ee-c1e7696e390f","timestamp":"2025-07-31T23:20:00.006Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T23:20:00.011Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T23:20:00.018Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T23:20:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T23:21:00.013Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T23:22:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T23:23:00.013Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T23:24:00.012Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T23:25:00.013Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T23:25:00.016Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"acbc9c2c-f901-4b74-a9ae-1a7e8a5c415c","timestamp":"2025-07-31T23:25:00.016Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T23:25:00.020Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T23:25:00.062Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T23:25:00.062Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T23:26:00.013Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T23:27:00.012Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T23:28:00.009Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T23:29:00.013Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T23:30:00.002Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T23:30:00.005Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"ba78c68b-1766-4a9b-914b-3dae90c2e331","timestamp":"2025-07-31T23:30:00.005Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Process Blacklisted Tags","timestamp":"2025-07-31T23:30:00.009Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T23:30:00.018Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T23:30:00.032Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T23:30:00.032Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T23:31:00.009Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T23:32:00.011Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T23:33:00.014Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T23:34:00.013Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T23:35:00.010Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T23:35:00.017Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"d3a635a9-47e3-478e-822c-098539c12420","timestamp":"2025-07-31T23:35:00.017Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T23:35:00.031Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T23:35:00.066Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T23:35:00.066Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T23:36:00.007Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T23:37:00.012Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T23:38:00.013Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T23:39:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T23:40:00.013Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T23:40:00.017Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"00faf293-b2bd-4ad9-8957-b5da87ad9dd8","timestamp":"2025-07-31T23:40:00.017Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T23:40:00.024Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T23:40:00.031Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T23:40:00.031Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T23:41:00.012Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T23:42:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T23:43:00.011Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T23:44:00.012Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T23:45:00.013Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T23:45:00.017Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"672a13a8-a34b-4156-8be0-504459f226fd","timestamp":"2025-07-31T23:45:00.017Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T23:45:00.021Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T23:45:00.031Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T23:45:00.032Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T23:46:00.007Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T23:47:00.013Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T23:48:00.010Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T23:49:00.012Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T23:50:00.010Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T23:50:00.013Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"ff6c8046-a000-4284-ac95-0d691babcb0a","timestamp":"2025-07-31T23:50:00.013Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T23:50:00.018Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T23:50:00.025Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T23:50:00.025Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T23:51:00.011Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T23:52:00.011Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T23:53:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T23:54:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T23:55:00.012Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T23:55:00.015Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"ff4364f8-d6c3-4239-8e98-d2ec0d6c27ed","timestamp":"2025-07-31T23:55:00.015Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T23:55:00.018Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T23:55:00.023Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T23:55:00.023Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T23:56:00.012Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T23:57:00.011Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T23:58:00.011Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T23:59:00.011Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T00:00:00.009Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T00:00:00.012Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"f464b1df-965f-439d-ae58-7e3077024b24","timestamp":"2025-08-01T00:00:00.012Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T00:00:00.024Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T00:00:00.075Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T00:00:00.075Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T00:01:00.004Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T00:02:00.012Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T00:03:00.012Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T00:04:00.009Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T00:05:00.011Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T00:05:00.013Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"0a2124da-64c9-40cd-9bb4-b162b52dee38","timestamp":"2025-08-01T00:05:00.013Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T00:05:00.017Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T00:05:00.022Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T00:05:00.022Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T00:06:00.010Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T00:07:00.010Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T00:08:00.012Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T00:09:00.012Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T00:10:00.011Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T00:10:00.014Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"94cb4e76-9392-43eb-8bbc-dc462bed3cea","timestamp":"2025-08-01T00:10:00.014Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T00:10:00.018Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T00:10:00.023Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T00:10:00.023Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T00:11:00.011Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T00:12:00.011Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T00:13:00.011Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T00:14:00.008Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T00:15:00.009Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T00:15:00.011Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"d2d3cabd-2d3c-490d-9648-99852e14926e","timestamp":"2025-08-01T00:15:00.011Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T00:15:00.016Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T00:15:00.054Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T00:15:00.056Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T00:16:00.010Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T00:17:00.011Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T00:18:00.011Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T00:19:00.011Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T00:20:00.009Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T00:20:00.012Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"987ce446-837d-4467-afe5-5ae3016c48ef","timestamp":"2025-08-01T00:20:00.013Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T00:20:00.029Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T00:20:00.037Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T00:20:00.038Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T00:21:00.005Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T00:22:00.011Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T00:23:00.011Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T00:24:00.006Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T00:25:00.011Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T00:25:00.014Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"f54022a2-82f1-463f-9c23-fbc057ccfa16","timestamp":"2025-08-01T00:25:00.014Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T00:25:00.019Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T00:25:00.025Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T00:25:00.025Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T00:26:00.011Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T00:27:00.006Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T00:28:00.004Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T00:29:00.010Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T00:30:00.012Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T00:30:00.013Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"dcb7435f-de72-47fa-9eb7-4dda61fbbe18","timestamp":"2025-08-01T00:30:00.013Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T00:30:00.017Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T00:30:00.059Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T00:30:00.059Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T00:31:00.010Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T00:32:00.000Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T00:33:00.011Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T00:34:00.011Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T00:35:00.005Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T00:35:00.008Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"045f3810-531d-4793-9fcb-c37fb4444908","timestamp":"2025-08-01T00:35:00.008Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T00:35:00.014Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T00:35:00.045Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T00:35:00.046Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T00:36:00.007Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T00:37:00.011Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T00:38:00.010Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T00:39:00.011Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T00:40:00.010Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T00:40:00.012Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"a65cde11-dcfb-4dd8-b04f-95f852fb1163","timestamp":"2025-08-01T00:40:00.012Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T00:40:00.018Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T00:40:00.047Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T00:40:00.047Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T00:41:00.010Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T00:42:00.006Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T00:43:00.011Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T00:44:00.010Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T00:45:00.010Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T00:45:00.013Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"f916c324-273c-4ecb-adf3-e38587a0a7fc","timestamp":"2025-08-01T00:45:00.013Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T00:45:00.018Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T00:45:00.025Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T00:45:00.025Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T00:46:00.008Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T00:47:00.010Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T00:48:00.010Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T00:49:00.008Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T00:50:00.010Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T00:50:00.016Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"78a18f3f-1e64-4477-9e9b-99a6e0129c14","timestamp":"2025-08-01T00:50:00.017Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T00:50:00.023Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T00:50:00.070Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T00:50:00.072Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T00:51:00.009Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T00:52:00.010Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T00:53:00.011Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T00:54:00.010Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T00:55:00.006Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T00:55:00.010Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"f51d05b2-85b0-4c64-bf80-8c88f3ccf746","timestamp":"2025-08-01T00:55:00.010Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T00:55:00.014Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T00:55:00.038Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T00:55:00.039Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T00:56:00.011Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T00:57:00.010Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T00:58:00.005Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T00:59:00.005Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T01:00:00.008Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T01:00:00.011Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"ad95025e-2262-4a47-b551-8a1e3cd83c40","timestamp":"2025-08-01T01:00:00.012Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Full Scan","timestamp":"2025-08-01T01:00:00.019Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"63a31ee4-ee01-48c1-aa4f-ca3d192f79b0","timestamp":"2025-08-01T01:00:00.019Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T01:00:00.040Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process library: Películas","timestamp":"2025-08-01T01:00:00.054Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T01:00:00.099Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T01:00:00.106Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T01:00:00.107Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T01:00:00.107Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T01:01:00.007Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T01:02:00.010Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T01:03:00.010Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T01:04:00.011Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T01:05:00.009Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T01:05:00.011Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"47e34658-d9ec-493b-bc11-c0d3dc8901b8","timestamp":"2025-08-01T01:05:00.012Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T01:05:00.017Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T01:05:00.048Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T01:05:00.050Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T01:06:00.010Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T01:07:00.009Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T01:08:00.009Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T01:09:00.009Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T01:10:00.011Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T01:10:00.013Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"200f6406-fdaf-4fbc-85ab-d50e773295c3","timestamp":"2025-08-01T01:10:00.013Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T01:10:00.017Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T01:10:00.043Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T01:10:00.044Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T01:11:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T01:12:00.011Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T01:13:00.010Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T01:14:00.010Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T01:15:00.009Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T01:15:00.012Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"1e42178c-a249-47e3-a5cc-a00eaf760e12","timestamp":"2025-08-01T01:15:00.012Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T01:15:00.018Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T01:15:00.046Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T01:15:00.049Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T01:16:00.007Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T01:17:00.009Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T01:18:00.009Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T01:19:00.010Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T01:20:00.010Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T01:20:00.013Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"910e23d2-5da0-47c1-90c7-9f2b4cf21457","timestamp":"2025-08-01T01:20:00.013Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T01:20:00.021Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T01:20:00.055Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T01:20:00.055Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T01:21:00.009Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T01:22:00.010Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T01:23:00.010Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T01:24:00.009Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T01:25:00.001Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T01:25:00.004Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"dd8d8756-3a03-4cd0-8982-ed41527e849a","timestamp":"2025-08-01T01:25:00.004Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T01:25:00.009Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T01:25:00.039Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T01:25:00.042Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T01:26:00.007Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T01:27:00.009Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T01:28:00.009Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T01:29:00.011Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T01:30:00.008Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T01:30:00.010Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"d82163ab-0c0e-490c-99a2-8b459461f429","timestamp":"2025-08-01T01:30:00.010Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T01:30:00.014Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T01:30:00.041Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T01:30:00.041Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T01:31:00.010Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T01:32:00.007Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T01:33:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T01:34:00.008Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T01:35:00.009Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T01:35:00.012Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"513b182c-85b9-468f-b823-7aab9a987935","timestamp":"2025-08-01T01:35:00.012Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T01:35:00.016Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T01:35:00.042Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T01:35:00.044Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T01:36:00.009Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T01:37:00.009Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T01:38:00.009Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T01:39:00.010Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T01:40:00.004Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T01:40:00.008Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"58225dcf-531d-4271-a7af-e356f87acf6a","timestamp":"2025-08-01T01:40:00.008Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T01:40:00.012Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T01:40:00.046Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T01:40:00.048Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T01:41:00.008Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T01:42:00.010Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T01:43:00.009Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T01:44:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T01:45:00.009Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T01:45:00.012Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"30af7520-2364-4b53-8442-106f070686cb","timestamp":"2025-08-01T01:45:00.012Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T01:45:00.018Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T01:45:00.025Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T01:45:00.026Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T01:46:00.004Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T01:47:00.010Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T01:48:00.010Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T01:49:00.009Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T01:50:00.009Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T01:50:00.012Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"5f61a92f-8810-4bc2-bfd7-88c83aa7c94b","timestamp":"2025-08-01T01:50:00.012Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T01:50:00.018Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T01:50:00.048Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T01:50:00.048Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T01:51:00.009Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T01:52:00.008Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T01:53:00.004Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T01:54:00.008Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T01:55:00.009Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T01:55:00.011Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"baf66f7e-ca30-4fc7-875c-df238fbc8fe1","timestamp":"2025-08-01T01:55:00.011Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T01:55:00.015Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T01:55:00.045Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T01:55:00.045Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T01:56:00.006Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T01:57:00.008Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T01:58:00.008Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T01:59:00.010Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T02:00:00.006Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T02:00:00.009Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"ecd8c80a-5823-4155-9b31-54d1fb451a68","timestamp":"2025-08-01T02:00:00.009Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Radarr Scan","timestamp":"2025-08-01T02:00:00.012Z"}
{"label":"Radarr Scan","level":"info","message":"Scan starting","sessionId":"371f8cbc-23bb-491a-adcd-6f0752841ce4","timestamp":"2025-08-01T02:00:00.012Z"}
{"label":"Radarr Scan","level":"info","message":"Beginning to process Radarr server: Radarr","timestamp":"2025-08-01T02:00:00.012Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T02:00:00.022Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T02:00:00.052Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T02:00:00.053Z"}
{"label":"Radarr Scan","level":"debug","message":"Title already exists and no changes detected for Destino final: Lazos de sangre","timestamp":"2025-08-01T02:00:00.491Z"}
{"label":"Radarr Scan","level":"debug","message":"Title already exists and no changes detected for Malditos bastardos","timestamp":"2025-08-01T02:00:00.491Z"}
{"label":"Radarr Scan","level":"debug","message":"Title already exists and no changes detected for Ocean's Eleven. Hagan juego","timestamp":"2025-08-01T02:00:00.493Z"}
{"label":"Radarr Scan","level":"debug","message":"Title already exists and no changes detected for Ocean's 8","timestamp":"2025-08-01T02:00:00.493Z"}
{"label":"Radarr Scan","level":"debug","message":"Title already exists and no changes detected for Oppenheimer","timestamp":"2025-08-01T02:00:00.494Z"}
{"label":"Radarr Scan","level":"debug","message":"Title already exists and no changes detected for Ocean's Thirteen","timestamp":"2025-08-01T02:00:00.495Z"}
{"label":"Radarr Scan","level":"debug","message":"Title already exists and no changes detected for Shutter Island","timestamp":"2025-08-01T02:00:00.495Z"}
{"label":"Radarr Scan","level":"debug","message":"Title already exists and no changes detected for Ocean's Twelve","timestamp":"2025-08-01T02:00:00.496Z"}
{"label":"Radarr Scan","level":"debug","message":"Title already exists and no changes detected for Las crónicas de Narnia: El león, la bruja y el armario","timestamp":"2025-08-01T02:00:00.496Z"}
{"label":"Radarr Scan","level":"debug","message":"Title already exists and no changes detected for Las crónicas de Narnia: La travesía del viajero del alba","timestamp":"2025-08-01T02:00:00.496Z"}
{"label":"Radarr Scan","level":"debug","message":"Title already exists and no changes detected for El contable 2","timestamp":"2025-08-01T02:00:00.497Z"}
{"label":"Radarr Scan","level":"debug","message":"Title already exists and no changes detected for El señor de los anillos: La comunidad del anillo","timestamp":"2025-08-01T02:00:00.497Z"}
{"label":"Radarr Scan","level":"debug","message":"Title already exists and no changes detected for Las crónicas de Narnia: El príncipe Caspian","timestamp":"2025-08-01T02:00:00.497Z"}
{"label":"Radarr Scan","level":"debug","message":"Title already exists and no changes detected for Prometheus","timestamp":"2025-08-01T02:00:00.497Z"}
{"label":"Radarr Scan","level":"debug","message":"Title already exists and no changes detected for Los odiosos ocho","timestamp":"2025-08-01T02:00:00.498Z"}
{"label":"Radarr Scan","level":"debug","message":"Title already exists and no changes detected for Until Dawn","timestamp":"2025-08-01T02:00:00.499Z"}
{"label":"Radarr Scan","level":"debug","message":"Title already exists and no changes detected for Los pecadores","timestamp":"2025-08-01T02:00:00.499Z"}
{"label":"Radarr Scan","level":"debug","message":"Title already exists and no changes detected for Kung Fu Panda","timestamp":"2025-08-01T02:00:00.500Z"}
{"label":"Radarr Scan","level":"debug","message":"Title already exists and no changes detected for Kung Fu Panda 2","timestamp":"2025-08-01T02:00:00.500Z"}
{"label":"Radarr Scan","level":"debug","message":"Title already exists and no changes detected for Kung Fu Panda 3","timestamp":"2025-08-01T02:00:00.500Z"}
{"label":"Radarr Scan","level":"debug","message":"Title already exists and no changes detected for Kung Fu Panda 4","timestamp":"2025-08-01T02:00:00.501Z"}
{"label":"Radarr Scan","level":"debug","message":"Title already exists and no changes detected for Una película de Minecraft","timestamp":"2025-08-01T02:00:00.501Z"}
{"label":"Radarr Scan","level":"debug","message":"Title already exists and no changes detected for El mundo perdido: Jurassic Park","timestamp":"2025-08-01T02:00:00.502Z"}
{"label":"Radarr Scan","level":"debug","message":"Title already exists and no changes detected for Gru 4. Mi villano favorito","timestamp":"2025-08-01T02:00:00.502Z"}
{"label":"Radarr Scan","level":"debug","message":"Title already exists and no changes detected for Interstellar","timestamp":"2025-08-01T02:00:00.504Z"}
{"label":"Radarr Scan","level":"debug","message":"Title already exists and no changes detected for Un lugar tranquilo: Día uno","timestamp":"2025-08-01T02:00:00.506Z"}
{"label":"Radarr Scan","level":"debug","message":"Title already exists and no changes detected for Capitán América: Brave New World","timestamp":"2025-08-01T02:00:00.506Z"}
{"label":"Radarr Scan","level":"info","message":"Media for Jurassic Park (Parque Jurásico) exists. Changes were detected and the title will be updated.","timestamp":"2025-08-01T02:00:00.529Z"}
{"label":"Radarr Scan","level":"info","message":"Media for Cómo entrenar a tu dragón 3 exists. Changes were detected and the title will be updated.","timestamp":"2025-08-01T02:00:00.529Z"}
{"label":"Radarr Scan","level":"info","message":"Media for Náufrago exists. Changes were detected and the title will be updated.","timestamp":"2025-08-01T02:00:00.530Z"}
{"label":"Radarr Scan","level":"info","message":"Media for Cómo entrenar a tu dragón exists. Changes were detected and the title will be updated.","timestamp":"2025-08-01T02:00:00.530Z"}
{"label":"Radarr Scan","level":"info","message":"Media for Jungla de cristal exists. Changes were detected and the title will be updated.","timestamp":"2025-08-01T02:00:00.530Z"}
{"label":"Radarr Scan","level":"info","message":"Media for American History X exists. Changes were detected and the title will be updated.","timestamp":"2025-08-01T02:00:00.531Z"}
{"label":"Radarr Scan","level":"info","message":"Media for Cómo entrenar a tu dragón exists. Changes were detected and the title will be updated.","timestamp":"2025-08-01T02:00:00.531Z"}
{"label":"Radarr Scan","level":"info","message":"Media for John Wick 4 exists. Changes were detected and the title will be updated.","timestamp":"2025-08-01T02:00:00.532Z"}
{"label":"Radarr Scan","level":"info","message":"Media for Los renglones torcidos de Dios exists. Changes were detected and the title will be updated.","timestamp":"2025-08-01T02:00:00.532Z"}
{"label":"Radarr Scan","level":"info","message":"Media for Destino final 2 exists. Changes were detected and the title will be updated.","timestamp":"2025-08-01T02:00:00.533Z"}
{"label":"Radarr Scan","level":"info","message":"Media for Babylon exists. Changes were detected and the title will be updated.","timestamp":"2025-08-01T02:00:00.533Z"}
{"label":"Radarr Scan","level":"info","message":"Media for Vengadores: Infinity War exists. Changes were detected and the title will be updated.","timestamp":"2025-08-01T02:00:00.533Z"}
{"label":"Radarr Scan","level":"info","message":"Media for Destino final 3 exists. Changes were detected and the title will be updated.","timestamp":"2025-08-01T02:00:00.534Z"}
{"label":"Radarr Scan","level":"info","message":"Media for Destino final 4 exists. Changes were detected and the title will be updated.","timestamp":"2025-08-01T02:00:00.534Z"}
{"label":"Radarr Scan","level":"info","message":"Media for El renacido exists. Changes were detected and the title will be updated.","timestamp":"2025-08-01T02:00:00.534Z"}
{"label":"Radarr Scan","level":"info","message":"Media for Destino final exists. Changes were detected and the title will be updated.","timestamp":"2025-08-01T02:00:00.535Z"}
{"label":"Radarr Scan","level":"info","message":"Media for Destino final 5 exists. Changes were detected and the title will be updated.","timestamp":"2025-08-01T02:00:00.535Z"}
{"label":"Radarr Scan","level":"info","message":"Media for Django desencadenado exists. Changes were detected and the title will be updated.","timestamp":"2025-08-01T02:00:00.535Z"}
{"label":"Radarr Scan","level":"info","message":"Media for Cómo entrenar a tu dragón 2 exists. Changes were detected and the title will be updated.","timestamp":"2025-08-01T02:00:00.536Z"}
{"label":"Radarr Scan","level":"info","message":"Radarr scan complete","timestamp":"2025-08-01T02:00:04.540Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T02:01:00.007Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T02:02:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T02:03:00.008Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T02:04:00.010Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T02:05:00.001Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T02:05:00.004Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"bb6a22f6-62d2-41df-8f38-b38466fcbc76","timestamp":"2025-08-01T02:05:00.004Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T02:05:00.009Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T02:05:00.036Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T02:05:00.036Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T02:06:00.009Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T02:07:00.008Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T02:08:00.008Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T02:09:00.008Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T02:10:00.009Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T02:10:00.013Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"7d576496-cf72-4d62-a792-542768b2f6e4","timestamp":"2025-08-01T02:10:00.013Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T02:10:00.017Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T02:10:00.044Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T02:10:00.045Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T02:11:00.008Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T02:12:00.008Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T02:13:00.009Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T02:14:00.009Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T02:15:00.007Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T02:15:00.010Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"50d38dc9-9a0f-46f9-bc92-5174060f6382","timestamp":"2025-08-01T02:15:00.010Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T02:15:00.016Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T02:15:00.043Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T02:15:00.045Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T02:16:00.007Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T02:17:00.009Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T02:18:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T02:19:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T02:20:00.002Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T02:20:00.004Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"d0a9a9f8-3560-45f9-8165-7e26a894a1c0","timestamp":"2025-08-01T02:20:00.005Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T02:20:00.009Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T02:20:00.039Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T02:20:00.039Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T02:21:00.008Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T02:22:00.008Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T02:23:00.007Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T02:24:00.007Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T02:25:00.008Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T02:25:00.010Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"08b8ce7b-25d0-40b8-809d-e0c0800b54a6","timestamp":"2025-08-01T02:25:00.011Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T02:25:00.020Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T02:25:00.052Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T02:25:00.052Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T02:26:00.008Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T02:27:00.007Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T02:28:00.006Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T02:29:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T02:30:00.002Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T02:30:00.004Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"3a1eeb44-d83e-4663-bf80-cb70d0a91c11","timestamp":"2025-08-01T02:30:00.005Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Sonarr Scan","timestamp":"2025-08-01T02:30:00.008Z"}
{"label":"Sonarr Scan","level":"info","message":"Scan starting","sessionId":"848d923a-0858-40bd-a68a-376cbc4374a1","timestamp":"2025-08-01T02:30:00.008Z"}
{"label":"Sonarr Scan","level":"info","message":"Beginning to process Sonarr server: Sonarr","timestamp":"2025-08-01T02:30:00.009Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T02:30:00.012Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T02:30:00.042Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T02:30:00.042Z"}
{"label":"Sonarr Scan","level":"debug","message":"Updating existing title: Rick and Morty","timestamp":"2025-08-01T02:30:00.815Z"}
{"label":"Sonarr Scan","level":"debug","message":"Updating existing title: Game of Thrones","timestamp":"2025-08-01T02:30:00.917Z"}
{"label":"Sonarr Scan","level":"debug","message":"Updating existing title: Dragon Ball","timestamp":"2025-08-01T02:30:00.999Z"}
{"label":"Sonarr Scan","level":"debug","message":"Updating existing title: The Big Bang Theory","timestamp":"2025-08-01T02:30:01.000Z"}
{"label":"Sonarr Scan","level":"debug","message":"Updating existing title: Squid Game","timestamp":"2025-08-01T02:30:01.001Z"}
{"label":"Sonarr Scan","level":"debug","message":"Updating existing title: Peaky Blinders","timestamp":"2025-08-01T02:30:01.013Z"}
{"label":"Sonarr Scan","level":"debug","message":"Updating existing title: Dark","timestamp":"2025-08-01T02:30:01.020Z"}
{"label":"Sonarr Scan","level":"debug","message":"Updating existing title: Prison Break","timestamp":"2025-08-01T02:30:01.029Z"}
{"label":"Sonarr Scan","level":"debug","message":"Updating existing title: House of the Dragon","timestamp":"2025-08-01T02:30:01.029Z"}
{"label":"Sonarr Scan","level":"debug","message":"Updating existing title: The Boys","timestamp":"2025-08-01T02:30:01.032Z"}
{"label":"Sonarr Scan","level":"debug","message":"Updating existing title: Breaking Bad","timestamp":"2025-08-01T02:30:01.043Z"}
{"label":"Sonarr Scan","level":"debug","message":"Updating existing title: Stranger Things","timestamp":"2025-08-01T02:30:01.052Z"}
{"label":"Sonarr Scan","level":"debug","message":"Updating existing title: Dexter","timestamp":"2025-08-01T02:30:01.084Z"}
{"label":"Sonarr Scan","level":"debug","message":"Updating existing title: Friends","timestamp":"2025-08-01T02:30:01.149Z"}
{"label":"Sonarr Scan","level":"debug","message":"Updating existing title: Outlander","timestamp":"2025-08-01T02:30:01.150Z"}
{"label":"Sonarr Scan","level":"debug","message":"Updating existing title: Better Call Saul","timestamp":"2025-08-01T02:30:01.153Z"}
{"label":"Sonarr Scan","level":"debug","message":"Updating existing title: Chernobyl","timestamp":"2025-08-01T02:30:01.155Z"}
{"label":"Sonarr Scan","level":"debug","message":"Updating existing title: Black Mirror","timestamp":"2025-08-01T02:30:01.158Z"}
{"label":"Sonarr Scan","level":"info","message":"Sonarr scan complete","timestamp":"2025-08-01T02:30:05.158Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T02:31:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T02:32:00.007Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T02:33:00.007Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T02:34:00.005Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T02:35:00.008Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T02:35:00.010Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"9b66c905-1b58-41cc-98f5-585b18284d3e","timestamp":"2025-08-01T02:35:00.010Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T02:35:00.015Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T02:35:00.041Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T02:35:00.042Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T02:36:00.004Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T02:37:00.008Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T02:38:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T02:39:00.007Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T02:40:00.007Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T02:40:00.010Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"1511f504-4419-429b-be19-a16276daa267","timestamp":"2025-08-01T02:40:00.010Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T02:40:00.025Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T02:40:00.067Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T02:40:00.069Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T02:41:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T02:42:00.010Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T02:43:00.007Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T02:44:00.006Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T02:45:00.008Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T02:45:00.010Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"02a0bb38-70ce-46fe-8407-bf9598a51f98","timestamp":"2025-08-01T02:45:00.011Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T02:45:00.015Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T02:45:00.024Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T02:45:00.030Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T02:46:00.007Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T02:47:00.007Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T02:48:00.008Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T02:49:00.007Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T02:50:00.007Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T02:50:00.010Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"e11d8d2e-8654-4248-a695-b3a6cb09b897","timestamp":"2025-08-01T02:50:00.010Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T02:50:00.015Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T02:50:00.042Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T02:50:00.043Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T02:51:00.009Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T02:52:00.005Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T02:53:00.006Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T02:54:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T02:55:00.007Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T02:55:00.009Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"6885b404-d737-47a4-a200-0350e6fcc3a9","timestamp":"2025-08-01T02:55:00.009Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T02:55:00.014Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T02:55:00.045Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T02:55:00.047Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T02:56:00.006Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T02:57:00.004Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T02:58:00.005Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T02:59:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T03:00:00.008Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T03:00:00.011Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"2595b35e-4f6a-44d4-8b0e-17bc36818473","timestamp":"2025-08-01T03:00:00.011Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Image Cache Cleanup","timestamp":"2025-08-01T03:00:00.015Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Media Availability Sync","timestamp":"2025-08-01T03:00:00.019Z"}
{"label":"Availability Sync","level":"info","message":"Starting availability sync...","timestamp":"2025-08-01T03:00:00.020Z"}
{"label":"Image Cache","level":"error","message":"ENOENT: no such file or directory, scandir '/app/config/cache/images/tmdb'","timestamp":"2025-08-01T03:00:00.024Z"}
{"label":"Image Cache","level":"info","message":"Cleared 0 stale image(s) from cache 'tmdb'","timestamp":"2025-08-01T03:00:00.024Z"}
{"label":"Image Cache","level":"info","message":"Cleared 0 stale image(s) from cache 'avatar'","timestamp":"2025-08-01T03:00:00.024Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T03:00:00.029Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T03:00:00.062Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T03:00:00.063Z"}
{"error":"apiError","errorMessage":"INVALID_AUTH_TOKEN","label":"AvailabilitySync","level":"error","message":"Sync interrupted.","status":401,"timestamp":"2025-08-01T03:00:00.064Z"}
{"label":"Availability Sync","level":"info","message":"Availability sync complete.","timestamp":"2025-08-01T03:00:00.064Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T03:01:00.007Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T03:02:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T03:03:00.009Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T03:04:00.007Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T03:05:00.007Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T03:05:00.009Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"de2ee5c6-d4a8-41e0-bb2c-5127148e29db","timestamp":"2025-08-01T03:05:00.010Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T03:05:00.015Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T03:05:00.059Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T03:05:00.059Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T03:06:00.006Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T03:07:00.007Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T03:08:00.007Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T03:09:00.005Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T03:10:00.007Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T03:10:00.010Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"6914219d-5385-4181-8bee-b9fb632bb067","timestamp":"2025-08-01T03:10:00.010Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T03:10:00.014Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T03:10:00.044Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T03:10:00.045Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T03:11:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T03:12:00.007Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T03:13:00.006Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T03:14:00.004Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T03:15:00.006Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T03:15:00.008Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"77e49b28-947c-4896-94ec-32b06f87992b","timestamp":"2025-08-01T03:15:00.008Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T03:15:00.012Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T03:15:00.039Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T03:15:00.041Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T03:16:00.007Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T03:17:00.007Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T03:18:00.005Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T03:19:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T03:20:00.007Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T03:20:00.009Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"a9ed728f-e18c-4e98-9bc6-c0ea288631de","timestamp":"2025-08-01T03:20:00.009Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T03:20:00.013Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T03:20:00.052Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T03:20:00.052Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T03:21:00.005Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T03:22:00.005Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T03:23:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T03:24:00.007Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T03:25:00.008Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T03:25:00.014Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"16204650-e933-47c7-beee-69904be31713","timestamp":"2025-08-01T03:25:00.014Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T03:25:00.028Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T03:25:00.062Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T03:25:00.062Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T03:26:00.005Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T03:27:00.005Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T03:28:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T03:29:00.006Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T03:30:00.007Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T03:30:00.010Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"e57ae08f-4715-4551-9d71-11d54973ffff","timestamp":"2025-08-01T03:30:00.010Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T03:30:00.015Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T03:30:00.066Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T03:30:00.067Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T03:31:00.007Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T03:32:00.005Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T03:33:00.007Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T03:34:00.008Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T03:35:00.002Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T03:35:00.007Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"44ed832c-5b65-4099-a6b5-0e0f7afdf695","timestamp":"2025-08-01T03:35:00.008Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T03:35:00.014Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T03:35:00.051Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T03:35:00.051Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T03:36:00.006Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T03:37:00.004Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T03:38:00.005Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T03:39:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T03:40:00.007Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T03:40:00.009Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"e0a82aa3-a5c0-4134-b3c7-411cb05c6e12","timestamp":"2025-08-01T03:40:00.010Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T03:40:00.017Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T03:40:00.048Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T03:40:00.049Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T03:41:00.005Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T03:42:00.006Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T03:43:00.007Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T03:44:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T03:45:00.005Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T03:45:00.008Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"bcd25787-2d28-43d8-909c-fbd6ea6c8a5c","timestamp":"2025-08-01T03:45:00.008Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T03:45:00.013Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T03:45:00.023Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T03:45:00.024Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T03:46:00.006Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T03:47:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T03:48:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T03:49:00.005Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T03:50:00.005Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T03:50:00.008Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"fe0bdb9d-9ad4-4489-aeb1-22a5bd6582b4","timestamp":"2025-08-01T03:50:00.008Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T03:50:00.022Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T03:50:00.055Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T03:50:00.056Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T03:51:00.005Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T03:52:00.004Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T03:53:00.005Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T03:54:00.005Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T03:55:00.005Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T03:55:00.007Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"e97c8fd5-6d04-4b41-8632-3ee328cf6095","timestamp":"2025-08-01T03:55:00.007Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T03:55:00.014Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T03:55:00.043Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T03:55:00.043Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T03:56:00.006Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T03:57:00.005Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T03:58:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T03:59:00.005Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T04:00:00.005Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T04:00:00.007Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"91bd61fe-989d-4e06-b435-b9d7f1eb8b70","timestamp":"2025-08-01T04:00:00.008Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T04:00:00.014Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T04:00:00.044Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T04:00:00.044Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T04:01:00.005Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T04:02:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T04:03:00.006Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T04:04:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T04:05:00.003Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T04:05:00.006Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"e093223e-efb6-4c65-9b43-6e9e357f6f64","timestamp":"2025-08-01T04:05:00.006Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T04:05:00.019Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T04:05:00.064Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T04:05:00.065Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T04:06:00.006Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T04:07:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T04:08:00.006Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T04:09:00.005Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T04:10:00.005Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T04:10:00.008Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"6ed55fa5-a7b2-441e-ba32-2c72152914e6","timestamp":"2025-08-01T04:10:00.008Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T04:10:00.013Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T04:10:00.050Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T04:10:00.050Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T04:11:00.005Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T04:12:00.006Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T04:13:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T04:14:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T04:15:00.004Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T04:15:00.006Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"457ef269-b20b-46b8-8373-61c3cfef1fd1","timestamp":"2025-08-01T04:15:00.007Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T04:15:00.011Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T04:15:00.038Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T04:15:00.038Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T04:16:00.005Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T04:17:00.004Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T04:18:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T04:19:00.004Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T04:20:00.005Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T04:20:00.007Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"462dc69b-d5e8-4ac6-8463-decad249126e","timestamp":"2025-08-01T04:20:00.008Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T04:20:00.012Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T04:20:00.037Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T04:20:00.038Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T04:21:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T04:22:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T04:23:00.005Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T04:24:00.005Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T04:25:00.003Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T04:25:00.006Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"bc206c3c-74d6-4e56-b0fa-9aa56407a613","timestamp":"2025-08-01T04:25:00.006Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T04:25:00.011Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T04:25:00.045Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T04:25:00.047Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T04:26:00.005Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T04:27:00.005Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T04:28:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T04:29:00.004Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T04:30:00.004Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T04:30:00.006Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"dc55b17b-0668-4d72-a228-a21ef67db134","timestamp":"2025-08-01T04:30:00.006Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T04:30:00.012Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T04:30:00.042Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T04:30:00.047Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T04:31:00.004Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T04:32:00.004Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T04:33:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T04:34:00.004Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T04:35:00.003Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T04:35:00.005Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"70746395-dad8-4dc5-be07-2f45e92466f6","timestamp":"2025-08-01T04:35:00.005Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T04:35:00.010Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T04:35:00.039Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T04:35:00.040Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T04:36:00.004Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T04:37:00.004Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T04:38:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T04:39:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T04:40:00.004Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T04:40:00.006Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"330a4d31-181f-4c87-a67f-9d970c52420e","timestamp":"2025-08-01T04:40:00.006Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T04:40:00.011Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T04:40:00.034Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T04:40:00.034Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T04:41:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T04:42:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T04:43:00.004Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T04:44:00.004Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T04:45:00.003Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T04:45:00.006Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"b86d1939-a70b-446c-bdf5-861c2f6901db","timestamp":"2025-08-01T04:45:00.006Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T04:45:00.014Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T04:45:00.050Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T04:45:00.054Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T04:46:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T04:47:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T04:48:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T04:49:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T04:50:00.003Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T04:50:00.006Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"9e9b023b-6622-4266-b104-dd9416737aa8","timestamp":"2025-08-01T04:50:00.006Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T04:50:00.009Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T04:50:00.035Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T04:50:00.036Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T04:51:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T04:52:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T04:53:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T04:54:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T04:55:00.002Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T04:55:00.005Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"8d675583-67de-4dc2-bfd0-2056840505fd","timestamp":"2025-08-01T04:55:00.005Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T04:55:00.010Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T04:55:00.037Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T04:55:00.038Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T04:56:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T04:57:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T04:58:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T04:59:00.004Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T05:00:00.003Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T05:00:00.006Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"1ee6f86a-44e4-4549-8bb8-84bc76f9a215","timestamp":"2025-08-01T05:00:00.006Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T05:00:00.011Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T05:00:00.039Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T05:00:00.039Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T05:01:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T05:02:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T05:03:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T05:04:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T05:05:00.002Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T05:05:00.005Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"016460c6-42d0-4f1c-a51f-ad900a9acfbd","timestamp":"2025-08-01T05:05:00.005Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T05:05:00.010Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T05:05:00.041Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T05:05:00.041Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T05:06:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T05:07:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T05:08:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T05:09:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T05:10:00.003Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T05:10:00.005Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"7d2b3728-13a5-40f8-b227-d3f3ba09a11c","timestamp":"2025-08-01T05:10:00.005Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T05:10:00.012Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T05:10:00.041Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T05:10:00.041Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T05:11:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T05:12:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T05:13:00.004Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T05:14:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T05:15:00.001Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T05:15:00.004Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"234b2b1a-c25d-4c13-bedc-fe1097e877bc","timestamp":"2025-08-01T05:15:00.004Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T05:15:00.010Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T05:15:00.035Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T05:15:00.036Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T05:16:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T05:17:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T05:18:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T05:19:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T05:20:00.003Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T05:20:00.005Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"4d49a65d-1bbe-46e0-a549-932ab2d1e915","timestamp":"2025-08-01T05:20:00.005Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T05:20:00.010Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T05:20:00.038Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T05:20:00.038Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T05:21:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T05:22:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T05:23:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T05:24:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T05:25:00.001Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T05:25:00.004Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"25d985cb-1183-4949-8e91-54141cd9df9c","timestamp":"2025-08-01T05:25:00.004Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T05:25:00.010Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T05:25:00.043Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T05:25:00.044Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T05:26:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T05:27:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T05:28:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T05:29:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T05:30:00.000Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T05:30:00.004Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"618163c7-d804-4ce6-a1bf-7e9de765c665","timestamp":"2025-08-01T05:30:00.004Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T05:30:00.011Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T05:30:00.034Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T05:30:00.035Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T05:31:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T05:32:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T05:33:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T05:34:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T05:35:00.002Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T05:35:00.005Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"feefc7eb-8b4b-40a6-87aa-2f7663e065ce","timestamp":"2025-08-01T05:35:00.005Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T05:35:00.009Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T05:35:00.035Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T05:35:00.036Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T05:36:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T05:37:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T05:38:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T05:39:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T05:40:00.001Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T05:40:00.004Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"5bb5b2e6-166f-416a-896b-24d3b5d46c29","timestamp":"2025-08-01T05:40:00.004Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T05:40:00.010Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T05:40:00.036Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T05:40:00.040Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T05:41:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T05:42:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T05:43:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T05:44:00.000Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T05:45:00.002Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T05:45:00.004Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"e64f142f-1704-4009-bc34-6b1b5a795b0c","timestamp":"2025-08-01T05:45:00.004Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T05:45:00.009Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T05:45:00.045Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T05:45:00.045Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T05:46:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T05:47:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T05:48:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T05:49:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T05:50:00.001Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T05:50:00.003Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"08d3350e-31fb-42cc-8518-fb78a7e2a222","timestamp":"2025-08-01T05:50:00.003Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T05:50:00.009Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T05:50:00.038Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T05:50:00.038Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T05:51:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T05:52:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T05:53:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T05:54:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T05:55:00.001Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T05:55:00.004Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"8f7e178c-47ff-4830-8699-38b59acd190d","timestamp":"2025-08-01T05:55:00.004Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T05:55:00.009Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T05:55:00.041Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T05:55:00.042Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T05:56:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T05:57:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T05:58:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T05:59:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T06:00:00.003Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T06:00:00.005Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"3155734b-de64-43a6-ba4a-33f9472748d6","timestamp":"2025-08-01T06:00:00.005Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T06:00:00.009Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T06:00:00.039Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T06:00:00.040Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T06:01:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T06:02:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T06:03:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T06:04:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T06:05:00.001Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T06:05:00.003Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"b45645d2-7410-481d-a069-1175a713aa51","timestamp":"2025-08-01T06:05:00.004Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T06:05:00.009Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T06:05:00.035Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T06:05:00.036Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T06:06:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T06:07:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T06:08:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T06:09:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T06:10:00.003Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T06:10:00.004Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"9039f17b-14cc-45eb-9b06-aae6ff7a14cc","timestamp":"2025-08-01T06:10:00.005Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T06:10:00.010Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T06:10:00.041Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T06:10:00.042Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T06:11:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T06:12:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T06:13:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T06:14:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T06:15:00.002Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T06:15:00.004Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"aa69dede-a811-496c-91a3-6f62b06143b9","timestamp":"2025-08-01T06:15:00.005Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T06:15:00.011Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T06:15:00.040Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T06:15:00.042Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T06:16:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T06:17:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T06:18:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T06:19:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T06:20:00.001Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T06:20:00.004Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"da3bcc55-3952-4ee7-b6e2-854e4b9a7fc1","timestamp":"2025-08-01T06:20:00.004Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T06:20:00.012Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T06:20:00.041Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T06:20:00.043Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T06:21:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T06:22:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T06:23:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T06:24:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T06:25:00.002Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T06:25:00.004Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"17e52dff-c9f9-4263-964f-d82a75b03e15","timestamp":"2025-08-01T06:25:00.005Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T06:25:00.010Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T06:25:00.036Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T06:25:00.038Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T06:26:00.000Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T06:27:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T06:28:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T06:29:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T06:30:00.002Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T06:30:00.005Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"2402ee64-848f-4cb8-8f3d-b27050bbea66","timestamp":"2025-08-01T06:30:00.005Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T06:30:00.008Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T06:30:00.039Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T06:30:00.039Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T06:31:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T06:32:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T06:33:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T06:34:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T06:35:00.001Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T06:35:00.004Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"53ec42c0-e46e-486c-8869-9c9105d73680","timestamp":"2025-08-01T06:35:00.004Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T06:35:00.008Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T06:35:00.045Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T06:35:00.046Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T06:36:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T06:37:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T06:38:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T06:39:00.000Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T06:40:00.002Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T06:40:00.004Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"4881361a-1bfc-45d1-89f2-0e6f55e58bc4","timestamp":"2025-08-01T06:40:00.004Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T06:40:00.009Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T06:40:00.035Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T06:40:00.037Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T06:41:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T06:42:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T06:43:00.000Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T06:44:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T06:45:00.002Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T06:45:00.005Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"be029d00-b680-40d0-9784-16839f9c112d","timestamp":"2025-08-01T06:45:00.005Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T06:45:00.011Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T06:45:00.053Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T06:45:00.053Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T06:46:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T06:47:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T06:48:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T06:49:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T06:50:00.001Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T06:50:00.003Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"8c1a3ebb-a45f-407f-86d6-71120a121a64","timestamp":"2025-08-01T06:50:00.004Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T06:50:00.009Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T06:50:00.036Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T06:50:00.036Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T06:51:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T06:52:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T06:53:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T06:54:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T06:55:00.002Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T06:55:00.009Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"70448e53-e243-4f4a-9001-d33acea5dc4c","timestamp":"2025-08-01T06:55:00.010Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T06:55:00.031Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T06:55:00.090Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T06:55:00.091Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T06:56:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T06:57:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T06:58:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T06:59:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T07:00:00.001Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T07:00:00.005Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"f077f182-79dd-4533-a50d-9fd517707cd8","timestamp":"2025-08-01T07:00:00.005Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T07:00:00.012Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T07:00:00.044Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T07:00:00.044Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T07:01:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T07:02:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T07:03:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T07:04:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T07:05:00.015Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T07:05:00.019Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"a54a73ab-8b20-4732-b8e1-9421764b7d43","timestamp":"2025-08-01T07:05:00.019Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T07:05:00.023Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T07:05:00.056Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T07:05:00.057Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T07:06:00.028Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T07:07:00.025Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T07:08:00.030Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T07:09:00.029Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T07:10:00.026Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T07:10:00.028Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"d0919f43-0630-4070-960f-c989ea9c3476","timestamp":"2025-08-01T07:10:00.028Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T07:10:00.032Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T07:10:00.055Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T07:10:00.057Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T07:11:00.017Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T07:12:00.018Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T07:13:00.014Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T07:14:00.011Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T07:15:00.013Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T07:15:00.015Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"7c326145-1179-4188-98b4-f02f1304ed96","timestamp":"2025-08-01T07:15:00.015Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T07:15:00.020Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T07:15:00.049Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T07:15:00.050Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T07:16:00.027Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T07:17:00.014Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T07:18:00.007Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T07:19:00.009Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T07:20:00.027Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T07:20:00.029Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"852c0af6-aa28-4b83-80f5-e5484682634d","timestamp":"2025-08-01T07:20:00.029Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T07:20:00.033Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T07:20:00.061Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T07:20:00.061Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T07:21:00.020Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T07:22:00.022Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T07:23:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T07:24:00.010Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T07:25:00.030Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T07:25:00.032Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"4aa3305c-4dd9-44f5-89d3-ca5e663dbd22","timestamp":"2025-08-01T07:25:00.032Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T07:25:00.036Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T07:25:00.067Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T07:25:00.068Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T07:26:00.029Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T07:27:00.014Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T07:28:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T07:29:00.017Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T07:30:00.015Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T07:30:00.018Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"384ffdd3-95ef-4178-8f38-486e829ee81a","timestamp":"2025-08-01T07:30:00.018Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T07:30:00.023Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T07:30:00.067Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T07:30:00.068Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T07:31:00.020Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T07:32:00.029Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T07:33:00.026Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T07:34:00.020Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T07:35:00.028Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T07:35:00.030Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"899a901c-fb88-4531-bd53-81c775cd6aff","timestamp":"2025-08-01T07:35:00.030Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T07:35:00.036Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T07:35:00.065Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T07:35:00.066Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T07:36:00.029Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T07:37:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T07:38:00.023Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T07:39:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T07:40:00.023Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T07:40:00.027Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"9686c9d4-4fad-4363-b729-0e689e7b6e2a","timestamp":"2025-08-01T07:40:00.027Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T07:40:00.079Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T07:40:00.127Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T07:40:00.127Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T07:41:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T07:42:00.025Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T07:43:00.010Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T07:44:00.029Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T07:45:00.002Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T07:45:00.004Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"205b3f0d-1c87-4f22-b164-93f33e4b458b","timestamp":"2025-08-01T07:45:00.005Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T07:45:00.011Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T07:45:00.039Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T07:45:00.041Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T07:46:00.023Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T07:47:00.015Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T07:48:00.028Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T07:49:00.017Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T07:50:00.015Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T07:50:00.018Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"d27cd442-6cbe-494d-bf80-65d6b79926f0","timestamp":"2025-08-01T07:50:00.018Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T07:50:00.022Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T07:50:00.048Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T07:50:00.049Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T07:51:00.027Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T07:52:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T07:53:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T07:54:00.028Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T07:55:00.014Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T07:55:00.017Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"ba91dd04-3561-4bdb-a1c7-98bdd74004f1","timestamp":"2025-08-01T07:55:00.017Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T07:55:00.021Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T07:55:00.052Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T07:55:00.054Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T07:56:00.029Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T07:57:00.036Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T07:58:00.008Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T07:59:00.038Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T08:00:00.017Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T08:00:00.019Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"9173e5b2-d442-49e5-9611-0778df673e9e","timestamp":"2025-08-01T08:00:00.019Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T08:00:00.037Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T08:00:00.086Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T08:00:00.086Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T08:01:00.027Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T08:02:00.026Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T08:03:00.024Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T08:04:00.028Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T08:05:00.024Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T08:05:00.030Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"53a08472-17f1-40b0-af35-dcea92b8e1fa","timestamp":"2025-08-01T08:05:00.031Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T08:05:00.040Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T08:05:00.083Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T08:05:00.083Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T08:06:00.014Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T08:07:00.012Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T08:08:00.010Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T08:09:00.029Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T08:10:00.005Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T08:10:00.008Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"73488fef-b9e4-4275-a651-096fd9bcc135","timestamp":"2025-08-01T08:10:00.008Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T08:10:00.011Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T08:10:00.036Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T08:10:00.038Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T08:11:00.015Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T08:12:00.028Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T08:13:00.030Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T08:14:00.023Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T08:15:00.018Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T08:15:00.024Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"45c9f665-0ec3-49cc-809b-01e28e54eb78","timestamp":"2025-08-01T08:15:00.024Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T08:15:00.188Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T08:15:00.361Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T08:15:00.375Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T08:16:00.025Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T08:17:00.029Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T08:18:00.010Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T08:19:00.097Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T08:20:00.106Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T08:20:00.179Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"3a8cf853-2a36-4960-951e-459ddebcd196","timestamp":"2025-08-01T08:20:00.179Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T08:20:00.193Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T08:20:00.298Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T08:20:00.299Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T08:21:00.074Z"}
{"level":"info","message":"Commit Tag: $GIT_SHA","timestamp":"2025-08-01T08:24:34.554Z"}
{"level":"info","message":"Starting Jellyseerr version 2.7.2","timestamp":"2025-08-01T08:24:41.035Z"}
{"label":"Settings Migrator","level":"debug","message":"Checking migration '0001_migrate_hostname.js'...","timestamp":"2025-08-01T08:24:45.224Z"}
{"label":"Settings Migrator","level":"debug","message":"Checking migration '0002_migrate_apitokens.js'...","timestamp":"2025-08-01T08:24:45.230Z"}
{"label":"Settings Migrator","level":"debug","message":"Checking migration '0003_emby_media_server_type.js'...","timestamp":"2025-08-01T08:24:45.236Z"}
{"label":"Settings Migrator","level":"debug","message":"Checking migration '0004_migrate_region_setting.js'...","timestamp":"2025-08-01T08:24:45.240Z"}
{"label":"Settings Migrator","level":"debug","message":"Checking migration '0005_migrate_network_settings.js'...","timestamp":"2025-08-01T08:24:45.241Z"}
{"label":"Settings Migrator","level":"debug","message":"Checking migration '0006_remove_lunasea.js'...","timestamp":"2025-08-01T08:24:45.243Z"}
{"label":"Notifications","level":"info","message":"Registered notification agents","timestamp":"2025-08-01T08:24:45.253Z"}
{"label":"Jobs","level":"info","message":"Scheduled jobs loaded","timestamp":"2025-08-01T08:24:45.335Z"}
{"label":"Server","level":"info","message":"Server ready on port 5055","timestamp":"2025-08-01T08:24:46.096Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T08:25:00.016Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T08:25:00.124Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"d4102b4f-736b-499e-92f0-7191dbdd20d5","timestamp":"2025-08-01T08:25:00.132Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T08:25:00.162Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T08:25:01.642Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T08:25:01.643Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T08:26:00.015Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T08:27:00.014Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T08:28:00.007Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T08:29:00.013Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T08:30:00.012Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T08:30:00.017Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"2f6326bb-9464-441b-893d-ea6b556aa237","timestamp":"2025-08-01T08:30:00.018Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T08:30:00.050Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T08:30:00.129Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T08:30:00.130Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T08:31:00.013Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T08:32:00.004Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T08:33:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T08:34:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T08:35:00.012Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T08:35:00.022Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"cb8ff331-cb92-4e37-847e-0dc3bf1a08e0","timestamp":"2025-08-01T08:35:00.022Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T08:35:00.052Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T08:35:00.132Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T08:35:00.140Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T08:36:00.013Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T08:37:00.010Z"}
{"level":"info","message":"Commit Tag: $GIT_SHA","timestamp":"2025-08-01T08:38:11.722Z"}
{"level":"info","message":"Starting Jellyseerr version 2.7.2","timestamp":"2025-08-01T08:38:13.802Z"}
{"label":"Settings Migrator","level":"debug","message":"Checking migration '0001_migrate_hostname.js'...","timestamp":"2025-08-01T08:38:16.786Z"}
{"label":"Settings Migrator","level":"debug","message":"Checking migration '0002_migrate_apitokens.js'...","timestamp":"2025-08-01T08:38:16.791Z"}
{"label":"Settings Migrator","level":"debug","message":"Checking migration '0003_emby_media_server_type.js'...","timestamp":"2025-08-01T08:38:16.796Z"}
{"label":"Settings Migrator","level":"debug","message":"Checking migration '0004_migrate_region_setting.js'...","timestamp":"2025-08-01T08:38:16.800Z"}
{"label":"Settings Migrator","level":"debug","message":"Checking migration '0005_migrate_network_settings.js'...","timestamp":"2025-08-01T08:38:16.803Z"}
{"label":"Settings Migrator","level":"debug","message":"Checking migration '0006_remove_lunasea.js'...","timestamp":"2025-08-01T08:38:16.805Z"}
{"label":"Notifications","level":"info","message":"Registered notification agents","timestamp":"2025-08-01T08:38:16.811Z"}
{"label":"Jobs","level":"info","message":"Scheduled jobs loaded","timestamp":"2025-08-01T08:38:16.902Z"}
{"label":"Server","level":"info","message":"Server ready on port 5055","timestamp":"2025-08-01T08:38:17.426Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T08:39:00.009Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T08:40:00.042Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T08:40:00.083Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"d7621d1a-8fed-42d4-a83a-edbc9b3347e3","timestamp":"2025-08-01T08:40:00.090Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T08:40:00.174Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T08:40:00.543Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T08:40:00.551Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T08:41:00.015Z"}
{"label":"Download Tracker","level":"debug","message":"Found 6 item(s) in progress on Sonarr server: Sonarr","timestamp":"2025-08-01T08:41:00.628Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T08:42:00.013Z"}
{"label":"Download Tracker","level":"debug","message":"Found 6 item(s) in progress on Sonarr server: Sonarr","timestamp":"2025-08-01T08:42:00.194Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T08:43:00.015Z"}
{"label":"Download Tracker","level":"debug","message":"Found 6 item(s) in progress on Sonarr server: Sonarr","timestamp":"2025-08-01T08:43:00.200Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T08:44:00.017Z"}
{"label":"Download Tracker","level":"debug","message":"Found 6 item(s) in progress on Sonarr server: Sonarr","timestamp":"2025-08-01T08:44:17.591Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T08:45:00.020Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T08:45:00.047Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"f277c548-1a4b-4914-96c3-696e085fb52d","timestamp":"2025-08-01T08:45:00.050Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T08:45:00.070Z"}
{"label":"Download Tracker","level":"debug","message":"Found 6 item(s) in progress on Sonarr server: Sonarr","timestamp":"2025-08-01T08:45:00.860Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T08:45:01.438Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T08:45:01.439Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T08:46:00.091Z"}
{"label":"Download Tracker","level":"debug","message":"Found 6 item(s) in progress on Sonarr server: Sonarr","timestamp":"2025-08-01T08:46:01.875Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T08:47:00.016Z"}
{"label":"Download Tracker","level":"debug","message":"Found 6 item(s) in progress on Sonarr server: Sonarr","timestamp":"2025-08-01T08:47:01.789Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T08:48:00.028Z"}
{"label":"Download Tracker","level":"debug","message":"Found 6 item(s) in progress on Sonarr server: Sonarr","timestamp":"2025-08-01T08:48:03.869Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T08:49:00.626Z"}
{"label":"Download Tracker","level":"debug","message":"Found 6 item(s) in progress on Sonarr server: Sonarr","timestamp":"2025-08-01T08:49:01.039Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T08:50:00.028Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T08:50:00.046Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"ff85de0d-6019-4805-9946-8229f096efb4","timestamp":"2025-08-01T08:50:00.054Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T08:50:00.128Z"}
{"label":"Download Tracker","level":"debug","message":"Found 6 item(s) in progress on Sonarr server: Sonarr","timestamp":"2025-08-01T08:50:00.803Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T08:50:00.882Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T08:50:00.884Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T08:51:00.005Z"}
{"label":"Download Tracker","level":"debug","message":"Found 6 item(s) in progress on Sonarr server: Sonarr","timestamp":"2025-08-01T08:51:00.140Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T08:52:00.010Z"}
{"label":"Download Tracker","level":"debug","message":"Found 6 item(s) in progress on Sonarr server: Sonarr","timestamp":"2025-08-01T08:52:00.207Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T08:53:00.021Z"}
{"label":"Download Tracker","level":"debug","message":"Found 6 item(s) in progress on Sonarr server: Sonarr","timestamp":"2025-08-01T08:53:00.293Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T08:54:00.011Z"}
{"label":"Download Tracker","level":"debug","message":"Found 6 item(s) in progress on Sonarr server: Sonarr","timestamp":"2025-08-01T08:54:00.137Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T08:55:00.002Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T08:55:00.012Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"b1a3dfc5-ff0c-42a5-a8a0-c3d0a130c394","timestamp":"2025-08-01T08:55:00.012Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T08:55:00.092Z"}
{"label":"Download Tracker","level":"debug","message":"Found 6 item(s) in progress on Sonarr server: Sonarr","timestamp":"2025-08-01T08:55:00.196Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T08:55:00.615Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T08:55:00.626Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T08:56:00.018Z"}
{"label":"Download Tracker","level":"debug","message":"Found 6 item(s) in progress on Sonarr server: Sonarr","timestamp":"2025-08-01T08:56:00.208Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T08:57:00.028Z"}
{"label":"Download Tracker","level":"debug","message":"Found 6 item(s) in progress on Sonarr server: Sonarr","timestamp":"2025-08-01T08:57:00.544Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T08:58:00.025Z"}
{"label":"Download Tracker","level":"debug","message":"Found 6 item(s) in progress on Sonarr server: Sonarr","timestamp":"2025-08-01T08:58:00.796Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T08:59:00.006Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T09:00:00.010Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T09:00:00.013Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"9a382d80-c489-4455-b2dd-366c853e36ce","timestamp":"2025-08-01T09:00:00.014Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T09:00:00.021Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T09:00:00.035Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T09:00:00.036Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T09:01:00.005Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T09:02:00.004Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T09:03:00.007Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T09:04:00.007Z"}
{"level":"info","message":"Commit Tag: $GIT_SHA","timestamp":"2025-08-01T09:06:30.782Z"}
{"level":"info","message":"Starting Jellyseerr version 2.7.2","timestamp":"2025-08-01T09:06:32.941Z"}
{"label":"Settings Migrator","level":"debug","message":"Checking migration '0001_migrate_hostname.js'...","timestamp":"2025-08-01T09:06:36.495Z"}
{"label":"Settings Migrator","level":"debug","message":"Checking migration '0002_migrate_apitokens.js'...","timestamp":"2025-08-01T09:06:36.501Z"}
{"label":"Settings Migrator","level":"debug","message":"Checking migration '0003_emby_media_server_type.js'...","timestamp":"2025-08-01T09:06:36.509Z"}
{"label":"Settings Migrator","level":"debug","message":"Checking migration '0004_migrate_region_setting.js'...","timestamp":"2025-08-01T09:06:36.519Z"}
{"label":"Settings Migrator","level":"debug","message":"Checking migration '0005_migrate_network_settings.js'...","timestamp":"2025-08-01T09:06:36.523Z"}
{"label":"Settings Migrator","level":"debug","message":"Checking migration '0006_remove_lunasea.js'...","timestamp":"2025-08-01T09:06:36.524Z"}
{"label":"Notifications","level":"info","message":"Registered notification agents","timestamp":"2025-08-01T09:06:36.543Z"}
{"label":"Jobs","level":"info","message":"Scheduled jobs loaded","timestamp":"2025-08-01T09:06:36.678Z"}
{"label":"Server","level":"info","message":"Server ready on port 5055","timestamp":"2025-08-01T09:06:37.442Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T09:07:00.010Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T09:08:00.021Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T09:09:00.025Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T09:10:00.022Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T09:10:00.026Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"c736b963-154a-400d-a646-7c8ef2c2226b","timestamp":"2025-08-01T09:10:00.027Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T09:10:00.044Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T09:10:00.125Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T09:10:00.126Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T09:11:00.017Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T09:12:00.020Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T09:13:00.028Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T09:14:00.009Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T09:15:00.022Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T09:15:00.033Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"c6cfbe51-b7a3-4d3e-bf86-c76fea929ae3","timestamp":"2025-08-01T09:15:00.034Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T09:15:00.066Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T09:15:00.221Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T09:15:00.222Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T09:16:00.004Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T09:17:00.024Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T09:18:24.755Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T09:19:00.007Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T09:20:00.021Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T09:20:00.193Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"794f2a65-52d9-481f-ae48-2e540b4f48f6","timestamp":"2025-08-01T09:20:00.194Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T09:20:01.125Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T09:20:03.071Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T09:20:03.072Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T09:21:00.023Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T09:22:00.013Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T09:23:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T09:24:00.025Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T09:25:00.031Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T09:25:00.047Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"20ee709f-5138-498b-868d-89c446f83b64","timestamp":"2025-08-01T09:25:00.048Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T09:25:00.057Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T09:25:00.106Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T09:25:00.113Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T09:26:00.023Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T09:27:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T09:28:00.191Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T09:29:00.023Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T09:30:00.004Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T09:30:00.007Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"8dbeb427-2772-4f5a-be03-17cbddf19d8c","timestamp":"2025-08-01T09:30:00.007Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T09:30:00.054Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T09:30:00.067Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T09:30:00.068Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T09:31:00.006Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T09:32:03.597Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T09:33:17.233Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T09:34:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T09:35:00.660Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T09:35:00.662Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"3d03c3b6-49d7-4ee8-a5c1-7b2edcd3e646","timestamp":"2025-08-01T09:35:00.667Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T09:35:01.492Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T09:35:01.901Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T09:35:01.903Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T09:36:00.124Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T09:38:22.527Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T09:38:28.271Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T09:39:01.229Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T09:40:02.119Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T09:40:15.795Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"6a4394fe-55c9-4846-b3bf-6ad2a657bf02","timestamp":"2025-08-01T09:40:16.026Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T09:40:18.199Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T09:40:18.744Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T09:40:18.745Z"}
{"level":"info","message":"Commit Tag: $GIT_SHA","timestamp":"2025-08-01T09:45:33.955Z"}
{"level":"info","message":"Starting Jellyseerr version 2.7.2","timestamp":"2025-08-01T09:45:36.930Z"}
{"label":"Settings Migrator","level":"debug","message":"Checking migration '0001_migrate_hostname.js'...","timestamp":"2025-08-01T09:45:47.891Z"}
{"label":"Settings Migrator","level":"debug","message":"Checking migration '0002_migrate_apitokens.js'...","timestamp":"2025-08-01T09:45:47.954Z"}
{"label":"Settings Migrator","level":"debug","message":"Checking migration '0003_emby_media_server_type.js'...","timestamp":"2025-08-01T09:45:48.043Z"}
{"label":"Settings Migrator","level":"debug","message":"Checking migration '0004_migrate_region_setting.js'...","timestamp":"2025-08-01T09:45:48.053Z"}
{"label":"Settings Migrator","level":"debug","message":"Checking migration '0005_migrate_network_settings.js'...","timestamp":"2025-08-01T09:45:48.058Z"}
{"label":"Settings Migrator","level":"debug","message":"Checking migration '0006_remove_lunasea.js'...","timestamp":"2025-08-01T09:45:48.062Z"}
{"label":"Notifications","level":"info","message":"Registered notification agents","timestamp":"2025-08-01T09:45:48.095Z"}
{"label":"Jobs","level":"info","message":"Scheduled jobs loaded","timestamp":"2025-08-01T09:45:48.413Z"}
{"label":"Server","level":"info","message":"Server ready on port 5055","timestamp":"2025-08-01T09:45:49.652Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T09:46:00.024Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T09:47:00.011Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T09:48:00.145Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T09:49:00.008Z"}
{"errorMessage":"Request failed with status code 404","label":"Image Cache","level":"debug","message":"Something went wrong caching image.","timestamp":"2025-08-01T09:49:37.951Z"}
{"errorMessage":"Request failed with status code 404","label":"Image Cache","level":"debug","message":"Something went wrong caching image.","timestamp":"2025-08-01T09:49:53.817Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T09:50:00.003Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T09:50:00.008Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"33b23371-b1cb-4fb1-8436-96c1409eae2b","timestamp":"2025-08-01T09:50:00.009Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T09:50:00.021Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T09:50:00.038Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T09:50:00.038Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T09:51:00.008Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T09:52:00.010Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T09:53:00.011Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T09:54:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T09:55:00.011Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T09:55:00.015Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"bcdee4d6-9b4c-4051-be72-7150c29955b6","timestamp":"2025-08-01T09:55:00.016Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T09:55:00.030Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T09:55:00.070Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T09:55:00.072Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T09:56:00.011Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T09:57:00.012Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T09:58:00.011Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T09:59:00.009Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T10:00:00.103Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T10:00:00.239Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"cb4a3bc6-f5c9-43f9-b582-3a8759271b58","timestamp":"2025-08-01T10:00:00.240Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T10:00:03.853Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T10:00:19.806Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T10:00:19.806Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T10:01:00.477Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T10:02:03.657Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T10:04:10.037Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T10:04:36.061Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T10:05:00.013Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T10:05:00.677Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"f85031e7-27ad-4506-9d96-82b249e43fa4","timestamp":"2025-08-01T10:05:00.678Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T10:05:09.822Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T10:05:16.986Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T10:05:16.987Z"}
{"level":"info","message":"Commit Tag: $GIT_SHA","timestamp":"2025-08-01T10:07:05.162Z"}
{"level":"info","message":"Starting Jellyseerr version 2.7.2","timestamp":"2025-08-01T10:07:08.375Z"}
{"label":"Settings Migrator","level":"debug","message":"Checking migration '0001_migrate_hostname.js'...","timestamp":"2025-08-01T10:07:13.639Z"}
{"label":"Settings Migrator","level":"debug","message":"Checking migration '0002_migrate_apitokens.js'...","timestamp":"2025-08-01T10:07:13.648Z"}
{"label":"Settings Migrator","level":"debug","message":"Checking migration '0003_emby_media_server_type.js'...","timestamp":"2025-08-01T10:07:13.673Z"}
{"label":"Settings Migrator","level":"debug","message":"Checking migration '0004_migrate_region_setting.js'...","timestamp":"2025-08-01T10:07:13.676Z"}
{"label":"Settings Migrator","level":"debug","message":"Checking migration '0005_migrate_network_settings.js'...","timestamp":"2025-08-01T10:07:13.679Z"}
{"label":"Settings Migrator","level":"debug","message":"Checking migration '0006_remove_lunasea.js'...","timestamp":"2025-08-01T10:07:13.683Z"}
{"label":"Notifications","level":"info","message":"Registered notification agents","timestamp":"2025-08-01T10:07:13.710Z"}
{"label":"Jobs","level":"info","message":"Scheduled jobs loaded","timestamp":"2025-08-01T10:07:14.016Z"}
{"label":"Server","level":"info","message":"Server ready on port 5055","timestamp":"2025-08-01T10:07:15.022Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T10:08:00.006Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T10:09:00.005Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T10:10:00.007Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T10:10:00.017Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"c0f71ab6-6973-4980-abc0-739868370a96","timestamp":"2025-08-01T10:10:00.018Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T10:10:00.044Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T10:10:00.076Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T10:10:00.077Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T10:11:00.004Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T10:12:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T10:13:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T10:14:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T10:15:00.004Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T10:15:00.010Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"02d9d302-e6e3-4fe0-a754-f093b128ed1b","timestamp":"2025-08-01T10:15:00.011Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T10:15:00.018Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T10:15:00.039Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T10:15:00.043Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T10:16:00.003Z"}
{"errorMessage":"Request failed with status code 404","label":"Image Cache","level":"debug","message":"Something went wrong caching image.","timestamp":"2025-08-01T10:16:48.880Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T10:17:00.004Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T10:18:00.006Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T10:19:00.011Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T10:20:00.003Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T10:20:00.009Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"6d134a6c-cbf6-4c43-87c0-4fbaccb8e712","timestamp":"2025-08-01T10:20:00.009Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T10:20:00.021Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T10:20:00.039Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T10:20:00.042Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T10:21:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T10:22:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T10:23:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T10:24:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T10:25:00.004Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T10:25:00.016Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"5b13ed96-a40d-46ee-8c51-5e9dc99fed37","timestamp":"2025-08-01T10:25:00.016Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T10:25:00.049Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T10:25:00.070Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T10:25:00.073Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T10:26:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T10:27:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T10:28:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T10:29:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T10:30:00.003Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T10:30:00.007Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"f2999932-374e-4254-8486-5bf6f8b07598","timestamp":"2025-08-01T10:30:00.008Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T10:30:00.015Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T10:30:00.029Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T10:30:00.029Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T10:31:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T10:32:00.004Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T10:33:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T10:34:00.014Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T10:35:00.002Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T10:35:00.009Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"8441c305-d9e2-4cb2-b41e-f2e01ac61099","timestamp":"2025-08-01T10:35:00.010Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T10:35:00.016Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T10:35:00.021Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T10:35:00.022Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T10:36:00.004Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T10:37:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T10:38:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T10:39:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T10:40:00.002Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T10:40:00.007Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"fcdabd9c-c1cd-4b6d-9feb-47cb5a292b35","timestamp":"2025-08-01T10:40:00.008Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T10:40:00.013Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T10:40:00.021Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T10:40:00.022Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T10:41:00.005Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T10:42:00.004Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T10:43:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T10:44:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T10:45:00.002Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T10:45:00.018Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"b02bcc70-fd02-4225-b232-b3a63a52170d","timestamp":"2025-08-01T10:45:00.020Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T10:45:00.043Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T10:45:00.060Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T10:45:00.061Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T10:46:00.289Z"}
{"level":"info","message":"Commit Tag: $GIT_SHA","timestamp":"2025-08-01T10:49:12.929Z"}
{"level":"info","message":"Starting Jellyseerr version 2.7.2","timestamp":"2025-08-01T10:49:15.498Z"}
{"label":"Settings Migrator","level":"debug","message":"Checking migration '0001_migrate_hostname.js'...","timestamp":"2025-08-01T10:49:20.181Z"}
{"label":"Settings Migrator","level":"debug","message":"Checking migration '0002_migrate_apitokens.js'...","timestamp":"2025-08-01T10:49:20.190Z"}
{"label":"Settings Migrator","level":"debug","message":"Checking migration '0003_emby_media_server_type.js'...","timestamp":"2025-08-01T10:49:20.202Z"}
{"label":"Settings Migrator","level":"debug","message":"Checking migration '0004_migrate_region_setting.js'...","timestamp":"2025-08-01T10:49:20.211Z"}
{"label":"Settings Migrator","level":"debug","message":"Checking migration '0005_migrate_network_settings.js'...","timestamp":"2025-08-01T10:49:20.215Z"}
{"label":"Settings Migrator","level":"debug","message":"Checking migration '0006_remove_lunasea.js'...","timestamp":"2025-08-01T10:49:20.219Z"}
{"label":"Notifications","level":"info","message":"Registered notification agents","timestamp":"2025-08-01T10:49:20.234Z"}
{"label":"Jobs","level":"info","message":"Scheduled jobs loaded","timestamp":"2025-08-01T10:49:20.512Z"}
{"label":"Server","level":"info","message":"Server ready on port 5055","timestamp":"2025-08-01T10:49:21.446Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T10:50:00.008Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T10:50:00.088Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"74ea8ed2-e576-4d3c-8ae0-3db17fdb5bd6","timestamp":"2025-08-01T10:50:00.090Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T10:50:00.115Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T10:50:00.190Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T10:50:00.193Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T10:51:00.034Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T10:52:00.009Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T10:53:00.010Z"}
{"level":"info","message":"Commit Tag: $GIT_SHA","timestamp":"2025-08-01T10:55:05.412Z"}
{"level":"info","message":"Starting Jellyseerr version 2.7.2","timestamp":"2025-08-01T10:55:07.553Z"}
{"label":"Settings Migrator","level":"debug","message":"Checking migration '0001_migrate_hostname.js'...","timestamp":"2025-08-01T10:55:12.342Z"}
{"label":"Settings Migrator","level":"debug","message":"Checking migration '0002_migrate_apitokens.js'...","timestamp":"2025-08-01T10:55:12.363Z"}
{"label":"Settings Migrator","level":"debug","message":"Checking migration '0003_emby_media_server_type.js'...","timestamp":"2025-08-01T10:55:12.393Z"}
{"label":"Settings Migrator","level":"debug","message":"Checking migration '0004_migrate_region_setting.js'...","timestamp":"2025-08-01T10:55:12.403Z"}
{"label":"Settings Migrator","level":"debug","message":"Checking migration '0005_migrate_network_settings.js'...","timestamp":"2025-08-01T10:55:12.407Z"}
{"label":"Settings Migrator","level":"debug","message":"Checking migration '0006_remove_lunasea.js'...","timestamp":"2025-08-01T10:55:12.417Z"}
{"label":"Notifications","level":"info","message":"Registered notification agents","timestamp":"2025-08-01T10:55:12.461Z"}
{"label":"Jobs","level":"info","message":"Scheduled jobs loaded","timestamp":"2025-08-01T10:55:12.635Z"}
{"label":"Server","level":"info","message":"Server ready on port 5055","timestamp":"2025-08-01T10:55:13.957Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T10:56:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T10:57:00.009Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T10:58:00.260Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T10:59:00.004Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T11:00:00.003Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T11:00:00.010Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"b78ef9a3-f294-45c1-9ea4-b781a2df66c0","timestamp":"2025-08-01T11:00:00.029Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T11:00:00.139Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T11:00:00.684Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T11:00:00.705Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T11:01:00.010Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T11:02:00.008Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T11:03:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T11:04:00.012Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T11:05:00.015Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T11:05:00.021Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"9213867f-3df5-4e8d-bd24-75b0e8f00f0f","timestamp":"2025-08-01T11:05:00.021Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T11:05:00.040Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T11:05:00.048Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T11:05:00.049Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T11:06:00.022Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T11:07:00.016Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T11:08:00.034Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T11:09:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T11:10:00.014Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T11:10:00.027Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"0fbfdd62-118a-4030-b8e1-d70b76ab980b","timestamp":"2025-08-01T11:10:00.027Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T11:10:00.036Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T11:10:00.085Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T11:10:00.085Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T11:11:00.010Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T11:12:00.007Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T11:13:00.043Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T11:14:00.015Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T11:15:00.014Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T11:15:00.017Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"64d9d479-ebf2-4a6b-8e5f-ddb3f3736d1b","timestamp":"2025-08-01T11:15:00.017Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T11:15:00.029Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T11:15:00.070Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T11:15:00.072Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T11:16:00.014Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T11:17:00.016Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T11:18:00.011Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T11:19:00.015Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T11:20:00.014Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T11:20:00.016Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"cc2a9864-9ca3-4755-beca-149b12ac023b","timestamp":"2025-08-01T11:20:00.017Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T11:20:00.023Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T11:20:00.031Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T11:20:00.035Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T11:21:00.013Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T11:22:00.011Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T11:23:00.006Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T11:24:00.014Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T11:25:00.016Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T11:25:00.024Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"00f909fa-4029-4776-b46a-c9047f16a0b3","timestamp":"2025-08-01T11:25:00.024Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T11:25:00.031Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T11:25:00.073Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T11:25:00.076Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T11:26:00.015Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T11:27:00.014Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T11:28:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T11:29:12.954Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T11:31:14.688Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T11:31:20.481Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"0816e70e-5d76-4a16-a018-186f533622e8","timestamp":"2025-08-01T11:31:20.812Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T11:31:23.834Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T11:31:28.715Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T11:31:33.825Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T11:31:33.826Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T11:32:01.092Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T11:33:00.410Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T11:34:00.005Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T11:35:00.113Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T11:35:00.115Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"3e625802-888c-4b0d-bcca-92d21d0ba16a","timestamp":"2025-08-01T11:35:00.115Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T11:35:00.165Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T11:35:00.347Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T11:35:00.347Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T11:36:00.010Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T11:37:00.004Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T11:38:00.375Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T11:39:00.016Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T11:40:00.001Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T11:40:00.005Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"2dd54d11-446c-40ec-892f-597db01fa6be","timestamp":"2025-08-01T11:40:00.005Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T11:40:00.015Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T11:40:00.055Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T11:40:00.056Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T11:41:00.027Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T11:42:00.015Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T11:43:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T11:44:00.011Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T11:45:00.016Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T11:45:00.018Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"02411eb6-9362-4acb-a7f0-274ab22ebebb","timestamp":"2025-08-01T11:45:00.019Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T11:45:00.026Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T11:45:00.106Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T11:45:00.107Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T11:46:00.013Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T11:47:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T11:48:00.022Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T11:49:00.017Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T11:50:00.010Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T11:50:00.015Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"f6067efa-d79b-4fd4-8e17-52c85f550bbc","timestamp":"2025-08-01T11:50:00.016Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T11:50:00.037Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T11:50:00.055Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T11:50:00.055Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T11:51:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T11:52:00.014Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T11:53:00.004Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T11:54:00.014Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T11:55:00.003Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T11:55:00.006Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"843b1fd2-3226-41c3-a81f-937efee6e41e","timestamp":"2025-08-01T11:55:00.006Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T11:55:00.011Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T11:55:00.054Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T11:55:00.054Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T11:56:00.042Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T11:57:00.014Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T11:58:00.015Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T11:59:00.015Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T12:00:00.012Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T12:00:00.015Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"33bb720f-fc9f-42bd-a264-acf85a27ebe0","timestamp":"2025-08-01T12:00:00.016Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T12:00:00.020Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T12:00:00.026Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T12:00:00.026Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T12:01:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T12:02:00.015Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T12:03:00.014Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T12:04:00.014Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T12:05:00.013Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T12:05:00.015Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"5dc73106-12ee-4f71-ad8e-7f7dc9fd6f56","timestamp":"2025-08-01T12:05:00.015Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T12:05:00.021Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T12:05:00.057Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T12:05:00.058Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T12:06:00.011Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T12:07:00.012Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T12:08:00.016Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T12:09:00.015Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T12:10:00.011Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T12:10:00.014Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"5edf1a09-328b-4271-9956-e196695fdb3e","timestamp":"2025-08-01T12:10:00.014Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T12:10:00.021Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T12:10:00.026Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T12:10:00.027Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T12:11:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T12:12:00.013Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T12:13:00.012Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T12:14:00.010Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T12:15:00.013Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T12:15:00.014Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"35b78b05-f80b-493d-b35b-803468e20c52","timestamp":"2025-08-01T12:15:00.015Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T12:15:00.062Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T12:15:00.164Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T12:15:00.164Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T12:16:00.013Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T12:17:00.011Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T12:18:00.014Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T12:19:00.011Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T12:20:00.015Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T12:20:00.018Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"3dc122d9-c20e-4539-9900-a5b832993768","timestamp":"2025-08-01T12:20:00.234Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T12:20:00.411Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T12:20:00.691Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T12:20:00.691Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T12:21:00.011Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T12:22:00.013Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T12:23:00.009Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T12:24:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T12:25:00.035Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T12:25:00.037Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"51beaa30-c4e0-4b36-8ad6-0cef488ff0c0","timestamp":"2025-08-01T12:25:00.037Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T12:25:00.099Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T12:25:00.441Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T12:25:00.442Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T12:26:00.012Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T12:27:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T12:28:00.039Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T12:29:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T12:30:00.012Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T12:30:00.068Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"5d472af5-54ba-4b01-8389-b5bc5e1df676","timestamp":"2025-08-01T12:30:00.068Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T12:30:00.101Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T12:30:00.122Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T12:30:00.123Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T12:31:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T12:32:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T12:33:00.013Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T12:34:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T12:35:00.010Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T12:35:00.012Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"05b250bb-da61-4462-882b-a7b0ad56ba77","timestamp":"2025-08-01T12:35:00.013Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T12:35:00.141Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T12:35:00.176Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T12:35:00.178Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T12:36:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T12:37:00.047Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T12:38:00.006Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T12:39:00.023Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T12:40:00.013Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T12:40:00.017Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"08cd4d6a-36f7-4db4-8036-0af938174b7d","timestamp":"2025-08-01T12:40:00.017Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T12:40:00.028Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T12:40:00.041Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T12:40:00.042Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T12:41:00.009Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T12:42:00.012Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T12:43:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T12:44:00.010Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T12:45:00.007Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T12:45:00.011Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"093e2d0f-7080-4213-bb02-a618e63358f5","timestamp":"2025-08-01T12:45:00.011Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T12:45:00.018Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T12:45:00.069Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T12:45:00.069Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T12:46:00.014Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T12:47:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T12:48:00.011Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T12:49:00.013Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T12:50:00.013Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T12:50:00.015Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"bb0ef07e-bed8-4753-ac49-7cbc6d8c6480","timestamp":"2025-08-01T12:50:00.015Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T12:50:00.021Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T12:50:00.024Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T12:50:00.024Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T12:51:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T12:52:00.009Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T12:53:00.012Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T12:54:00.011Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T12:55:00.012Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T12:55:00.015Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"6a12b27e-f07b-4f73-9807-aa23a32e8ca3","timestamp":"2025-08-01T12:55:00.015Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T12:55:00.019Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T12:55:00.135Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T12:55:00.136Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T12:56:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T12:57:00.009Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T12:58:00.011Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T12:59:00.009Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T13:00:00.012Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T13:00:00.014Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"486dd625-7ccc-4e09-a43d-345e59542691","timestamp":"2025-08-01T13:00:00.015Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T13:00:00.019Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T13:00:00.024Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T13:00:00.024Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T13:01:00.014Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T13:02:00.016Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T13:03:00.012Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T13:04:00.017Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T13:05:00.006Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T13:05:00.009Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"135103ea-0d67-4f69-8072-c5b8404252d0","timestamp":"2025-08-01T13:05:00.009Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T13:05:00.013Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T13:05:00.020Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T13:05:00.020Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T13:06:00.004Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T13:07:00.007Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T13:08:00.012Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T13:09:00.010Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T13:10:00.012Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T13:10:00.015Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"359b24bf-627a-4a6e-a184-193f081b3147","timestamp":"2025-08-01T13:10:00.015Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T13:10:00.020Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T13:10:00.026Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T13:10:00.027Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T13:11:00.006Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T13:12:00.012Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T13:13:00.010Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T13:14:00.009Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T13:15:00.002Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T13:15:00.004Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"ab4f76eb-b645-4231-aaae-cb6b76273918","timestamp":"2025-08-01T13:15:00.004Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T13:15:00.009Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T13:15:00.013Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T13:15:00.013Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T13:16:00.013Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T13:17:00.012Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T13:18:00.011Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T13:19:00.012Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T13:20:00.013Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T13:20:00.016Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"4069476b-84c4-41d4-97a9-67c5ed9a22f5","timestamp":"2025-08-01T13:20:00.016Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T13:20:00.021Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T13:20:00.027Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T13:20:00.027Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T13:21:00.011Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T13:22:00.008Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T13:23:00.013Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T13:24:00.049Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T13:25:00.010Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T13:25:00.013Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"076813e2-64d0-4d64-8b71-28218eafc7ed","timestamp":"2025-08-01T13:25:00.013Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T13:25:00.018Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T13:25:00.024Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T13:25:00.024Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T13:26:00.008Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T13:27:00.013Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T13:28:00.012Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T13:29:00.095Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T13:30:00.015Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T13:30:00.018Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"2d027418-b60a-4bb0-aa9e-e67e4a8ce320","timestamp":"2025-08-01T13:30:00.018Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T13:30:00.151Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T13:30:00.353Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T13:30:00.354Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T13:31:00.012Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T13:32:00.011Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T13:33:00.008Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T13:34:00.108Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T13:35:00.271Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T13:35:00.453Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"dee020c6-5b3a-4bca-9f41-3eca9192819c","timestamp":"2025-08-01T13:35:00.453Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T13:35:00.472Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T13:35:00.502Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T13:35:00.504Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T13:36:00.042Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T13:37:00.007Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T13:38:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T13:39:00.004Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T13:40:00.011Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T13:40:00.030Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"90aaecd2-119d-4f3b-9294-e51205b9e7ab","timestamp":"2025-08-01T13:40:00.030Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T13:40:00.034Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T13:40:00.038Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T13:40:00.038Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T13:41:00.031Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T13:42:00.052Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T13:43:00.014Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T13:44:00.012Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T13:45:00.007Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T13:45:00.033Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"f85e9ad8-00a4-4998-9fa4-684a352680c6","timestamp":"2025-08-01T13:45:00.033Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T13:45:00.042Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T13:45:00.048Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T13:45:00.049Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T13:46:00.008Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T13:47:00.011Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T13:48:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T13:49:00.027Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T13:50:00.012Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T13:50:00.014Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"7a5221ea-0ec8-43d1-8753-cb72a80f86dc","timestamp":"2025-08-01T13:50:00.015Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T13:50:00.020Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T13:50:00.024Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T13:50:00.024Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T13:51:00.011Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T13:52:00.009Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T13:53:00.009Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T13:54:00.011Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T13:55:00.010Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T13:55:00.013Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"36244c2a-1eab-4202-9427-216673cd6232","timestamp":"2025-08-01T13:55:00.013Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T13:55:00.018Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T13:55:00.026Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T13:55:00.027Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T13:56:00.011Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T13:57:00.069Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T13:58:00.176Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T13:59:00.008Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T14:00:00.002Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T14:00:00.006Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"f8441b09-1f53-4a4c-937c-e29fe81080a1","timestamp":"2025-08-01T14:00:00.006Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T14:00:00.013Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T14:00:00.020Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T14:00:00.020Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T14:01:00.010Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T14:02:00.015Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T14:03:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T14:04:00.011Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T14:05:00.010Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T14:05:00.012Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"8a49ccf3-c0a0-47f8-bb48-bda6c30981cc","timestamp":"2025-08-01T14:05:00.012Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T14:05:00.018Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T14:05:00.023Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T14:05:00.023Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T14:06:00.010Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T14:07:00.010Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T14:08:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T14:09:00.012Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T14:10:00.011Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T14:10:00.013Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"8a1c1fb2-8cbb-42a3-a164-2b69d496045b","timestamp":"2025-08-01T14:10:00.014Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T14:10:00.021Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T14:10:00.027Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T14:10:00.027Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T14:11:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T14:12:00.010Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T14:13:00.011Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T14:14:00.010Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T14:15:00.008Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T14:15:00.032Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"9a66916b-648a-45d2-9958-11147a5d5b45","timestamp":"2025-08-01T14:15:00.032Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T14:15:00.037Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T14:15:00.044Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T14:15:00.045Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T14:16:00.010Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T14:17:00.010Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T14:18:00.010Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T14:19:00.011Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T14:20:00.010Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T14:20:00.012Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"86b34e45-fd84-466c-8258-717b891d6d67","timestamp":"2025-08-01T14:20:00.013Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T14:20:00.019Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T14:20:00.027Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T14:20:00.028Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T14:21:00.009Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T14:22:00.009Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T14:23:00.011Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T14:24:00.010Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T14:25:00.010Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T14:25:00.012Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"6773e3fb-9caf-41fc-b366-0570c5f6b9b5","timestamp":"2025-08-01T14:25:00.012Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T14:25:00.017Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T14:25:00.024Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T14:25:00.024Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T14:26:00.011Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T14:27:00.009Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T14:28:00.009Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T14:29:00.011Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T14:30:00.010Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T14:30:00.053Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"8d5fef39-aa60-4e55-b011-0592fc1054da","timestamp":"2025-08-01T14:30:00.053Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T14:30:00.060Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T14:30:00.066Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T14:30:00.066Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T14:31:00.008Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T14:32:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T14:33:00.011Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T14:34:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T14:35:00.009Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T14:35:00.012Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"1831eade-6c8a-4868-b008-28795aaca03d","timestamp":"2025-08-01T14:35:00.013Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T14:35:00.018Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T14:35:00.024Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T14:35:00.024Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T14:36:00.010Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T14:37:00.009Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T14:38:00.009Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T14:39:00.009Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T14:40:00.009Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T14:40:00.011Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"ee351811-a3bd-47bb-a73a-65ebdd1adca9","timestamp":"2025-08-01T14:40:00.011Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T14:40:00.016Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T14:40:00.022Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T14:40:00.022Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T14:41:00.010Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T14:42:00.005Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T14:43:00.005Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T14:44:00.011Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T14:45:00.056Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T14:45:00.059Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"96dfe310-9c44-494c-8607-f849b64179dd","timestamp":"2025-08-01T14:45:00.059Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T14:45:00.063Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T14:45:00.070Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T14:45:00.070Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T14:46:00.009Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T14:47:00.008Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T14:48:00.105Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T14:49:00.009Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T14:50:00.001Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T14:50:00.004Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"99195f86-446e-4f43-b45e-3503b1728f55","timestamp":"2025-08-01T14:50:00.004Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T14:50:00.010Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T14:50:00.015Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T14:50:00.015Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T14:51:00.004Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T14:52:00.010Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T14:53:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T14:54:00.009Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T14:55:00.011Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T14:55:00.016Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"f24ba2f2-6403-47fc-a9a1-8df1ea762449","timestamp":"2025-08-01T14:55:00.017Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T14:55:00.058Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T14:55:00.075Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T14:55:00.075Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T14:56:00.009Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T14:57:00.008Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T14:58:00.010Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T14:59:00.010Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T15:00:00.006Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T15:00:00.009Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"919dc7aa-3de9-47bd-a43b-19d6237ff3ff","timestamp":"2025-08-01T15:00:00.009Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T15:00:00.013Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T15:00:00.018Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T15:00:00.018Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T15:02:23.413Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T15:03:45.887Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T15:03:45.903Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T15:04:03.918Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T15:05:00.327Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T15:05:01.841Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"21813470-3e89-4694-9533-243ea3f4992b","timestamp":"2025-08-01T15:05:03.394Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T15:05:20.702Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T15:06:00.017Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T15:06:30.285Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T15:06:30.286Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T15:07:00.138Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T15:08:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T15:09:00.035Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T15:10:00.003Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T15:10:00.005Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"a62dab29-1a8b-46c1-b59a-6e2086c72a1a","timestamp":"2025-08-01T15:10:00.005Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T15:10:00.015Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T15:10:00.022Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T15:10:00.023Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T15:11:00.014Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T15:12:00.012Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T15:13:00.010Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T15:14:00.010Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T15:15:00.011Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T15:15:00.016Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"92354da7-56af-47a2-98ad-fe4a4b6c3172","timestamp":"2025-08-01T15:15:00.016Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T15:15:00.020Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T15:15:00.083Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T15:15:00.103Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T15:16:00.009Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T15:17:00.010Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T15:18:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T15:19:00.009Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T15:20:00.011Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T15:20:00.016Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"a6d32a3c-d5bc-40b2-8526-d886a52e67c5","timestamp":"2025-08-01T15:20:00.016Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T15:20:00.022Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T15:20:00.028Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T15:20:00.029Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T15:21:00.012Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T15:22:00.010Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T15:23:00.007Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T15:24:00.009Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T15:25:00.012Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T15:25:00.014Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"43abc1c6-90b1-4570-8849-c89f06a3c3d7","timestamp":"2025-08-01T15:25:00.014Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T15:25:00.022Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T15:25:00.027Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T15:25:00.027Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T15:26:00.010Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T15:27:00.005Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T15:28:00.005Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T15:29:00.017Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T15:30:00.068Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T15:30:00.073Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"a2a92105-0bff-42c3-8093-8727b862b5f5","timestamp":"2025-08-01T15:30:00.073Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T15:30:00.077Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T15:30:00.098Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T15:30:00.098Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T15:31:00.008Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T15:32:00.008Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T15:33:00.012Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T15:34:00.014Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T15:35:00.006Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T15:35:00.037Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"480d19b7-86c1-4ab6-95ce-c33c15c96c55","timestamp":"2025-08-01T15:35:00.037Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T15:35:00.041Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T15:35:00.046Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T15:35:00.046Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T15:36:00.007Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T15:37:00.031Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T15:38:00.010Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T15:39:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T15:40:00.009Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T15:40:00.012Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"642ba8b6-63e4-4e24-89df-002a34ab0119","timestamp":"2025-08-01T15:40:00.012Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T15:40:00.092Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T15:40:00.106Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T15:40:00.106Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T15:41:00.009Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T15:42:00.008Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T15:43:00.007Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T15:44:00.009Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T15:45:00.004Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T15:45:00.006Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"392e9031-8c57-4ce5-b503-afe7775d0565","timestamp":"2025-08-01T15:45:00.007Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T15:45:00.013Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T15:45:00.019Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T15:45:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T15:46:00.047Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T15:47:00.006Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T15:48:00.009Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T15:49:00.008Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T15:50:00.001Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T15:50:00.005Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"662700f9-c477-4661-b59f-4ade7d83705f","timestamp":"2025-08-01T15:50:00.007Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T15:50:00.015Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T15:50:00.099Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T15:50:00.100Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T15:51:00.084Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T15:52:00.149Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T15:53:00.623Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T15:54:00.024Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T15:55:08.658Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T15:55:14.461Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"57b7acad-bb5b-4623-a950-e6deac639387","timestamp":"2025-08-01T15:55:14.461Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T15:55:14.606Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T15:55:44.906Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T15:55:45.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T15:56:00.016Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T15:57:00.031Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T15:58:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T15:59:00.051Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T16:00:00.003Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T16:00:00.071Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"5e4320ce-abed-4934-b0bb-d13c390ceb7b","timestamp":"2025-08-01T16:00:00.071Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T16:00:00.217Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T16:00:00.295Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T16:00:00.296Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T16:01:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T16:02:00.008Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T16:03:00.119Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T16:04:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T16:05:00.004Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T16:05:00.007Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"b5d829af-b114-4fc1-9629-1e50e586bcc8","timestamp":"2025-08-01T16:05:00.007Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T16:05:00.012Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T16:05:00.018Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T16:05:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T16:06:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T16:07:00.039Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T16:08:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T16:09:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T16:10:00.005Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T16:10:00.008Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"6a033c1c-69dc-4af9-a46e-d4972050437f","timestamp":"2025-08-01T16:10:00.008Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T16:10:00.013Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T16:10:00.016Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T16:10:00.017Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T16:11:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T16:12:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T16:13:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T16:14:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T16:15:00.007Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T16:15:00.010Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"6c793bc9-3c5f-40e8-a150-4bbda515f9f6","timestamp":"2025-08-01T16:15:00.011Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T16:15:00.016Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T16:15:00.022Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T16:15:00.022Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T16:16:00.006Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T16:17:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T16:18:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T16:19:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T16:20:00.006Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-08-01T16:20:00.007Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"b9af10f5-619c-4b39-9690-7e4d0379a41c","timestamp":"2025-08-01T16:20:00.008Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-08-01T16:20:00.012Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-08-01T16:20:00.018Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-08-01T16:20:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-08-01T16:21:00.007Z"}
