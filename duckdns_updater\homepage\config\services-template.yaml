# Homepage Services Template - Plantilla de Servicios con Variables de Entorno
# Este archivo usa variables de entorno para las API keys
# Copia este archivo como services.yaml y configura las variables en api-keys.env

# === SERVICIOS DE MEDIA ===
- Media:
    - Jellyfin:
        icon: "jellyfin.png"
        href: "{{HOMEPAGE_VAR_JELLYFIN_DOMAIN}}"
        description: "Servidor multimedia personal"
        server: "host.docker.internal"
        container: "jellyfin"
        widget:
          type: "jellyfin"
          url: "{{HOMEPAGE_VAR_JELLYFIN_URL}}"
          key: "{{HOMEPAGE_VAR_JELLYFIN_KEY}}"
          enableBlocks: true
          enableNowPlaying: true
          enableUser: true
          showEpisodeNumber: true
          expandOneStreamToTwoRows: false

    - Jellyseerr:
        icon: "jellyseerr.png"
        href: "{{HOMEPAGE_VAR_JELLYSEERR_DOMAIN}}"
        description: "Gestión de solicitudes de contenido"
        server: "host.docker.internal"
        container: "jellyseerr"
        widget:
          type: "jellyseerr"
          url: "{{HOMEPAGE_VAR_JELLYSEERR_URL}}"
          key: "{{HOMEPAGE_VAR_JELLYSEERR_KEY}}"

    - qBittorrent:
        icon: "qbittorrent.png"
        href: "{{HOMEPAGE_VAR_QBITTORRENT_DOMAIN}}"
        description: "Cliente torrent"
        server: "host.docker.internal"
        container: "qbittorrent"
        widget:
          type: "qbittorrent"
          url: "{{HOMEPAGE_VAR_QBITTORRENT_URL}}"
          username: "{{HOMEPAGE_VAR_QBITTORRENT_USERNAME}}"
          password: "{{HOMEPAGE_VAR_QBITTORRENT_PASSWORD}}"

# === SERVICIOS DE AUTOMATIZACIÓN ===
- Automatización:
    - AdGuard Home:
        icon: "adguard-home.png"
        href: "{{HOMEPAGE_VAR_ADGUARD_DOMAIN}}"
        description: "Filtrado DNS y bloqueo de anuncios"
        server: "host.docker.internal"
        container: "adguard"
        widget:
          type: "adguard"
          url: "{{HOMEPAGE_VAR_ADGUARD_URL}}"
          username: "{{HOMEPAGE_VAR_ADGUARD_USERNAME}}"
          password: "{{HOMEPAGE_VAR_ADGUARD_PASSWORD}}"

# === SERVICIOS DE DESCARGAS (*ARR STACK) ===
- Descargas:
    - Sonarr:
        icon: "sonarr.png"
        href: "{{HOMEPAGE_VAR_SONARR_DOMAIN}}"
        description: "Gestión automática de series"
        server: "host.docker.internal"
        container: "sonarr"
        widget:
          type: "sonarr"
          url: "{{HOMEPAGE_VAR_SONARR_URL}}"
          key: "{{HOMEPAGE_VAR_SONARR_KEY}}"
          enableQueue: true

    - Radarr:
        icon: "radarr.png"
        href: "{{HOMEPAGE_VAR_RADARR_DOMAIN}}"
        description: "Gestión automática de películas"
        server: "host.docker.internal"
        container: "radarr"
        widget:
          type: "radarr"
          url: "{{HOMEPAGE_VAR_RADARR_URL}}"
          key: "{{HOMEPAGE_VAR_RADARR_KEY}}"
          enableQueue: true

    - Jackett:
        icon: "jackett.png"
        href: "{{HOMEPAGE_VAR_JACKETT_DOMAIN}}"
        description: "Indexador de torrents"
        server: "host.docker.internal"
        container: "jackett"
        widget:
          type: "jackett"
          url: "{{HOMEPAGE_VAR_JACKETT_URL}}"
          key: "{{HOMEPAGE_VAR_JACKETT_KEY}}"

    - FlareSolverr:
        icon: "flaresolverr.png"
        href: "{{HOMEPAGE_VAR_FLARESOLVERR_URL}}"
        description: "Solucionador de Cloudflare"
        server: "host.docker.internal"
        container: "flaresolverr"
        widget:
          type: "flaresolverr"
          url: "{{HOMEPAGE_VAR_FLARESOLVERR_URL}}"

# === SERVICIOS DE SISTEMA ===
- Sistema:
    - Portainer:
        icon: "portainer.png"
        href: "{{HOMEPAGE_VAR_PORTAINER_URL}}"
        description: "Gestión de contenedores Docker"
        server: "host.docker.internal"
        container: "portainer"
        widget:
          type: "portainer"
          url: "{{HOMEPAGE_VAR_PORTAINER_URL}}"
          env: 1
          key: "{{HOMEPAGE_VAR_PORTAINER_KEY}}"

    - Caddy:
        icon: "caddy.png"
        href: "http://host.docker.internal:2019"
        description: "Proxy reverso y servidor web"
        server: "host.docker.internal"
        container: "caddy_proxy"
        widget:
          type: "caddy"
          url: "http://host.docker.internal:2019"

    - Watchtower:
        icon: "watchtower.png"
        href: "#"
        description: "Actualizador automático de contenedores"
        server: "host.docker.internal"
        container: "watchtower"

# === SERVICIOS DE INFRAESTRUCTURA ===
- Infraestructura:
    - DuckDNS 1:
        icon: "duckdns.png"
        href: "https://www.duckdns.org"
        description: "DNS dinámico - Dominio principal"
        server: "host.docker.internal"
        container: "duckdns_jucago705"

    - DuckDNS 2:
        icon: "duckdns.png"
        href: "https://www.duckdns.org"
        description: "DNS dinámico - Dominio secundario"
        server: "host.docker.internal"
        container: "duckdns_jucago706"

    - Telegram Bot:
        icon: "telegram.png"
        href: "http://host.docker.internal:8082"
        description: "Bot de notificaciones Telegram"
        server: "host.docker.internal"
        container: "telegram-bot"
        widget:
          type: "customapi"
          url: "http://host.docker.internal:8082/health"
          method: "GET"
