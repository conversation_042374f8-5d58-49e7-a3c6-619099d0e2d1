# 🔧 Solución de Problemas de Estabilidad

## 📋 Problemas Identificados y Solucionados

### ❌ **Problemas Encontrados:**

1. **Referencias a AdGuard Home eliminado**:
   - Servicio presente en `services.yaml` pero contenedor eliminado
   - Configuración en `api-keys.env` apuntando a servicio inexistente
   - Causaba errores en widgets de Homepage

2. **IP hardcodeada en configuración de Jackett**:
   - URL configurada como `http://**********:9117`
   - IP específica de contenedor que puede cambiar
   - Causaba fallos intermitentes de conectividad

3. **Problemas de permisos Docker en Homepage**:
   - Errores EACCES en `/var/run/docker.sock`
   - Homepage marcado como "unhealthy"
   - Widgets de Docker no funcionando correctamente

4. **Configuración subóptima de timeouts**:
   - Timeouts muy cortos (10s) causando fallos intermitentes
   - Intervalos de actualización muy frecuentes (30s)
   - Health checks demasiado agresivos

### ✅ **Soluciones Aplicadas:**

#### 🧹 **Limpieza de Referencias a AdGuard Home**
```yaml
# ELIMINADO de services.yaml:
- AdGuard Home:
    icon: "adguard-home.png"
    href: "https://tankeguard.duckdns.org"
    # ... configuración completa eliminada

# COMENTADO en api-keys.env:
# HOMEPAGE_VAR_ADGUARD_USERNAME=Tankeeee2_GAMES
# HOMEPAGE_VAR_ADGUARD_PASSWORD=Ahmadmuhsin4148!
# HOMEPAGE_VAR_ADGUARD_URL=http://host.docker.internal:8080 # ELIMINADO
```

#### 🔗 **Corrección de IP Hardcodeada**
```yaml
# ANTES:
url: "http://**********:9117"

# DESPUÉS:
url: "http://host.docker.internal:9117"
```

#### ⚙️ **Optimización de Configuración de Homepage**

**settings.yaml optimizado:**
```yaml
# Timeouts aumentados para estabilidad
timeout: 15000 # 15 segundos (antes: 10s)
refreshInterval: 60 # 60 segundos (antes: 30s)
hideErrors: true # Ocultar errores para mejor UX

# Configuración Docker optimizada
docker:
  socket: "/var/run/docker.sock"
  timeout: 15000
  retries: 3
  cache: 300
```

**docker.yaml mejorado:**
```yaml
# Configuración completa con timeouts y reintentos
my-docker:
  socket: /var/run/docker.sock
  timeout: 15000
  retries: 3
  cache: 300
```

#### 🐳 **Docker Compose Optimizado**
```yaml
homepage:
  # Health check mejorado
  healthcheck:
    test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "--timeout=10", "http://localhost:3000"]
    interval: 60s      # Antes: 30s
    timeout: 15s       # Antes: 10s
    retries: 5         # Antes: 3
    start_period: 60s  # Antes: 30s
  
  # Límites de recursos
  deploy:
    resources:
      limits:
        memory: 512M
      reservations:
        memory: 256M
  
  # Logging optimizado
  logging:
    driver: "json-file"
    options:
      max-size: "10m"
      max-file: "3"
```

## 🛠️ Scripts de Reparación Disponibles

### 🚀 **Script Principal: `complete-stability-fix.ps1`**

**Uso recomendado:**
```powershell
# Diagnóstico completo (sin cambios)
.\complete-stability-fix.ps1 -DiagnosticOnly

# Reparación rápida (solo Homepage)
.\complete-stability-fix.ps1 -QuickFix

# Reparación completa (todos los servicios)
.\complete-stability-fix.ps1 -FullRepair

# Reparación completa sin confirmación
.\complete-stability-fix.ps1 -FullRepair -Force
```

### 🔍 **Scripts Específicos:**

1. **`fix-homepage-stability.ps1`** - Diagnóstico y reparación de Homepage
2. **`optimize-docker-network.ps1`** - Optimización de redes Docker
3. **`verify-cloudflare-migration.ps1`** - Verificación de servicios (para futura migración)

## 📊 Análisis de Causas de Conectividad Intermitente

### 🔍 **Posibles Causas Identificadas:**

1. **Timeouts Agresivos**:
   - Health checks cada 30s con timeout de 10s
   - Widgets actualizándose cada 30s
   - **Solución**: Aumentados a 60s y 15s respectivamente

2. **Referencias a Servicios Inexistentes**:
   - AdGuard Home eliminado pero referenciado
   - **Solución**: Referencias completamente eliminadas

3. **IPs Hardcodeadas**:
   - Jackett usando IP específica del contenedor
   - **Solución**: Cambiado a `host.docker.internal`

4. **Problemas de Permisos Docker**:
   - Socket de Docker con permisos restrictivos
   - **Solución**: Configuración optimizada en Docker Compose

5. **Recursos Insuficientes**:
   - Homepage sin límites de memoria
   - **Solución**: Límites configurados (512M max, 256M reservado)

### 🌐 **Optimizaciones de Red Docker**

**Configuración mejorada:**
- Uso consistente de `host.docker.internal` para comunicación entre contenedores
- Redes Docker optimizadas (`caddy_network`, `arr_net`)
- Timeouts aumentados para estabilidad
- Reintentos configurados para fallos temporales

## 📈 Mejoras de Rendimiento Implementadas

### ⚡ **Optimizaciones de Homepage:**

1. **Caché Mejorado**:
   - TTL de caché aumentado a 300s
   - Caché de widgets optimizado

2. **Intervalos Optimizados**:
   - Actualización de widgets: 30s → 60s
   - Health checks: 30s → 60s
   - Timeouts: 10s → 15s

3. **Logging Controlado**:
   - Logs limitados a 10MB por archivo
   - Máximo 3 archivos de log
   - Previene crecimiento excesivo de logs

### 🔄 **Configuración de Reintentos:**

```yaml
# Configuración robusta de reintentos
retries: 5          # Antes: 3
timeout: 15000      # Antes: 10000
cache: 300          # Nuevo: caché de 5 minutos
```

## 🚨 Monitoreo y Mantenimiento

### 📊 **Comandos de Diagnóstico:**

```powershell
# Verificar estado de contenedores
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"

# Verificar salud específica de Homepage
docker inspect homepage --format "{{.State.Health.Status}}"

# Ver logs de Homepage
docker logs homepage --tail 50

# Verificar conectividad local
Test-NetConnection -ComputerName localhost -Port 3001
```

### 🔍 **Indicadores de Problemas:**

1. **Homepage marcado como "unhealthy"**
2. **Errores EACCES en logs**
3. **Widgets que no cargan datos**
4. **Conectividad intermitente a servicios locales**
5. **Timeouts frecuentes en DuckDNS**

### 💡 **Recomendaciones de Mantenimiento:**

1. **Ejecutar diagnósticos semanalmente**:
   ```powershell
   .\complete-stability-fix.ps1 -DiagnosticOnly
   ```

2. **Monitorear logs regularmente**:
   ```powershell
   docker logs homepage --tail 100
   ```

3. **Verificar uso de recursos**:
   ```powershell
   docker stats --no-stream
   ```

4. **Limpiar logs antiguos**:
   ```powershell
   docker system prune -f
   ```

## 🎯 Resultados Esperados

### ✅ **Mejoras Inmediatas:**

- ✅ Homepage estable y sin errores
- ✅ Widgets funcionando correctamente
- ✅ Conectividad consistente a servicios locales
- ✅ Eliminación de errores en logs
- ✅ Health checks pasando consistentemente

### 📈 **Mejoras de Rendimiento:**

- 🚀 Tiempo de carga de Homepage reducido
- 🚀 Widgets más responsivos
- 🚀 Menor uso de CPU y memoria
- 🚀 Logs más limpios y útiles

### 🛡️ **Mejoras de Estabilidad:**

- 🔒 Configuración robusta ante fallos temporales
- 🔒 Reintentos automáticos en caso de errores
- 🔒 Timeouts apropiados para evitar falsos positivos
- 🔒 Recursos garantizados para Homepage

## 🆘 Solución de Problemas Adicionales

### ❓ **Si Homepage sigue sin funcionar:**

1. **Reiniciar Docker Desktop**
2. **Verificar permisos de archivos**
3. **Ejecutar reparación completa**:
   ```powershell
   .\complete-stability-fix.ps1 -FullRepair -Force
   ```

### ❓ **Si persisten problemas de conectividad:**

1. **Optimizar redes Docker**:
   ```powershell
   .\optimize-docker-network.ps1 -All
   ```

2. **Verificar configuración de Windows Firewall**
3. **Comprobar antivirus que pueda bloquear Docker**

### ❓ **Si los widgets no cargan:**

1. **Verificar API keys en `api-keys.env`**
2. **Comprobar que los servicios estén ejecutándose**
3. **Revisar logs específicos del servicio**

---

## 📞 Soporte

**Scripts disponibles para diagnóstico:**
- `complete-stability-fix.ps1` - Solución completa
- `fix-homepage-stability.ps1` - Específico para Homepage
- `optimize-docker-network.ps1` - Optimización de red

**Archivos de configuración optimizados:**
- `services.yaml` - Limpio de referencias obsoletas
- `docker.yaml` - Configuración robusta
- `settings.yaml` - Timeouts optimizados
- `DNS-compose.yml` - Health checks mejorados

**🎉 ¡Tu configuración ahora debería ser mucho más estable y confiable!**
