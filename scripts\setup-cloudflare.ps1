# setup-cloudflare.ps1
# Script para automatizar la configuración inicial de Cloudflare

param(
    [string]$Domain = "",
    [string]$CloudflareEmail = "",
    [string]$CloudflareAPIKey = "",
    [switch]$Interactive = $true,
    [switch]$CreateBackup = $true,
    [switch]$DryRun = $false
)

# Configuración de colores
$Colors = @{
    Success = "Green"
    Error = "Red"
    Warning = "Yellow"
    Info = "Blue"
    Highlight = "Cyan"
}

function Write-ColorOutput {
    param($Message, $Color = "White")
    Write-Host $Message -ForegroundColor $Colors[$Color]
}

function Get-PublicIP {
    try {
        $IP = (Invoke-WebRequest -Uri "https://ipinfo.io/ip" -UseBasicParsing).Content.Trim()
        return $IP
    }
    catch {
        try {
            $IP = (Invoke-WebRequest -Uri "https://api.ipify.org" -UseBasicParsing).Content.Trim()
            return $IP
        }
        catch {
            Write-ColorOutput "❌ No se pudo obtener la IP pública" "Error"
            return $null
        }
    }
}

function Test-CloudflareAPI {
    param($Email, $APIKey)
    
    $Headers = @{
        "X-Auth-Email" = $Email
        "X-Auth-Key" = $APIKey
        "Content-Type" = "application/json"
    }
    
    try {
        $Response = Invoke-RestMethod -Uri "https://api.cloudflare.com/client/v4/user" -Headers $Headers
        if ($Response.success) {
            Write-ColorOutput "✅ Credenciales de Cloudflare válidas" "Success"
            return $true
        } else {
            Write-ColorOutput "❌ Credenciales de Cloudflare inválidas" "Error"
            return $false
        }
    }
    catch {
        Write-ColorOutput "❌ Error conectando con la API de Cloudflare: $($_.Exception.Message)" "Error"
        return $false
    }
}

function Get-CloudflareZoneID {
    param($Domain, $Email, $APIKey)
    
    $Headers = @{
        "X-Auth-Email" = $Email
        "X-Auth-Key" = $APIKey
        "Content-Type" = "application/json"
    }
    
    try {
        $Response = Invoke-RestMethod -Uri "https://api.cloudflare.com/client/v4/zones?name=$Domain" -Headers $Headers
        if ($Response.success -and $Response.result.Count -gt 0) {
            return $Response.result[0].id
        } else {
            Write-ColorOutput "❌ No se encontró la zona para el dominio $Domain en Cloudflare" "Error"
            return $null
        }
    }
    catch {
        Write-ColorOutput "❌ Error obteniendo Zone ID: $($_.Exception.Message)" "Error"
        return $null
    }
}

function Create-DNSRecord {
    param($ZoneID, $Type, $Name, $Content, $Proxied, $Email, $APIKey)
    
    $Headers = @{
        "X-Auth-Email" = $Email
        "X-Auth-Key" = $APIKey
        "Content-Type" = "application/json"
    }
    
    $Body = @{
        type = $Type
        name = $Name
        content = $Content
        proxied = $Proxied
    } | ConvertTo-Json
    
    try {
        if ($DryRun) {
            Write-ColorOutput "   [DRY-RUN] Crear registro DNS: $Type $Name -> $Content (Proxied: $Proxied)" "Highlight"
            return $true
        }
        
        $Response = Invoke-RestMethod -Uri "https://api.cloudflare.com/client/v4/zones/$ZoneID/dns_records" -Method POST -Headers $Headers -Body $Body
        if ($Response.success) {
            Write-ColorOutput "   ✅ Registro DNS creado: $Type $Name -> $Content" "Success"
            return $true
        } else {
            Write-ColorOutput "   ❌ Error creando registro DNS: $($Response.errors[0].message)" "Error"
            return $false
        }
    }
    catch {
        Write-ColorOutput "   ❌ Error en API: $($_.Exception.Message)" "Error"
        return $false
    }
}

function Create-ConfigurationBackup {
    Write-ColorOutput "📦 Creando backup de configuración actual..." "Info"
    
    $BackupDate = Get-Date -Format "yyyyMMdd_HHmmss"
    $BackupDir = "C:\docker\backups\pre-cloudflare_$BackupDate"
    
    try {
        New-Item -ItemType Directory -Path $BackupDir -Force | Out-Null
        
        # Backup de archivos críticos
        $FilesToBackup = @(
            @{Source="C:\docker\Caddyfile"; Dest="$BackupDir\Caddyfile.backup"},
            @{Source="C:\docker\Caddyfile.host"; Dest="$BackupDir\Caddyfile.host.backup"},
            @{Source="C:\docker\duckdns_updater\DNS-compose.yml"; Dest="$BackupDir\DNS-compose.yml.backup"}
        )
        
        foreach ($File in $FilesToBackup) {
            if (Test-Path $File.Source) {
                Copy-Item $File.Source $File.Dest -Force
                Write-ColorOutput "   ✅ Backup creado: $($File.Dest)" "Success"
            } else {
                Write-ColorOutput "   ⚠️  Archivo no encontrado: $($File.Source)" "Warning"
            }
        }
        
        # Crear archivo de información del backup
        $BackupInfo = @"
Backup de configuración pre-Cloudflare
Fecha: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')
Dominio objetivo: $Domain
IP pública en el momento del backup: $(Get-PublicIP)

Archivos incluidos:
- Caddyfile.backup
- Caddyfile.host.backup  
- DNS-compose.yml.backup

Para restaurar, ejecuta:
.\rollback-to-duckdns.ps1 -Execute
"@
        
        $BackupInfo | Out-File -FilePath "$BackupDir\README.txt" -Encoding UTF8
        
        Write-ColorOutput "✅ Backup completado en: $BackupDir" "Success"
        return $BackupDir
    }
    catch {
        Write-ColorOutput "❌ Error creando backup: $($_.Exception.Message)" "Error"
        return $null
    }
}

function Setup-CloudflareDNS {
    param($Domain, $PublicIP, $Email, $APIKey)
    
    Write-ColorOutput "🌐 Configurando registros DNS en Cloudflare..." "Info"
    
    $ZoneID = Get-CloudflareZoneID -Domain $Domain -Email $Email -APIKey $APIKey
    if (-not $ZoneID) {
        return $false
    }
    
    Write-ColorOutput "📋 Zone ID obtenido: $ZoneID" "Info"
    
    # Registros DNS a crear
    $DNSRecords = @(
        @{Type="A"; Name="@"; Content=$PublicIP; Proxied=$true; Description="Registro principal"},
        @{Type="A"; Name="*"; Content=$PublicIP; Proxied=$true; Description="Wildcard para subdominios"},
        @{Type="A"; Name="home"; Content=$PublicIP; Proxied=$true; Description="Homepage Dashboard"},
        @{Type="A"; Name="flix"; Content=$PublicIP; Proxied=$true; Description="Jellyfin"},
        @{Type="A"; Name="torrent"; Content=$PublicIP; Proxied=$true; Description="qBittorrent"},
        @{Type="A"; Name="sonarr"; Content=$PublicIP; Proxied=$true; Description="Sonarr"},
        @{Type="A"; Name="radarr"; Content=$PublicIP; Proxied=$true; Description="Radarr"},
        @{Type="A"; Name="jackett"; Content=$PublicIP; Proxied=$true; Description="Jackett"},
        @{Type="A"; Name="jellyseerr"; Content=$PublicIP; Proxied=$true; Description="Jellyseerr"},
        @{Type="A"; Name="guard"; Content=$PublicIP; Proxied=$true; Description="AdGuard Home"},
        @{Type="A"; Name="prowlarr"; Content=$PublicIP; Proxied=$true; Description="Prowlarr"},
        @{Type="A"; Name="ha"; Content=$PublicIP; Proxied=$true; Description="Home Assistant (futuro)"}
    )
    
    $SuccessCount = 0
    foreach ($Record in $DNSRecords) {
        Write-ColorOutput "   🔧 Configurando: $($Record.Description)" "Info"
        if (Create-DNSRecord -ZoneID $ZoneID -Type $Record.Type -Name $Record.Name -Content $Record.Content -Proxied $Record.Proxied -Email $Email -APIKey $APIKey) {
            $SuccessCount++
        }
        Start-Sleep -Seconds 1
    }
    
    Write-ColorOutput "📊 Registros DNS creados: $SuccessCount/$($DNSRecords.Count)" "Info"
    return $SuccessCount -eq $DNSRecords.Count
}

function Copy-CloudflareConfig {
    param($Domain)
    
    Write-ColorOutput "📄 Configurando archivos de Caddy para Cloudflare..." "Info"
    
    try {
        # Verificar que existe el archivo de configuración de Cloudflare
        $CloudflareConfigPath = "C:\docker\Caddyfile.cloudflare"
        if (-not (Test-Path $CloudflareConfigPath)) {
            Write-ColorOutput "❌ No se encontró Caddyfile.cloudflare" "Error"
            return $false
        }
        
        # Leer el contenido y reemplazar el dominio placeholder
        $ConfigContent = Get-Content $CloudflareConfigPath -Raw
        $UpdatedContent = $ConfigContent -replace "tudominio\.com", $Domain
        
        # Guardar la configuración actualizada
        $UpdatedConfigPath = "C:\docker\Caddyfile.cloudflare.configured"
        if ($DryRun) {
            Write-ColorOutput "   [DRY-RUN] Guardar configuración actualizada en: $UpdatedConfigPath" "Highlight"
        } else {
            $UpdatedContent | Out-File -FilePath $UpdatedConfigPath -Encoding UTF8
            Write-ColorOutput "   ✅ Configuración de Caddy actualizada: $UpdatedConfigPath" "Success"
        }
        
        return $true
    }
    catch {
        Write-ColorOutput "❌ Error configurando Caddy: $($_.Exception.Message)" "Error"
        return $false
    }
}

function Show-NextSteps {
    param($Domain, $BackupDir)
    
    Write-ColorOutput "`n📋 Próximos pasos para completar la migración:" "Info"
    Write-ColorOutput "=" * 50 "Info"
    
    Write-ColorOutput "1. 🔐 Configurar certificados SSL Origin en Cloudflare:" "Info"
    Write-ColorOutput "   • Ve a SSL/TLS → Origin Server en Cloudflare" "Highlight"
    Write-ColorOutput "   • Crea un certificado Origin para *.$Domain y $Domain" "Highlight"
    Write-ColorOutput "   • Guarda el certificado y clave en C:\docker\ssl\cloudflare\" "Highlight"
    
    Write-ColorOutput "`n2. 🔧 Actualizar configuración de Docker:" "Info"
    Write-ColorOutput "   • Modifica DNS-compose.yml para usar Caddyfile.cloudflare.configured" "Highlight"
    Write-ColorOutput "   • Agrega volumen para certificados SSL" "Highlight"
    
    Write-ColorOutput "`n3. 🚀 Probar la configuración:" "Info"
    Write-ColorOutput "   • Ejecuta: .\verify-cloudflare-migration.ps1 -Domain '$Domain' -Detailed" "Highlight"
    
    Write-ColorOutput "`n4. 🔄 Si algo sale mal:" "Info"
    Write-ColorOutput "   • Ejecuta: .\rollback-to-duckdns.ps1 -Execute" "Highlight"
    Write-ColorOutput "   • Backup disponible en: $BackupDir" "Highlight"
    
    Write-ColorOutput "`n5. ⚙️ Optimizaciones post-migración:" "Info"
    Write-ColorOutput "   • Configurar Page Rules en Cloudflare" "Highlight"
    Write-ColorOutput "   • Habilitar compresión Brotli" "Highlight"
    Write-ColorOutput "   • Configurar alertas de uptime" "Highlight"
}

# Función principal
function Start-CloudflareSetup {
    Clear-Host
    Write-ColorOutput "☁️  Configuración Inicial de Cloudflare" "Info"
    Write-ColorOutput "=" * 40 "Info"
    Write-ColorOutput "📅 Fecha: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" "Info"
    Write-ColorOutput ""
    
    if ($DryRun) {
        Write-ColorOutput "🧪 MODO DRY-RUN - No se realizarán cambios reales" "Highlight"
        Write-ColorOutput ""
    }
    
    # Obtener información si no se proporcionó
    if ($Interactive) {
        if (-not $Domain) {
            $Domain = Read-Host "🌐 Ingresa tu dominio (ej: mihomelabpersonal.com)"
        }
        
        if (-not $CloudflareEmail) {
            $CloudflareEmail = Read-Host "📧 Ingresa tu email de Cloudflare"
        }
        
        if (-not $CloudflareAPIKey) {
            $CloudflareAPIKey = Read-Host "🔑 Ingresa tu API Key de Cloudflare" -AsSecureString
            $CloudflareAPIKey = [Runtime.InteropServices.Marshal]::PtrToStringAuto([Runtime.InteropServices.Marshal]::SecureStringToBSTR($CloudflareAPIKey))
        }
    }
    
    # Validar parámetros
    if (-not $Domain -or -not $CloudflareEmail -or -not $CloudflareAPIKey) {
        Write-ColorOutput "❌ Faltan parámetros requeridos" "Error"
        return $false
    }
    
    Write-ColorOutput "🌐 Dominio: $Domain" "Info"
    Write-ColorOutput "📧 Email: $CloudflareEmail" "Info"
    Write-ColorOutput ""
    
    # Obtener IP pública
    $PublicIP = Get-PublicIP
    if (-not $PublicIP) {
        Write-ColorOutput "❌ No se pudo obtener la IP pública" "Error"
        return $false
    }
    Write-ColorOutput "🌍 IP pública detectada: $PublicIP" "Info"
    Write-ColorOutput ""
    
    # Verificar credenciales de Cloudflare
    if (-not (Test-CloudflareAPI -Email $CloudflareEmail -APIKey $CloudflareAPIKey)) {
        return $false
    }
    Write-ColorOutput ""
    
    # Crear backup si se solicita
    $BackupDir = $null
    if ($CreateBackup) {
        $BackupDir = Create-ConfigurationBackup
        if (-not $BackupDir) {
            Write-ColorOutput "❌ Error creando backup. Abortando." "Error"
            return $false
        }
        Write-ColorOutput ""
    }
    
    # Configurar DNS en Cloudflare
    if (-not (Setup-CloudflareDNS -Domain $Domain -PublicIP $PublicIP -Email $CloudflareEmail -APIKey $CloudflareAPIKey)) {
        Write-ColorOutput "❌ Error configurando DNS en Cloudflare" "Error"
        return $false
    }
    Write-ColorOutput ""
    
    # Configurar archivos de Caddy
    if (-not (Copy-CloudflareConfig -Domain $Domain)) {
        Write-ColorOutput "❌ Error configurando Caddy" "Error"
        return $false
    }
    Write-ColorOutput ""
    
    Write-ColorOutput "🎉 ¡Configuración inicial de Cloudflare completada!" "Success"
    
    # Mostrar próximos pasos
    Show-NextSteps -Domain $Domain -BackupDir $BackupDir
    
    return $true
}

# Ejecutar configuración
Start-CloudflareSetup
