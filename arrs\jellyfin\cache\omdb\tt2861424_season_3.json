{"Title": "<PERSON> and <PERSON><PERSON><PERSON>", "seriesID": "tt2861424", "Season": 3, "totalSeasons": 8, "Episodes": [{"Title": "The Rickshank Rickdemption", "Year": "2017", "Rated": "TV-14", "Released": "01 Apr 2017", "Runtime": "22 min", "Genre": "Animation, Adventure, Comedy", "Director": "<PERSON>-<PERSON>, <PERSON>, <PERSON>", "Writer": "<PERSON>, <PERSON>, <PERSON>", "Actors": "<PERSON>, <PERSON>, <PERSON>", "Plot": "<PERSON>, still in galactic prison, puts an intricate escape plan into action. Back on Earth, which is now under federation control, <PERSON><PERSON><PERSON> and <PERSON> have an argument about their grandpa.", "Language": "English", "Country": "United States", "Poster": "https://m.media-amazon.com/images/M/MV5BNWVlMGE5NGEtZTI4Yi00YWUxLTk2NDktMjRkODhlNGI0MzYxXkEyXkFqcGc@._V1_SX300.jpg", "imdbRating": "9.6", "imdbVotes": "25087", "imdbID": "tt5218228", "Episode": 1}, {"Title": "Rickmancing the Stone", "Year": "2017", "Rated": "TV-14", "Released": "30 Jul 2017", "Runtime": "22 min", "Genre": "Animation, Adventure, Comedy", "Director": "<PERSON>, <PERSON>, <PERSON>", "Writer": "<PERSON>, <PERSON>, <PERSON>", "Actors": "<PERSON>, <PERSON>, <PERSON>", "Plot": "With the kids dealing with their parents' divorce, <PERSON> takes them to a Mad Max-style universe where he tries to steal a green crystal from a group of scavengers.", "Language": "English", "Country": "United States", "Poster": "https://m.media-amazon.com/images/M/MV5BM2Q1MGEzZDYtMmZmYS00MDBkLTgwODAtNTIxNGNjN2VmZTMyXkEyXkFqcGc@._V1_SX300.jpg", "imdbRating": "8.1", "imdbVotes": "14925", "imdbID": "tt5218256", "Episode": 2}, {"Title": "<PERSON><PERSON>", "Year": "2017", "Rated": "TV-14", "Released": "06 Aug 2017", "Runtime": "23 min", "Genre": "Animation, Adventure, Comedy", "Director": "<PERSON>, <PERSON>, <PERSON>", "Writer": "<PERSON>, <PERSON>, <PERSON>", "Actors": "<PERSON>, <PERSON>, <PERSON>", "Plot": "<PERSON> turns himself into a pickle while <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON> go to family therapy.", "Language": "English", "Country": "United States", "Poster": "https://m.media-amazon.com/images/M/MV5BNmIwM2IyM2UtOGRlNC00OGE1LTgzZjAtYWZmNjBjOGMyYzI4XkEyXkFqcGc@._V1_SX300.jpg", "imdbRating": "9.3", "imdbVotes": "22770", "imdbID": "tt5218268", "Episode": 3}, {"Title": "Vindicators 3: The Return of Worldender", "Year": "2017", "Rated": "TV-MA", "Released": "13 Aug 2017", "Runtime": "23 min", "Genre": "Animation, Adventure, Comedy", "Director": "<PERSON>, <PERSON>, <PERSON>", "Writer": "<PERSON>, <PERSON>, <PERSON>", "Actors": "<PERSON>, <PERSON>, <PERSON>", "Plot": "<PERSON> and <PERSON><PERSON><PERSON> are summoned by the Vindicators to stop <PERSON><PERSON> but end up in a deathtrap conceived by <PERSON><PERSON>.", "Language": "English", "Country": "United States", "Poster": "https://m.media-amazon.com/images/M/MV5BOTBiZmRiOWItNjdiZC00NTI0LWI1NTEtYTg2MjU5Njg2OWI1XkEyXkFqcGc@._V1_SX300.jpg", "imdbRating": "8.1", "imdbVotes": "15240", "imdbID": "tt5218288", "Episode": 4}, {"Title": "The Whirly Dirly Conspiracy", "Year": "2017", "Rated": "TV-14", "Released": "20 Aug 2017", "Runtime": "22 min", "Genre": "Animation, Adventure, Comedy", "Director": "<PERSON>-<PERSON>, <PERSON>, <PERSON>", "Writer": "<PERSON>, <PERSON>, <PERSON>", "Actors": "<PERSON>, <PERSON>, <PERSON>", "Plot": "<PERSON> takes <PERSON> on a sympathy adventure to a resort where everyone is immortal when there, and <PERSON> meets some old acquaintances of <PERSON>'<PERSON> who want payback.", "Language": "English", "Country": "United States", "Poster": "https://m.media-amazon.com/images/M/MV5BMDc3YzIxOTMtOGJmOS00ZDE2LWEyNDEtY2Q2NDhiMmZhMzM3XkEyXkFqcGc@._V1_SX300.jpg", "imdbRating": "8.4", "imdbVotes": "14581", "imdbID": "tt5218310", "Episode": 5}, {"Title": "Rest and Ricklaxation", "Year": "2017", "Rated": "TV-14", "Released": "27 Aug 2017", "Runtime": "22 min", "Genre": "Animation, Adventure, Comedy", "Director": "<PERSON>, <PERSON>, <PERSON>", "Writer": "<PERSON>, <PERSON>, <PERSON>", "Actors": "<PERSON>, <PERSON>, <PERSON>", "Plot": "Following a stressful adventure, <PERSON> and <PERSON><PERSON><PERSON> go on a break to a spa where they remove their toxins, which in turn take a form of their own.", "Language": "English", "Country": "United States", "Poster": "https://m.media-amazon.com/images/M/MV5BNzBjMWRmMzItZjM2Ny00M2ZjLWJlNGUtNDg2NjVjYjMyYTM3XkEyXkFqcGc@._V1_SX300.jpg", "imdbRating": "8.8", "imdbVotes": "15180", "imdbID": "tt5218318", "Episode": 6}, {"Title": "The Ricklantis Mixup", "Year": "2017", "Rated": "TV-14", "Released": "10 Sep 2017", "Runtime": "22 min", "Genre": "Animation, Adventure, Comedy", "Director": "<PERSON>, <PERSON>, <PERSON>", "Writer": "<PERSON>, <PERSON>, <PERSON>", "Actors": "<PERSON>, <PERSON>, <PERSON>", "Plot": "While <PERSON> and <PERSON><PERSON><PERSON> go off to adventure in Atlantis, we take a look at how the Citadel has rebuilt itself since both <PERSON> and <PERSON><PERSON><PERSON> visited.", "Language": "English", "Country": "United States", "Poster": "https://m.media-amazon.com/images/M/MV5BZDYwMjYzNjktMjNkYy00MWFmLWE2ZTgtYmVlNzgyOWI3ZTcwXkEyXkFqcGc@._V1_SX300.jpg", "imdbRating": "9.8", "imdbVotes": "38673", "imdbID": "tt5218332", "Episode": 7}, {"Title": "<PERSON><PERSON><PERSON>'s Mind Blowers", "Year": "2017", "Rated": "TV-14", "Released": "17 Sep 2017", "Runtime": "22 min", "Genre": "Animation, Adventure, Comedy", "Director": "<PERSON>, <PERSON>, <PERSON>", "Writer": "<PERSON>, <PERSON>, <PERSON>", "Actors": "<PERSON>, <PERSON>, <PERSON>", "Plot": "<PERSON> reveals to <PERSON><PERSON><PERSON> his \"<PERSON><PERSON><PERSON>'s mind blowers,\" a collection of memories <PERSON><PERSON><PERSON> asked <PERSON> to erase from his mind.", "Language": "English", "Country": "United States", "Poster": "https://m.media-amazon.com/images/M/MV5BZTFhNzFjZDgtZWEzZS00ODZjLWIyMDItMGM5ZTUyMzE3MDlkXkEyXkFqcGc@._V1_SX300.jpg", "imdbRating": "8.9", "imdbVotes": "14718", "imdbID": "tt5218350", "Episode": 8}, {"Title": "The ABCs of Beth", "Year": "2017", "Rated": "TV-14", "Released": "24 Sep 2017", "Runtime": "22 min", "Genre": "Animation, Adventure, Comedy", "Director": "<PERSON>-<PERSON>, <PERSON>, <PERSON>", "Writer": "<PERSON>, <PERSON>, <PERSON>", "Actors": "<PERSON>, <PERSON>, <PERSON>", "Plot": "<PERSON> brings <PERSON> to a world he created for her when she was younger while <PERSON> looks for a long-lost childhood friend trapped there for years.", "Language": "English", "Country": "United States", "Poster": "https://m.media-amazon.com/images/M/MV5BNjdiNWQxMWMtZjEzYS00YmFlLTlkZjEtY2U1ZGI0NWFiNWYyXkEyXkFqcGc@._V1_SX300.jpg", "imdbRating": "7.9", "imdbVotes": "12728", "imdbID": "tt5218370", "Episode": 9}, {"Title": "The Rickchurian Mortyd<PERSON>", "Year": "2017", "Rated": "TV-14", "Released": "01 Oct 2017", "Runtime": "22 min", "Genre": "Animation, Adventure, Comedy", "Director": "<PERSON>, <PERSON>, <PERSON>", "Writer": "<PERSON>, <PERSON>, <PERSON>", "Actors": "<PERSON>, <PERSON>, <PERSON>", "Plot": "<PERSON> and <PERSON><PERSON><PERSON> are called upon by the president to kill a vicious alien in the secret tunnels under the White House.", "Language": "English", "Country": "United States", "Poster": "https://m.media-amazon.com/images/M/MV5BMmYxZTA4OGQtNTE1Yy00NmEzLWE4ZDMtOTUxNjY5MTQ0NTU4XkEyXkFqcGc@._V1_SX300.jpg", "imdbVotes": "13595", "imdbID": "tt5218386", "Episode": 10}], "Response": "True"}