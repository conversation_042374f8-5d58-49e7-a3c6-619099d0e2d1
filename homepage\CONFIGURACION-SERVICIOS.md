# Configuración Detallada de Servicios para Homepage

## 📋 Índice de Servicios

1. [Home Assistant](#-home-assistant)
2. [AdGuard Home](#-adguard-home)
3. [Sonarr](#-sonarr)
4. [<PERSON><PERSON>](#-radarr)
5. [<PERSON><PERSON>](#-jackett)
6. [Je<PERSON>seerr](#-jellyseerr)
7. [Je<PERSON><PERSON>](#-jellyfin)
8. [qBittorrent](#-qbittorrent)
9. [<PERSON><PERSON><PERSON>](#-portainer)

---

## 🏠 Home Assistant

### Obtener API Key
1. Accede a Home Assistant: `http://host.docker.internal:8123`
2. Ve a tu **Perfil** (esquina inferior izquierda)
3. Desplázate hasta **Tokens de acceso de larga duración**
4. Haz clic en **Crear token**
5. Dale un nombre: `Homepage Dashboard`
6. Copia el token generado

### Configuración en Homepage
```env
HOMEPAGE_VAR_HOMEASSISTANT_KEY=tu_token_aqui
```

### Widgets Disponibles
- Estado de sensores personalizados
- Información de dispositivos
- Estados de automatizaciones

---

## 🛡️ AdGuard Home

### Obtener Credenciales
1. Accede a AdGuard Home: `https://tankeguard.duckdns.org`
2. Usa las credenciales de administrador que configuraste durante la instalación inicial
3. Si no las recuerdas, puedes resetearlas desde el contenedor

### Configuración en Homepage
```env
HOMEPAGE_VAR_ADGUARD_USERNAME=admin
HOMEPAGE_VAR_ADGUARD_PASSWORD=tu_password_aqui
```

### Generar Autenticación Base64
```powershell
# En PowerShell
$credentials = "admin:tu_password"
$bytes = [System.Text.Encoding]::UTF8.GetBytes($credentials)
$base64 = [System.Convert]::ToBase64String($bytes)
Write-Host $base64
```

### Widgets Disponibles
- Consultas DNS totales
- Consultas bloqueadas
- Porcentaje de bloqueo
- Tiempo de respuesta promedio

---

## 📺 Sonarr

### Obtener API Key
1. Accede a Sonarr: `https://tankesonarr.duckdns.org`
2. Ve a **Configuración** → **General**
3. En la sección **Seguridad**, copia la **API Key**

### Configuración en Homepage
```env
HOMEPAGE_VAR_SONARR_KEY=tu_api_key_aqui
```

### Widgets Disponibles
- Series monitoreadas
- Episodios faltantes
- Cola de descargas
- Espacio en disco

---

## 🎬 Radarr

### Obtener API Key
1. Accede a Radarr: `https://tankeradarr.duckdns.org`
2. Ve a **Configuración** → **General**
3. En la sección **Seguridad**, copia la **API Key**

### Configuración en Homepage
```env
HOMEPAGE_VAR_RADARR_KEY=tu_api_key_aqui
```

### Widgets Disponibles
- Películas monitoreadas
- Películas faltantes
- Cola de descargas
- Espacio en disco

---

## 🔍 Jackett

### Obtener API Key
1. Accede a Jackett: `https://tankejackett.duckdns.org`
2. En la página principal, la **API Key** está visible en la parte superior
3. Copia la clave completa

### Configuración en Homepage
```env
HOMEPAGE_VAR_JACKETT_KEY=tu_api_key_aqui
```

### Widgets Disponibles
- Indexadores configurados
- Indexadores activos
- Búsquedas recientes

---

## 🎭 Jellyseerr

### Obtener API Key
1. Accede a Jellyseerr: `https://tankejellyseerr.duckdns.org`
2. Ve a **Configuración** → **General**
3. En la sección **API**, copia la **API Key**

### Configuración en Homepage
```env
HOMEPAGE_VAR_JELLYSEERR_KEY=tu_api_key_aqui
```

### Widgets Disponibles
- Solicitudes pendientes
- Solicitudes aprobadas
- Usuarios activos

---

## 🎵 Jellyfin

### Obtener API Key
1. Accede a Jellyfin: `https://tankeflix.duckdns.org`
2. Ve al **Panel de administración**
3. Selecciona **API Keys** en el menú lateral
4. Haz clic en **+** para crear una nueva API key
5. Dale un nombre: `Homepage Dashboard`
6. Copia la clave generada

### Configuración en Homepage
```env
HOMEPAGE_VAR_JELLYFIN_KEY=tu_api_key_aqui
```

### Widgets Disponibles
- Contenido en reproducción
- Usuarios activos
- Biblioteca de medios
- Estadísticas de reproducción

---

## 🌊 qBittorrent

### Configurar Credenciales
1. Accede a qBittorrent: `https://tanketorrent.duckdns.org`
2. Ve a **Herramientas** → **Opciones**
3. En la pestaña **Web UI**:
   - Anota el **usuario** (por defecto: `admin`)
   - Anota/cambia la **contraseña**

### Configuración en Homepage
```env
HOMEPAGE_VAR_QBITTORRENT_USERNAME=admin
HOMEPAGE_VAR_QBITTORRENT_PASSWORD=tu_password_aqui
```

### Widgets Disponibles
- Torrents activos
- Velocidad de descarga/subida
- Ratio global
- Espacio libre

---

## 🐳 Portainer

### Obtener API Key
1. Accede a Portainer: `http://host.docker.internal:9000`
2. Ve a **User settings** (icono de usuario)
3. Selecciona **Access tokens**
4. Haz clic en **Add access token**
5. Dale un nombre: `Homepage Dashboard`
6. Copia el token generado

### Configuración en Homepage
```env
HOMEPAGE_VAR_PORTAINER_KEY=tu_api_key_aqui
```

### Widgets Disponibles
- Contenedores ejecutándose
- Contenedores detenidos
- Imágenes
- Volúmenes

---

## 🔧 Configuración Final

### 1. Editar el archivo de API keys
```powershell
notepad C:\docker\duckdns_updater\homepage\config\api-keys.env
```

### 2. Verificar configuración
```powershell
cd C:\docker\homepage
.\verify-config.ps1 -Detailed
```

### 3. Iniciar Homepage
```powershell
.\setup-homepage.ps1 -Start
```

### 4. Verificar funcionamiento
```powershell
.\setup-homepage.ps1 -Status
```

---

## 🚨 Solución de Problemas

### Widget no muestra datos
1. Verifica que la API key sea correcta
2. Comprueba que el servicio esté ejecutándose
3. Revisa los logs: `.\setup-homepage.ps1 -Logs`

### Error de conexión
1. Verifica que el contenedor esté en la red correcta
2. Comprueba la URL del servicio
3. Asegúrate de que no haya conflictos de puertos

### Autenticación fallida
1. Regenera la API key en el servicio
2. Actualiza el archivo `api-keys.env`
3. Reinicia Homepage: `.\setup-homepage.ps1 -Restart`

---

## 📞 Recursos Adicionales

- [Documentación oficial de Homepage](https://gethomepage.dev)
- [Widgets disponibles](https://gethomepage.dev/latest/widgets/)
- [Configuración de servicios](https://gethomepage.dev/latest/configs/services/)
- [Solución de problemas](https://gethomepage.dev/latest/troubleshooting/)
