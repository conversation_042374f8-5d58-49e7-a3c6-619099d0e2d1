# Arr-Bo<PERSON> Dockerfile
# Bot de Telegram para gestionar Radarr y Sonarr
# Integrado con stack DNS DuckDNS

FROM python:3.11-slim

# Metadatos
LABEL maintainer="telegram-Bot"
LABEL description="Bot de Telegram para gestionar Radarr y Sonarr"
LABEL version="1.0.0"

# Variables de entorno
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1

# Crear usuario no-root para seguridad
RUN groupadd -r arrbot && useradd -r -g arrbot arrbot

# Directorio de trabajo
WORKDIR /app

# Instalar dependencias del sistema
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Copiar archivos de dependencias
COPY requirements.txt .

# Instalar dependencias de Python
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt

# Copiar código de la aplicación
COPY bot.py .

# Crear directorio para logs
RUN mkdir -p /app/logs && chown -R arrbot:arrbot /app

# Cambiar a usuario no-root
USER arrbot

# Exponer puerto para webhooks
EXPOSE 8082

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8082/health || exit 1

# Comando por defecto
CMD ["python", "bot.py"]
