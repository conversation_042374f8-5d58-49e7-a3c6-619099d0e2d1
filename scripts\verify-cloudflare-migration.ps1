# verify-cloudflare-migration.ps1
# Script para verificar el estado de la migración a Cloudflare

param(
    [string]$Domain = "tudominio.com",
    [switch]$Detailed,
    [switch]$Performance,
    [switch]$All
)

# Configuración de colores
$Colors = @{
    Success = "Green"
    Error = "Red"
    Warning = "Yellow"
    Info = "Blue"
    Highlight = "Cyan"
}

function Write-ColorOutput {
    param($Message, $Color = "White")
    Write-Host $Message -ForegroundColor $Colors[$Color]
}

function Test-CloudflareService {
    param($Subdomain, $Port, $ServiceName, $ExpectedContent = $null)
    
    $FullDomain = if ($Subdomain -eq "@") { $Domain } else { "$Subdomain.$Domain" }
    $TestUrl = "https://$FullDomain"
    
    Write-ColorOutput "🔍 Probando $ServiceName ($FullDomain)..." "Info"
    
    try {
        $StartTime = Get-Date
        $Response = Invoke-WebRequest -Uri $TestUrl -UseBasicParsing -TimeoutSec 15
        $EndTime = Get-Date
        $ResponseTime = ($EndTime - $StartTime).TotalMilliseconds
        
        if ($Response.StatusCode -eq 200) {
            Write-ColorOutput "✅ $ServiceName - OK (${Response.StatusCode}) - ${ResponseTime}ms" "Success"
            
            if ($Detailed) {
                # Información de Cloudflare
                $CFRay = $Response.Headers['CF-RAY']
                $CFCache = $Response.Headers['CF-Cache-Status']
                $CFCountry = $Response.Headers['CF-IPCountry']
                
                if ($CFRay) {
                    Write-ColorOutput "   📊 CF-RAY: $CFRay" "Highlight"
                    Write-ColorOutput "   🌍 Cache Status: $CFCache" "Highlight"
                    Write-ColorOutput "   🌎 País: $CFCountry" "Highlight"
                }
                
                # Verificar headers de seguridad
                $SecurityHeaders = @('X-Content-Type-Options', 'X-Frame-Options', 'Strict-Transport-Security')
                foreach ($Header in $SecurityHeaders) {
                    $Value = $Response.Headers[$Header]
                    if ($Value) {
                        Write-ColorOutput "   🔒 $Header: $Value" "Highlight"
                    }
                }
            }
            
            if ($Performance) {
                return @{
                    Success = $true
                    ResponseTime = $ResponseTime
                    Service = $ServiceName
                    Domain = $FullDomain
                }
            }
            
            return $true
        }
    }
    catch {
        $ErrorMessage = $_.Exception.Message
        Write-ColorOutput "❌ $ServiceName - Error: $ErrorMessage" "Error"
        
        # Intentar diagnóstico básico
        try {
            $DNSResult = Resolve-DnsName $FullDomain -ErrorAction SilentlyContinue
            if ($DNSResult) {
                Write-ColorOutput "   🔍 DNS resuelve a: $($DNSResult.IPAddress -join ', ')" "Warning"
            } else {
                Write-ColorOutput "   ❌ DNS no resuelve" "Error"
            }
        }
        catch {
            Write-ColorOutput "   ❌ Error en resolución DNS" "Error"
        }
        
        if ($Performance) {
            return @{
                Success = $false
                ResponseTime = 0
                Service = $ServiceName
                Domain = $FullDomain
                Error = $ErrorMessage
            }
        }
        
        return $false
    }
}

function Test-DNSPropagation {
    param($Domain)
    
    Write-ColorOutput "🌐 Verificando propagación DNS para $Domain..." "Info"
    
    $DNSServers = @(
        @{Name="Cloudflare"; Server="*******"},
        @{Name="Google"; Server="*******"},
        @{Name="OpenDNS"; Server="**************"},
        @{Name="Local"; Server=$null}
    )
    
    foreach ($DNS in $DNSServers) {
        try {
            if ($DNS.Server) {
                $Result = Resolve-DnsName $Domain -Server $DNS.Server -ErrorAction SilentlyContinue
            } else {
                $Result = Resolve-DnsName $Domain -ErrorAction SilentlyContinue
            }
            
            if ($Result) {
                $IP = $Result.IPAddress -join ', '
                Write-ColorOutput "✅ $($DNS.Name): $IP" "Success"
            } else {
                Write-ColorOutput "❌ $($DNS.Name): No resuelve" "Error"
            }
        }
        catch {
            Write-ColorOutput "❌ $($DNS.Name): Error - $($_.Exception.Message)" "Error"
        }
    }
}

function Compare-Performance {
    param($Results)
    
    Write-ColorOutput "`n📊 Análisis de Rendimiento:" "Info"
    Write-ColorOutput "=" * 50 "Info"
    
    $SuccessfulResults = $Results | Where-Object { $_.Success }
    
    if ($SuccessfulResults.Count -gt 0) {
        $AverageTime = ($SuccessfulResults | Measure-Object -Property ResponseTime -Average).Average
        $FastestService = $SuccessfulResults | Sort-Object ResponseTime | Select-Object -First 1
        $SlowestService = $SuccessfulResults | Sort-Object ResponseTime -Descending | Select-Object -First 1
        
        Write-ColorOutput "⚡ Tiempo promedio de respuesta: $([math]::Round($AverageTime, 2))ms" "Highlight"
        Write-ColorOutput "🚀 Servicio más rápido: $($FastestService.Service) ($([math]::Round($FastestService.ResponseTime, 2))ms)" "Success"
        Write-ColorOutput "🐌 Servicio más lento: $($SlowestService.Service) ($([math]::Round($SlowestService.ResponseTime, 2))ms)" "Warning"
        
        # Clasificación de rendimiento
        $ExcellentCount = ($SuccessfulResults | Where-Object { $_.ResponseTime -lt 500 }).Count
        $GoodCount = ($SuccessfulResults | Where-Object { $_.ResponseTime -ge 500 -and $_.ResponseTime -lt 1000 }).Count
        $SlowCount = ($SuccessfulResults | Where-Object { $_.ResponseTime -ge 1000 }).Count
        
        Write-ColorOutput "`n🏆 Clasificación de rendimiento:" "Info"
        Write-ColorOutput "   Excelente (<500ms): $ExcellentCount servicios" "Success"
        Write-ColorOutput "   Bueno (500-1000ms): $GoodCount servicios" "Warning"
        Write-ColorOutput "   Lento (>1000ms): $SlowCount servicios" "Error"
    }
}

# Servicios a verificar
$Services = @(
    @{Subdomain="home"; Port=3001; Name="Homepage Dashboard"},
    @{Subdomain="flix"; Port=8096; Name="Jellyfin"},
    @{Subdomain="torrent"; Port=8091; Name="qBittorrent"},
    @{Subdomain="sonarr"; Port=8989; Name="Sonarr"},
    @{Subdomain="radarr"; Port=7878; Name="Radarr"},
    @{Subdomain="jackett"; Port=9117; Name="Jackett"},
    @{Subdomain="jellyseerr"; Port=5055; Name="Jellyseerr"},
    @{Subdomain="guard"; Port=8080; Name="AdGuard Home"},
    @{Subdomain="prowlarr"; Port=9696; Name="Prowlarr"}
)

# Inicio del script
Clear-Host
Write-ColorOutput "🚀 Verificación de Migración a Cloudflare" "Info"
Write-ColorOutput "=" * 50 "Info"
Write-ColorOutput "🌐 Dominio base: $Domain" "Highlight"
Write-ColorOutput "📅 Fecha: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" "Info"
Write-ColorOutput ""

# Verificar propagación DNS si se solicita
if ($All -or $Detailed) {
    Test-DNSPropagation -Domain $Domain
    Write-ColorOutput ""
}

# Verificar servicios
Write-ColorOutput "🔍 Verificando servicios..." "Info"
Write-ColorOutput ""

$Results = @()
$SuccessCount = 0
$TotalServices = $Services.Count

foreach ($Service in $Services) {
    $Result = Test-CloudflareService -Subdomain $Service.Subdomain -Port $Service.Port -ServiceName $Service.Name -Detailed:$Detailed -Performance:$Performance
    
    if ($Performance) {
        $Results += $Result
        if ($Result.Success) { $SuccessCount++ }
    } else {
        if ($Result) { $SuccessCount++ }
    }
    
    Start-Sleep -Seconds 1
}

# Resumen
Write-ColorOutput ""
Write-ColorOutput "📊 Resumen de verificación:" "Info"
Write-ColorOutput "=" * 30 "Info"
Write-ColorOutput "✅ Servicios funcionando: $SuccessCount/$TotalServices" "Success"

$SuccessPercentage = [math]::Round(($SuccessCount / $TotalServices) * 100, 2)
Write-ColorOutput "📈 Porcentaje de éxito: $SuccessPercentage%" "Highlight"

if ($SuccessCount -eq $TotalServices) {
    Write-ColorOutput "🎉 ¡Migración completada exitosamente!" "Success"
} elseif ($SuccessCount -gt ($TotalServices * 0.8)) {
    Write-ColorOutput "⚠️  Migración mayormente exitosa, algunos servicios requieren atención" "Warning"
} else {
    Write-ColorOutput "❌ Migración requiere revisión, varios servicios no funcionan" "Error"
}

# Análisis de rendimiento
if ($Performance -and $Results.Count -gt 0) {
    Compare-Performance -Results $Results
}

# Recomendaciones
Write-ColorOutput "`n💡 Recomendaciones:" "Info"
if ($SuccessCount -lt $TotalServices) {
    Write-ColorOutput "   • Verificar configuración DNS en Cloudflare" "Warning"
    Write-ColorOutput "   • Comprobar que los servicios estén ejecutándose localmente" "Warning"
    Write-ColorOutput "   • Revisar logs de Caddy para errores específicos" "Warning"
}

if ($SuccessCount -gt 0) {
    Write-ColorOutput "   • Configurar Page Rules en Cloudflare para optimizar rendimiento" "Info"
    Write-ColorOutput "   • Habilitar compresión Brotli en Cloudflare" "Info"
    Write-ColorOutput "   • Configurar alertas de uptime" "Info"
}

Write-ColorOutput "`n🔧 Para más detalles, ejecuta:" "Info"
Write-ColorOutput "   .\verify-cloudflare-migration.ps1 -Domain '$Domain' -Detailed -Performance" "Highlight"

Write-ColorOutput "`n✅ Verificación completada." "Success"
