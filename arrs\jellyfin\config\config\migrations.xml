<?xml version="1.0" encoding="utf-8"?>
<MigrationOptions xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
  <Applied>
    <ValueTupleOfGuidString>
      <Item1>9b354818-94d5-4b68-ac49-e35cb85f9d84</Item1>
      <Item2>CreateNetworkConfiguration</Item2>
    </ValueTupleOfGuidString>
    <ValueTupleOfGuidString>
      <Item1>a6dcacf4-c057-4ef9-80d3-61cef9ddb4f0</Item1>
      <Item2>MigrateMusicBrainzTimeout</Item2>
    </ValueTupleOfGuidString>
    <ValueTupleOfGuidString>
      <Item1>4fb5c950-1991-11ee-9b4b-0800200c9a66</Item1>
      <Item2>MigrateNetworkConfiguration</Item2>
    </ValueTupleOfGuidString>
    <ValueTupleOfGuidString>
      <Item1>a8e61960-7726-4450-8f3d-82c12daabbcb</Item1>
      <Item2>MigrateEncodingOptions</Item2>
    </ValueTupleOfGuidString>
    <ValueTupleOfGuidString>
      <Item1>4124c2cd-e939-4ffb-9be9-9b311c413638</Item1>
      <Item2>DisableTranscodingThrottling</Item2>
    </ValueTupleOfGuidString>
    <ValueTupleOfGuidString>
      <Item1>ef103419-8451-40d8-9f34-d1a8e93a1679</Item1>
      <Item2>CreateLoggingConfigHeirarchy</Item2>
    </ValueTupleOfGuidString>
    <ValueTupleOfGuidString>
      <Item1>3793eb59-bc8c-456c-8b9f-bd5a62a42978</Item1>
      <Item2>MigrateActivityLogDatabase</Item2>
    </ValueTupleOfGuidString>
    <ValueTupleOfGuidString>
      <Item1>acbe17b7-8435-4a83-8b64-6fcf162cb9bd</Item1>
      <Item2>RemoveDuplicateExtras</Item2>
    </ValueTupleOfGuidString>
    <ValueTupleOfGuidString>
      <Item1>5c4b82a2-f053-4009-bd05-b6fcad82f14c</Item1>
      <Item2>MigrateUserDatabase</Item2>
    </ValueTupleOfGuidString>
    <ValueTupleOfGuidString>
      <Item1>06387815-c3cc-421f-a888-fb5f9992bea8</Item1>
      <Item2>MigrateDisplayPreferencesDatabase</Item2>
    </ValueTupleOfGuidString>
    <ValueTupleOfGuidString>
      <Item1>a81f75e0-8f43-416f-a5e8-516ccab4d8cc</Item1>
      <Item2>RemoveDownloadImagesInAdvance</Item2>
    </ValueTupleOfGuidString>
    <ValueTupleOfGuidString>
      <Item1>5bd72f41-e6f3-4f60-90aa-09869abe0e22</Item1>
      <Item2>MigrateAuthenticationDatabase</Item2>
    </ValueTupleOfGuidString>
    <ValueTupleOfGuidString>
      <Item1>615dfa9e-2497-4dbb-a472-61938b752c5b</Item1>
      <Item2>FixPlaylistOwner</Item2>
    </ValueTupleOfGuidString>
    <ValueTupleOfGuidString>
      <Item1>d34bfc33-5d2e-4790-8085-069ef6eecb4e</Item1>
      <Item2>MigrateRatingLevels</Item2>
    </ValueTupleOfGuidString>
    <ValueTupleOfGuidString>
      <Item1>cf6fabc2-9fbe-4933-84a5-ffe52ef22a58</Item1>
      <Item2>FixAudioData</Item2>
    </ValueTupleOfGuidString>
    <ValueTupleOfGuidString>
      <Item1>96c156a2-7a13-4b3b-a8b8-fb80c94d20c0</Item1>
      <Item2>RemoveDuplicatePlaylistChildren</Item2>
    </ValueTupleOfGuidString>
    <ValueTupleOfGuidString>
      <Item1>eb58ebee-9514-4b9b-8225-12e1a40020df</Item1>
      <Item2>AddDefaultPluginRepository</Item2>
    </ValueTupleOfGuidString>
    <ValueTupleOfGuidString>
      <Item1>5f86e7f6-d966-4c77-849d-7a7b40b68c4e</Item1>
      <Item2>ReaddDefaultPluginRepository</Item2>
    </ValueTupleOfGuidString>
    <ValueTupleOfGuidString>
      <Item1>34a1a1c4-5572-418e-a2f8-32cdfe2668e8</Item1>
      <Item2>AddDefaultCastReceivers</Item2>
    </ValueTupleOfGuidString>
    <ValueTupleOfGuidString>
      <Item1>852816e0-2712-49a9-9240-c6fc5fcad1a8</Item1>
      <Item2>UpdateDefaultPluginRepository10.9</Item2>
    </ValueTupleOfGuidString>
    <ValueTupleOfGuidString>
      <Item1>4ef123d5-8eff-4b0b-869d-3aed07a60e1b</Item1>
      <Item2>MoveTrickplayFiles</Item2>
    </ValueTupleOfGuidString>
  </Applied>
</MigrationOptions>