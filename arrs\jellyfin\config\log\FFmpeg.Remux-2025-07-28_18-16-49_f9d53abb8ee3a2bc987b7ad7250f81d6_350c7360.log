{"Protocol":0,"Id":"f9d53abb8ee3a2bc987b7ad7250f81d6","Path":"/CONTENIDO/SERIES/Dexter/Season 4/Dexter - S04E07 - Slack Tide Bluray-1080p.mp4","EncoderPath":null,"EncoderProtocol":null,"Type":0,"Container":"mov,mp4,m4a,3gp,3g2,mj2","Size":2446460386,"Name":"Dexter - S04E07 - Slack Tide Bluray-1080p","IsRemote":false,"ETag":"67c1ec09837261a6ebbaabb9486afbf9","RunTimeTicks":31612586670,"ReadAtNativeFramerate":false,"IgnoreDts":false,"IgnoreIndex":false,"GenPtsInput":false,"SupportsTranscoding":true,"SupportsDirectStream":true,"SupportsDirectPlay":true,"IsInfiniteStream":false,"UseMostCompatibleTranscodingProfile":false,"RequiresOpening":false,"OpenToken":null,"RequiresClosing":false,"LiveStreamId":null,"BufferMs":null,"RequiresLooping":false,"SupportsProbing":true,"VideoType":0,"IsoType":null,"Video3DFormat":null,"MediaStreams":[{"Codec":"h264","CodecTag":"avc1","Language":"und","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/90000","CodecTimeBase":null,"Title":null,"VideoRange":1,"VideoRangeType":1,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":null,"LocalizedDefault":null,"LocalizedForced":null,"LocalizedExternal":null,"LocalizedHearingImpaired":null,"DisplayTitle":"1080p H264 SDR","NalLengthSize":"4","IsInterlaced":false,"IsAVC":true,"ChannelLayout":null,"BitRate":5928156,"BitDepth":8,"RefFrames":1,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":true,"IsForced":false,"IsHearingImpaired":false,"Height":1080,"Width":1920,"AverageFrameRate":23.976025,"RealFrameRate":23.976025,"ReferenceFrameRate":23.976025,"Profile":"High","Type":1,"AspectRatio":"16:9","Index":0,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":false,"Path":null,"PixelFormat":"yuv420p","Level":40,"IsAnamorphic":false},{"Codec":"aac","CodecTag":"mp4a","Language":"eng","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/48000","CodecTimeBase":null,"Title":null,"VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":null,"LocalizedDefault":"Predeterminado","LocalizedForced":null,"LocalizedExternal":"Externo","LocalizedHearingImpaired":null,"DisplayTitle":"English - HE-AAC - Stereo - Predeterminado","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":"stereo","BitRate":127999,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":2,"SampleRate":48000,"IsDefault":true,"IsForced":false,"IsHearingImpaired":false,"Height":null,"Width":null,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":"HE-AAC","Type":0,"AspectRatio":null,"Index":1,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":false,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"aac","CodecTag":"mp4a","Language":"spa","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/48000","CodecTimeBase":null,"Title":null,"VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":null,"LocalizedDefault":"Predeterminado","LocalizedForced":null,"LocalizedExternal":"Externo","LocalizedHearingImpaired":null,"DisplayTitle":"Spanish - HE-AAC - Stereo","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":"stereo","BitRate":127999,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":2,"SampleRate":48000,"IsDefault":false,"IsForced":false,"IsHearingImpaired":false,"Height":null,"Width":null,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":"HE-AAC","Type":0,"AspectRatio":null,"Index":2,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":false,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"mov_text","CodecTag":"tx3g","Language":"spa","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/90000","CodecTimeBase":null,"Title":null,"VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":"Indefinido","LocalizedDefault":"Predeterminado","LocalizedForced":"Forzado","LocalizedExternal":"Externo","LocalizedHearingImpaired":"Discapacidad Auditiva","DisplayTitle":"Spanish - Predeterminado - MOV_TEXT","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":null,"BitRate":70,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":true,"IsForced":false,"IsHearingImpaired":false,"Height":0,"Width":0,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":2,"AspectRatio":null,"Index":3,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":true,"SupportsExternalStream":true,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"mov_text","CodecTag":"tx3g","Language":"eng","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/90000","CodecTimeBase":null,"Title":null,"VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":"Indefinido","LocalizedDefault":"Predeterminado","LocalizedForced":"Forzado","LocalizedExternal":"Externo","LocalizedHearingImpaired":"Discapacidad Auditiva","DisplayTitle":"English - MOV_TEXT","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":null,"BitRate":71,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":false,"IsForced":false,"IsHearingImpaired":false,"Height":0,"Width":0,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":2,"AspectRatio":null,"Index":4,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":true,"SupportsExternalStream":true,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"mjpeg","CodecTag":null,"Language":null,"ColorRange":null,"ColorSpace":"bt470bg","ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/90000","CodecTimeBase":null,"Title":null,"VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":null,"LocalizedDefault":null,"LocalizedForced":null,"LocalizedExternal":null,"LocalizedHearingImpaired":null,"DisplayTitle":null,"NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":null,"BitRate":null,"BitDepth":8,"RefFrames":1,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":false,"IsForced":false,"IsHearingImpaired":false,"Height":197,"Width":350,"AverageFrameRate":null,"RealFrameRate":90000,"ReferenceFrameRate":90000,"Profile":"Baseline","Type":3,"AspectRatio":"16:9","Index":5,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":false,"Path":null,"PixelFormat":"yuvj420p","Level":-99,"IsAnamorphic":false}],"MediaAttachments":[{"Codec":"mjpeg","CodecTag":"[0][0][0][0]","Comment":null,"Index":5,"FileName":null,"MimeType":null,"DeliveryUrl":null}],"Formats":[],"Bitrate":6191104,"FallbackMaxStreamingBitrate":null,"Timestamp":null,"RequiredHttpHeaders":{},"TranscodingUrl":null,"TranscodingSubProtocol":0,"TranscodingContainer":null,"AnalyzeDurationMs":null,"DefaultAudioStreamIndex":null,"DefaultSubtitleStreamIndex":null,"HasSegments":false}

/usr/lib/jellyfin-ffmpeg/ffmpeg -analyzeduration 200M -probesize 1G -ss 00:38:18.500 -noaccurate_seek -fflags +genpts -f mov,mp4,m4a,3gp,3g2,mj2  -i file:"/CONTENIDO/SERIES/Dexter/Season 4/Dexter - S04E07 - Slack Tide Bluray-1080p.mp4" -map_metadata -1 -map_chapters -1 -threads 0 -map 0:0 -map 0:2 -map -0:s -codec:v:0 copy -bsf:v h264_mp4toannexb -start_at_zero -codec:a:0 copy -copyts -avoid_negative_ts disabled -max_muxing_queue_size 2048 -f hls -max_delay 5000000 -hls_time 6 -hls_segment_type fmp4 -hls_fmp4_init_filename "5105ec3aab253053f33ba06a42e100c6-1.mp4" -start_number 383 -hls_segment_filename "/cache/transcodes/5105ec3aab253053f33ba06a42e100c6%d.mp4" -hls_playlist_type vod -hls_list_size 0 -y "/cache/transcodes/5105ec3aab253053f33ba06a42e100c6.m3u8"


ffmpeg version 7.0.2-Jellyfin Copyright (c) 2000-2024 the FFmpeg developers
  built with gcc 12 (Debian 12.2.0-14)
  configuration: --prefix=/usr/lib/jellyfin-ffmpeg --target-os=linux --extra-version=Jellyfin --disable-doc --disable-ffplay --disable-ptx-compression --disable-static --disable-libxcb --disable-sdl2 --disable-xlib --enable-lto=auto --enable-gpl --enable-version3 --enable-shared --enable-gmp --enable-gnutls --enable-chromaprint --enable-opencl --enable-libdrm --enable-libxml2 --enable-libass --enable-libfreetype --enable-libfribidi --enable-libfontconfig --enable-libharfbuzz --enable-libbluray --enable-libmp3lame --enable-libopus --enable-libtheora --enable-libvorbis --enable-libopenmpt --enable-libdav1d --enable-libsvtav1 --enable-libwebp --enable-libvpx --enable-libx264 --enable-libx265 --enable-libzvbi --enable-libzimg --enable-libfdk-aac --arch=amd64 --enable-libshaderc --enable-libplacebo --enable-vulkan --enable-vaapi --enable-amf --enable-libvpl --enable-ffnvcodec --enable-cuda --enable-cuda-llvm --enable-cuvid --enable-nvdec --enable-nvenc
  libavutil      59.  8.100 / 59.  8.100
  libavcodec     61.  3.100 / 61.  3.100
  libavformat    61.  1.100 / 61.  1.100
  libavdevice    61.  1.100 / 61.  1.100
  libavfilter    10.  1.100 / 10.  1.100
  libswscale      8.  1.100 /  8.  1.100
  libswresample   5.  1.100 /  5.  1.100
  libpostproc    58.  1.100 / 58.  1.100
[mov,mp4,m4a,3gp,3g2,mj2 @ 0x648086e578c0] stream 0, timescale not set
Input #0, mov,mp4,m4a,3gp,3g2,mj2, from 'file:/CONTENIDO/SERIES/Dexter/Season 4/Dexter - S04E07 - Slack Tide Bluray-1080p.mp4':
  Metadata:
    major_brand     : isom
    minor_version   : 512
    compatible_brands: isomiso2avc1mp41
    title           : Dexter [S04.E07] - Marea Perezosa
    encoder         : Lavf60.16.100
    description     : Dexter está cómodo con su vida, pero se siente frustrado en la búsqueda de una nueva víctima. Debra tiene un renovado interés por el turbio pasado de su padre.
    episode_id      : 7/4
  Duration: 00:52:41.26, start: 0.000000, bitrate: 6191 kb/s
  Stream #0:0[0x1](und): Video: h264 (High) (avc1 / 0x31637661), yuv420p(progressive), 1920x1080, 5928 kb/s, 23.98 fps, 23.98 tbr, 90k tbn (default)
      Metadata:
        handler_name    : VideoHandler
        vendor_id       : [0][0][0][0]
  Stream #0:1[0x2](eng): Audio: aac (HE-AAC) (mp4a / 0x6134706D), 48000 Hz, stereo, fltp, 127 kb/s (default)
      Metadata:
        handler_name    : SoundHandler
        vendor_id       : [0][0][0][0]
  Stream #0:2[0x3](spa): Audio: aac (HE-AAC) (mp4a / 0x6134706D), 48000 Hz, stereo, fltp, 127 kb/s
      Metadata:
        handler_name    : SoundHandler
        vendor_id       : [0][0][0][0]
  Stream #0:3[0x4](spa): Subtitle: mov_text (tx3g / 0x67337874), 0 kb/s (default)
      Metadata:
        handler_name    : SubtitleHandler
  Stream #0:4[0x5](eng): Subtitle: mov_text (tx3g / 0x67337874), 0 kb/s
      Metadata:
        handler_name    : SubtitleHandler
  Stream #0:5[0x0]: Video: mjpeg (Baseline), yuvj420p(pc, bt470bg/unknown/unknown), 350x197, 90k tbr, 90k tbn (attached pic)
[out#0/hls @ 0x648086ee8f40] Codec AVOption threads (set the number of threads) has not been used for any stream. The most likely reason is either wrong type (e.g. a video option with no video streams) or that it is a private option of some encoder which was not actually used for any stream.
Stream mapping:
  Stream #0:0 -> #0:0 (copy)
  Stream #0:2 -> #0:1 (copy)
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6-1.mp4' for writing
Output #0, hls, to '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6.m3u8':
  Metadata:
    encoder         : Lavf61.1.100
  Stream #0:0: Video: h264 (High) (avc1 / 0x31637661), yuv420p(progressive), 1920x1080, q=2-31, 5928 kb/s, 23.98 fps, 23.98 tbr, 90k tbn (default)
  Stream #0:1: Audio: aac (HE-AAC) (mp4a / 0x6134706D), 48000 Hz, stereo, fltp, 127 kb/s
Press [q] to stop, [?] for help
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6383.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6384.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6385.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6386.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6387.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6388.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6389.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6390.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6391.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6392.mp4' for writing
size=N/A time=00:00:00.00 bitrate=N/A speed=   0x    
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6393.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6394.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6395.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6396.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6397.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6398.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6399.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6400.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6401.mp4' for writing
size=N/A time=00:00:43.75 bitrate=N/A speed=43.7x    
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6402.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6403.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6404.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6405.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6406.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6407.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6408.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6409.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6410.mp4' for writing
size=N/A time=00:01:17.36 bitrate=N/A speed=51.6x    
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6411.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6412.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6413.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6414.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6415.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6416.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6417.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6418.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6419.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6420.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6421.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6422.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6423.mp4' for writing
size=N/A time=00:01:59.24 bitrate=N/A speed=59.6x    
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6424.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6425.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6426.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6427.mp4' for writing
size=N/A time=00:02:22.76 bitrate=N/A speed=57.1x    
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6428.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6429.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6430.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6431.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6432.mp4' for writing
size=N/A time=00:02:53.38 bitrate=N/A speed=57.8x    
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6433.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6434.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6435.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6436.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6437.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6438.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6439.mp4' for writing
size=N/A time=00:03:24.01 bitrate=N/A speed=58.3x    
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6440.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6441.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6442.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6443.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6444.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6445.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6446.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6447.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6448.mp4' for writing
size=N/A time=00:03:49.52 bitrate=N/A speed=57.3x    
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6449.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6450.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6451.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6452.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6453.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6454.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6455.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6456.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6457.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6458.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6459.mp4' for writing
size=N/A time=00:04:24.80 bitrate=N/A speed=58.8x    
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6460.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6461.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6462.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6463.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6464.mp4' for writing
size=N/A time=00:04:41.82 bitrate=N/A speed=56.3x    
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6465.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6466.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6467.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6468.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6469.mp4' for writing
size=N/A time=00:04:59.34 bitrate=N/A speed=54.4x    
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6470.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6471.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6472.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6473.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6474.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6475.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6476.mp4' for writing
size=N/A time=00:05:12.77 bitrate=N/A speed=52.1x    
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6477.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6478.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6479.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6480.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6481.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6482.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6483.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6484.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6485.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6486.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6487.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6488.mp4' for writing
size=N/A time=00:05:32.95 bitrate=N/A speed=51.2x    
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6489.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6490.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6491.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6492.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6493.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6494.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6495.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6496.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6497.mp4' for writing
size=N/A time=00:05:54.56 bitrate=N/A speed=50.6x    
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6498.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6499.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6500.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6501.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6502.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6503.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6504.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6505.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6506.mp4' for writing
size=N/A time=00:06:12.88 bitrate=N/A speed=49.7x    
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6507.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6508.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6509.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6510.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6511.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6512.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6513.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6514.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6515.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6516.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6517.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6518.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6519.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6520.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6521.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6522.mp4' for writing
size=N/A time=00:06:52.35 bitrate=N/A speed=51.5x    
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6523.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6524.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6525.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6526.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6527.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6528.mp4' for writing
size=N/A time=00:07:18.18 bitrate=N/A speed=51.5x    
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6529.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6530.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6531.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6532.mp4' for writing
size=N/A time=00:07:47.50 bitrate=N/A speed=51.9x    
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6533.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6534.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6535.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6536.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6537.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6538.mp4' for writing
size=N/A time=00:08:01.98 bitrate=N/A speed=50.7x    
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6539.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6540.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6541.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6542.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6543.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6544.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6545.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6546.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6547.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6548.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6549.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6550.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6551.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6552.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6553.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6554.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6555.mp4' for writing
size=N/A time=00:08:29.71 bitrate=N/A speed=50.9x    
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6556.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6557.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6558.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6559.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6560.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6561.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6562.mp4' for writing
size=N/A time=00:09:14.09 bitrate=N/A speed=52.7x    
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6563.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6564.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6565.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6566.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6567.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6568.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6569.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6570.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6571.mp4' for writing
size=N/A time=00:09:33.65 bitrate=N/A speed=52.1x    
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6572.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6573.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6574.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6575.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6576.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6577.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6578.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6579.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6580.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6581.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6582.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6583.mp4' for writing
size=N/A time=00:10:01.55 bitrate=N/A speed=52.3x    
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6584.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6585.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6586.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6587.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6588.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6589.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6590.mp4' for writing
size=N/A time=00:10:22.24 bitrate=N/A speed=51.8x    
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6591.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6592.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6593.mp4' for writing
size=N/A time=00:10:48.98 bitrate=N/A speed=51.9x    
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6594.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6595.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6596.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6597.mp4' for writing
size=N/A time=00:11:09.76 bitrate=N/A speed=51.5x    
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6598.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6599.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6600.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6601.mp4' for writing
size=N/A time=00:11:31.64 bitrate=N/A speed=51.2x    
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6602.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6603.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6604.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6605.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6606.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6607.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6608.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6609.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6610.mp4' for writing
size=N/A time=00:12:10.77 bitrate=N/A speed=52.2x    
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6611.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6612.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6613.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6614.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6615.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6616.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6617.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6618.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6619.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6620.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6621.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6622.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6623.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6624.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6625.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6626.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6627.mp4' for writing
size=N/A time=00:13:43.36 bitrate=N/A speed=56.7x    
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6628.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6629.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6630.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6631.mp4' for writing
[hls @ 0x648086e6f7c0] Opening '/cache/transcodes/5105ec3aab253053f33ba06a42e100c6632.mp4' for writing
[out#0/hls @ 0x648086ee8f40] video:567496KiB audio:13492KiB subtitle:0KiB other streams:0KiB global headers:0KiB muxing overhead: unknown
size=N/A time=00:13:57.08 bitrate=N/A speed=57.5x    