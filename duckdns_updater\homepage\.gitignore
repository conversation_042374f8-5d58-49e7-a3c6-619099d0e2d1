# Homepage .gitignore - Protección de Datos Sensibles

# === ARCHIVOS DE CONFIGURACIÓN SENSIBLES ===
# API Keys y credenciales
config/api-keys.env
config/*.env
*.env

# Archivos de configuración con datos reales
config/services.yaml
config/settings-local.yaml

# === LOGS Y DATOS TEMPORALES ===
# Logs de Homepage
logs/
*.log
*.log.*

# Archivos temporales
*.tmp
*.temp
.DS_Store
Thumbs.db

# === BACKUPS Y COPIAS ===
# Backups de configuración
*.backup
*.bak
config/*.backup
config/*.bak

# === ARCHIVOS DE SISTEMA ===
# Windows
desktop.ini
$RECYCLE.BIN/

# Linux/Mac
.Trash-*
.directory

# === ARCHIVOS DE DESARROLLO ===
# IDEs
.vscode/
.idea/
*.swp
*.swo
*~

# === CERTIFICADOS Y CLAVES ===
# Certificados SSL
*.crt
*.key
*.pem
*.p12
*.pfx

# === DATOS DE USUARIO ===
# Configuraciones personalizadas con datos sensibles
config/custom-*.yaml
config/personal-*.yaml
config/private-*.yaml

# === MANTENER ARCHIVOS TEMPLATE ===
# Permitir archivos de plantilla
!config/*-template.yaml
!config/*-example.yaml
!config/README.md
