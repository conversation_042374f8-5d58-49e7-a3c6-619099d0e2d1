# Homepage Dashboard - Configuración Completa

## 📋 Descripción
Homepage es un dashboard moderno y personalizable que centraliza el acceso a todos tus servicios Docker. Esta configuración está optimizada para tu stack actual de servicios.

## 🚀 Servicios Integrados

### Servicios Principales
- **Home Assistant** - Automatización del hogar
- **AdGuard Home** - Filtrado DNS y bloqueo de anuncios
- **Jellyfin** - Servidor multimedia
- **qBittorrent** - Cliente torrent
- **Portainer** - Gestión de contenedores Docker

### Stack *ARR
- **Sonarr** - Gestión de series
- **Radarr** - Gestión de películas
- **Jackett** - Indexador de torrents
- **Jellyseerr** - Gestión de solicitudes
- **FlareSolverr** - Solucionador de Cloudflare

### Servicios de Sistema
- **Caddy** - Proxy reverso
- **Watchtower** - Actualizador automático
- **DuckDNS** - DNS dinámico

## 🔧 Configuración Paso a Paso

### 1. Script de Configuración Automática (Recomendado)
```powershell
# Navegar al directorio del proyecto
cd C:\docker\homepage

# Iniciar Homepage
.\setup-homepage.ps1 -Start

# Verificar estado
.\setup-homepage.ps1 -Status

# Ver logs en tiempo real
.\setup-homepage.ps1 -Logs

# Reiniciar si es necesario
.\setup-homepage.ps1 -Restart
```

### 2. Configuración Manual
```bash
cd C:\docker\duckdns_updater
docker-compose up -d homepage
```

### 3. Verificar el Estado
```bash
docker logs homepage
```

### 4. Acceder al Dashboard
- **URL Externa**: https://tankeeee2.duckdns.org
- **URL Local**: http://localhost:3001

### 5. Script de Gestión
El script `setup-homepage.ps1` incluye las siguientes opciones:
- `-Start`: Inicia el servicio
- `-Stop`: Detiene el servicio
- `-Restart`: Reinicia el servicio
- `-Logs`: Muestra logs en tiempo real
- `-Status`: Muestra estado del contenedor
- `-Help`: Muestra ayuda completa

## 🔑 API Keys Necesarias

### Home Assistant
1. **Dónde obtenerla**: 
   - Accede a Home Assistant
   - Ve a Perfil → Tokens de acceso de larga duración
   - Crea un nuevo token
2. **Dónde configurarla**: `config/services.yaml` → `homeassistant.key`

### AdGuard Home
1. **Dónde obtenerla**:
   - Accede a AdGuard Home
   - Ve a Configuración → General → Configuración web
   - Anota usuario y contraseña
2. **Dónde configurarla**: `config/services.yaml` → `adguard.username` y `adguard.password`

### Sonarr
1. **Dónde obtenerla**:
   - Accede a Sonarr
   - Ve a Configuración → General → Seguridad → API Key
2. **Dónde configurarla**: `config/services.yaml` → `sonarr.key`

### Radarr
1. **Dónde obtenerla**:
   - Accede a Radarr
   - Ve a Configuración → General → Seguridad → API Key
2. **Dónde configurarla**: `config/services.yaml` → `radarr.key`

### Jackett
1. **Dónde obtenerla**:
   - Accede a Jackett
   - Copia la API Key de la página principal
2. **Dónde configurarla**: `config/services.yaml` → `jackett.key`

### Jellyseerr
1. **Dónde obtenerla**:
   - Accede a Jellyseerr
   - Ve a Configuración → General → API Key
2. **Dónde configurarla**: `config/services.yaml` → `jellyseerr.key`

### qBittorrent
1. **Configuración**:
   - Usuario y contraseña configurados en qBittorrent
2. **Dónde configurarla**: `config/services.yaml` → `qbittorrent.username` y `qbittorrent.password`

## 📁 Estructura de Archivos

```
duckdns_updater/homepage/config/
├── settings.yaml      # Configuración general
├── services.yaml      # Definición de servicios
├── widgets.yaml       # Widgets del dashboard
├── bookmarks.yaml     # Marcadores
└── docker.yaml        # Configuración de Docker
```

## 🔒 Archivo de Configuración Centralizado

Todas las API keys se gestionan a través del archivo `config/api-keys.env`. Este archivo:

- Centraliza todas las credenciales en un solo lugar
- Está protegido por .gitignore para evitar subidas accidentales
- Usa variables de entorno para mayor seguridad
- Incluye documentación detallada para cada API key

### Configuración de API Keys

1. **Edita el archivo de configuración**:
   ```bash
   notepad duckdns_updater\homepage\config\api-keys.env
   ```

2. **Reemplaza cada placeholder** con la API key real del servicio correspondiente

3. **Reinicia Homepage** para aplicar los cambios:
   ```powershell
   .\homepage\setup-homepage.ps1 -Restart
   ```

### Archivos de Configuración

- `config/api-keys.env` - Variables de entorno con API keys
- `config/services.yaml` - Definición de servicios (usa placeholders)
- `config/services-template.yaml` - Plantilla con variables de entorno
- `config/settings.yaml` - Configuración general del dashboard
- `config/widgets.yaml` - Widgets del sistema y monitoreo
- `config/bookmarks.yaml` - Marcadores organizados por categorías
- `config/docker.yaml` - Configuración de integración con Docker

## 🛠️ Solución de Problemas

### El servicio no inicia
```bash
docker logs homepage
docker-compose restart homepage
```

### No se muestran los widgets
1. Verifica que las API keys sean correctas
2. Comprueba la conectividad de red entre contenedores
3. Revisa los logs para errores específicos

### Problemas de permisos
```bash
# En Windows, asegúrate de que Docker tenga acceso a los volúmenes
docker-compose down homepage
docker-compose up -d homepage
```

## 📊 Widgets Disponibles

- **Estado de servicios** - Monitoreo en tiempo real
- **Estadísticas de sistema** - CPU, RAM, almacenamiento
- **Información de Docker** - Estado de contenedores
- **Estadísticas de *ARR** - Descargas y biblioteca
- **Métricas de AdGuard** - Consultas DNS bloqueadas

## 🔄 Actualizaciones

El servicio se actualiza automáticamente gracias a Watchtower. Para actualizaciones manuales:

```bash
docker-compose pull homepage
docker-compose up -d homepage
```

## 📞 Soporte

Si encuentras problemas:
1. Revisa los logs del contenedor
2. Verifica la configuración de red
3. Comprueba que todos los servicios estén ejecutándose
4. Valida las API keys en los servicios correspondientes
