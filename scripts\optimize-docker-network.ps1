# optimize-docker-network.ps1
# Script para optimizar la configuración de red de Docker y solucionar problemas de conectividad

param(
    [switch]$Analyze,
    [switch]$Fix,
    [switch]$Reset,
    [switch]$All
)

# Configuración de colores
$Colors = @{
    Success = "Green"
    Error = "Red"
    Warning = "Yellow"
    Info = "Blue"
    Highlight = "Cyan"
}

function Write-ColorOutput {
    param($Message, $Color = "White")
    Write-Host $Message -ForegroundColor $Colors[$Color]
}

function Analyze-DockerNetworks {
    Write-ColorOutput "🌐 Analizando redes de Docker..." "Info"
    
    try {
        # Listar todas las redes
        $Networks = docker network ls --format "table {{.Name}}\t{{.Driver}}\t{{.Scope}}" 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-ColorOutput "📊 Redes de Docker existentes:" "Info"
            $Networks | ForEach-Object { Write-ColorOutput "   $_" "Highlight" }
        } else {
            Write-ColorOutput "❌ Error listando redes de Docker" "Error"
            return $false
        }
        
        # Verificar redes específicas de nuestro stack
        $RequiredNetworks = @("caddy_network", "arr_net")
        $MissingNetworks = @()
        
        foreach ($Network in $RequiredNetworks) {
            $NetworkInfo = docker network inspect $Network 2>$null
            if ($LASTEXITCODE -eq 0) {
                Write-ColorOutput "   ✅ Red $Network existe" "Success"
                
                # Analizar contenedores conectados
                $NetworkDetails = $NetworkInfo | ConvertFrom-Json
                $ConnectedContainers = $NetworkDetails.Containers
                
                if ($ConnectedContainers) {
                    Write-ColorOutput "   📦 Contenedores en $Network:" "Info"
                    $ConnectedContainers.PSObject.Properties | ForEach-Object {
                        $ContainerName = $_.Value.Name
                        $ContainerIP = $_.Value.IPv4Address
                        Write-ColorOutput "      • $ContainerName ($ContainerIP)" "Highlight"
                    }
                } else {
                    Write-ColorOutput "   ⚠️  No hay contenedores conectados a $Network" "Warning"
                }
            } else {
                Write-ColorOutput "   ❌ Red $Network no existe" "Error"
                $MissingNetworks += $Network
            }
        }
        
        return $MissingNetworks
    }
    catch {
        Write-ColorOutput "❌ Error analizando redes: $($_.Exception.Message)" "Error"
        return @("error")
    }
}

function Test-ContainerConnectivity {
    Write-ColorOutput "🔗 Probando conectividad entre contenedores..." "Info"
    
    # Contenedores críticos para probar
    $TestCases = @(
        @{From="homepage"; To="jellyfin"; Port=8096},
        @{From="homepage"; To="sonarr"; Port=8989},
        @{From="homepage"; To="radarr"; Port=7878},
        @{From="homepage"; To="qbittorrent"; Port=8091},
        @{From="homepage"; To="jackett"; Port=9117},
        @{From="caddy_proxy"; To="jellyfin"; Port=8096},
        @{From="caddy_proxy"; To="homepage"; Port=3000}
    )
    
    $FailedConnections = @()
    
    foreach ($Test in $TestCases) {
        try {
            Write-ColorOutput "   🔍 Probando: $($Test.From) → $($Test.To):$($Test.Port)" "Info"
            
            # Verificar que ambos contenedores existan
            $FromExists = docker ps --filter "name=$($Test.From)" --format "{{.Names}}" 2>$null
            $ToExists = docker ps --filter "name=$($Test.To)" --format "{{.Names}}" 2>$null
            
            if (-not $FromExists) {
                Write-ColorOutput "      ⚠️  Contenedor origen $($Test.From) no está ejecutándose" "Warning"
                continue
            }
            
            if (-not $ToExists) {
                Write-ColorOutput "      ⚠️  Contenedor destino $($Test.To) no está ejecutándose" "Warning"
                continue
            }
            
            # Probar conectividad usando host.docker.internal (método preferido en Windows)
            $TestResult = docker exec $($Test.From) sh -c "wget -q --spider --timeout=5 http://host.docker.internal:$($Test.Port) 2>/dev/null && echo 'OK' || echo 'FAIL'" 2>$null
            
            if ($TestResult -eq "OK") {
                Write-ColorOutput "      ✅ Conectividad OK" "Success"
            } else {
                Write-ColorOutput "      ❌ Conectividad FAIL" "Error"
                $FailedConnections += "$($Test.From) → $($Test.To):$($Test.Port)"
            }
        }
        catch {
            Write-ColorOutput "      ❌ Error probando conectividad: $($_.Exception.Message)" "Error"
            $FailedConnections += "$($Test.From) → $($Test.To):$($Test.Port)"
        }
        
        Start-Sleep -Seconds 1
    }
    
    return $FailedConnections
}

function Fix-DockerNetworks {
    Write-ColorOutput "🔧 Reparando redes de Docker..." "Info"
    
    try {
        # Recrear red caddy_network si no existe
        $CaddyNetwork = docker network inspect caddy_network 2>$null
        if ($LASTEXITCODE -ne 0) {
            Write-ColorOutput "   🔨 Creando red caddy_network..." "Info"
            docker network create caddy_network --driver bridge
            if ($LASTEXITCODE -eq 0) {
                Write-ColorOutput "   ✅ Red caddy_network creada" "Success"
            } else {
                Write-ColorOutput "   ❌ Error creando red caddy_network" "Error"
            }
        }
        
        # Recrear red arr_net si no existe
        $ArrNetwork = docker network inspect arr_net 2>$null
        if ($LASTEXITCODE -ne 0) {
            Write-ColorOutput "   🔨 Creando red arr_net..." "Info"
            docker network create arr_net --driver bridge
            if ($LASTEXITCODE -eq 0) {
                Write-ColorOutput "   ✅ Red arr_net creada" "Success"
            } else {
                Write-ColorOutput "   ❌ Error creando red arr_net" "Error"
            }
        }
        
        return $true
    }
    catch {
        Write-ColorOutput "❌ Error reparando redes: $($_.Exception.Message)" "Error"
        return $false
    }
}

function Optimize-DockerDNS {
    Write-ColorOutput "🌐 Optimizando configuración DNS de Docker..." "Info"
    
    try {
        # Verificar configuración DNS de Docker
        $DockerInfo = docker system info --format "{{.SystemStatus}}" 2>$null
        
        Write-ColorOutput "   💡 Recomendaciones para DNS:" "Info"
        Write-ColorOutput "      • Usar host.docker.internal para comunicación entre contenedores" "Highlight"
        Write-ColorOutput "      • Configurar DNS personalizado si es necesario" "Highlight"
        Write-ColorOutput "      • Verificar que Docker Desktop tenga acceso a DNS" "Highlight"
        
        # Verificar resolución DNS desde contenedores
        $TestContainers = @("homepage", "caddy_proxy")
        
        foreach ($Container in $TestContainers) {
            $ContainerExists = docker ps --filter "name=$Container" --format "{{.Names}}" 2>$null
            if ($ContainerExists) {
                Write-ColorOutput "   🔍 Probando DNS en $Container..." "Info"
                
                # Probar resolución de host.docker.internal
                $DNSTest = docker exec $Container sh -c "nslookup host.docker.internal 2>/dev/null | grep -q 'Address:' && echo 'OK' || echo 'FAIL'" 2>$null
                if ($DNSTest -eq "OK") {
                    Write-ColorOutput "      ✅ DNS funcionando en $Container" "Success"
                } else {
                    Write-ColorOutput "      ⚠️  DNS puede tener problemas en $Container" "Warning"
                }
            }
        }
        
        return $true
    }
    catch {
        Write-ColorOutput "❌ Error optimizando DNS: $($_.Exception.Message)" "Error"
        return $false
    }
}

function Reset-DockerNetworking {
    Write-ColorOutput "🔄 Reiniciando configuración de red de Docker..." "Warning"
    
    $Confirmation = Read-Host "⚠️  Esto reiniciará todos los contenedores. ¿Continuar? (s/N)"
    if ($Confirmation -ne 's' -and $Confirmation -ne 'S') {
        Write-ColorOutput "❌ Operación cancelada" "Error"
        return $false
    }
    
    try {
        # Detener todos los servicios
        Write-ColorOutput "   🛑 Deteniendo servicios..." "Info"
        Set-Location "C:\docker\arrs"
        docker-compose down
        
        Set-Location "C:\docker\duckdns_updater"
        docker-compose down
        
        # Limpiar redes huérfanas
        Write-ColorOutput "   🧹 Limpiando redes huérfanas..." "Info"
        docker network prune -f
        
        # Recrear redes principales
        Write-ColorOutput "   🔨 Recreando redes..." "Info"
        docker network rm caddy_network 2>$null
        docker network rm arr_net 2>$null
        
        docker network create caddy_network --driver bridge
        docker network create arr_net --driver bridge
        
        # Reiniciar servicios
        Write-ColorOutput "   🚀 Reiniciando servicios..." "Info"
        docker-compose up -d
        
        Start-Sleep -Seconds 15
        
        Set-Location "C:\docker\arrs"
        docker-compose up -d
        
        Write-ColorOutput "   ⏳ Esperando que los servicios estén listos..." "Info"
        Start-Sleep -Seconds 45
        
        Write-ColorOutput "   ✅ Reinicio de red completado" "Success"
        return $true
    }
    catch {
        Write-ColorOutput "❌ Error reiniciando redes: $($_.Exception.Message)" "Error"
        return $false
    }
}

function Check-DockerResources {
    Write-ColorOutput "📊 Verificando recursos de Docker..." "Info"
    
    try {
        # Verificar uso de memoria y CPU
        $DockerStats = docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.MemPerc}}" 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-ColorOutput "📈 Uso de recursos por contenedor:" "Info"
            $DockerStats | ForEach-Object { Write-ColorOutput "   $_" "Highlight" }
        }
        
        # Verificar espacio en disco
        $DockerSystemDF = docker system df 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-ColorOutput "💾 Uso de espacio en disco:" "Info"
            $DockerSystemDF | ForEach-Object { Write-ColorOutput "   $_" "Highlight" }
        }
        
        # Verificar logs grandes
        Write-ColorOutput "📝 Verificando tamaño de logs..." "Info"
        $LogSizes = docker ps --format "{{.Names}}" | ForEach-Object {
            $LogSize = docker logs $_ --tail 0 2>&1 | Measure-Object -Line | Select-Object -ExpandProperty Lines
            if ($LogSize -gt 10000) {
                Write-ColorOutput "   ⚠️  $_ tiene muchos logs ($LogSize líneas)" "Warning"
            }
        }
        
        return $true
    }
    catch {
        Write-ColorOutput "❌ Error verificando recursos: $($_.Exception.Message)" "Error"
        return $false
    }
}

function Generate-NetworkReport {
    Write-ColorOutput "📋 Generando reporte de red..." "Info"
    
    $ReportPath = "C:\docker\network-report-$(Get-Date -Format 'yyyyMMdd-HHmmss').txt"
    
    try {
        $Report = @"
=== REPORTE DE RED DE DOCKER ===
Fecha: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')

=== REDES ===
$(docker network ls 2>$null)

=== CONTENEDORES ===
$(docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" 2>$null)

=== INFORMACIÓN DEL SISTEMA ===
$(docker system info 2>$null)

=== USO DE RECURSOS ===
$(docker stats --no-stream 2>$null)

=== ESPACIO EN DISCO ===
$(docker system df 2>$null)
"@
        
        $Report | Out-File -FilePath $ReportPath -Encoding UTF8
        Write-ColorOutput "   ✅ Reporte guardado en: $ReportPath" "Success"
        return $ReportPath
    }
    catch {
        Write-ColorOutput "❌ Error generando reporte: $($_.Exception.Message)" "Error"
        return $null
    }
}

# Función principal
function Start-NetworkOptimization {
    Clear-Host
    Write-ColorOutput "🌐 Optimización de Red de Docker" "Info"
    Write-ColorOutput "=" * 40 "Info"
    Write-ColorOutput "📅 Fecha: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" "Info"
    Write-ColorOutput ""
    
    if ($Analyze -or $All) {
        Write-ColorOutput "🔍 Analizando configuración de red..." "Info"
        Write-ColorOutput ""
        
        # Analizar redes
        $MissingNetworks = Analyze-DockerNetworks
        Write-ColorOutput ""
        
        # Probar conectividad
        $FailedConnections = Test-ContainerConnectivity
        Write-ColorOutput ""
        
        # Verificar recursos
        Check-DockerResources
        Write-ColorOutput ""
        
        # Optimizar DNS
        Optimize-DockerDNS
        Write-ColorOutput ""
        
        # Resumen
        Write-ColorOutput "📊 Resumen del Análisis:" "Info"
        Write-ColorOutput "=" * 25 "Info"
        
        if ($MissingNetworks.Count -eq 0) {
            Write-ColorOutput "✅ Todas las redes requeridas existen" "Success"
        } else {
            Write-ColorOutput "❌ Redes faltantes: $($MissingNetworks -join ', ')" "Error"
        }
        
        if ($FailedConnections.Count -eq 0) {
            Write-ColorOutput "✅ Toda la conectividad entre contenedores funciona" "Success"
        } else {
            Write-ColorOutput "❌ Conexiones fallidas: $($FailedConnections.Count)" "Error"
            $FailedConnections | ForEach-Object { Write-ColorOutput "   • $_" "Error" }
        }
    }
    
    if ($Fix -or $All) {
        Write-ColorOutput ""
        Write-ColorOutput "🔧 Aplicando correcciones..." "Info"
        Fix-DockerNetworks
    }
    
    if ($Reset) {
        Write-ColorOutput ""
        Reset-DockerNetworking
    }
    
    # Generar reporte
    Write-ColorOutput ""
    $ReportPath = Generate-NetworkReport
    
    Write-ColorOutput ""
    Write-ColorOutput "✅ Optimización completada." "Success"
    
    # Recomendaciones
    Write-ColorOutput ""
    Write-ColorOutput "💡 Recomendaciones:" "Info"
    Write-ColorOutput "   • Monitorea los logs: docker logs <container>" "Info"
    Write-ColorOutput "   • Ejecuta diagnósticos regulares" "Info"
    Write-ColorOutput "   • Considera reiniciar Docker Desktop si persisten problemas" "Warning"
    if ($ReportPath) {
        Write-ColorOutput "   • Revisa el reporte detallado: $ReportPath" "Highlight"
    }
}

# Ejecutar según parámetros
if ($All) {
    Start-NetworkOptimization
} elseif ($Analyze) {
    Start-NetworkOptimization
} elseif ($Fix) {
    Start-NetworkOptimization
} elseif ($Reset) {
    Start-NetworkOptimization
} else {
    Write-ColorOutput "🌐 Script de Optimización de Red de Docker" "Info"
    Write-ColorOutput ""
    Write-ColorOutput "Uso:" "Info"
    Write-ColorOutput "   .\optimize-docker-network.ps1 -Analyze    # Analizar configuración" "Highlight"
    Write-ColorOutput "   .\optimize-docker-network.ps1 -Fix        # Reparar problemas" "Highlight"
    Write-ColorOutput "   .\optimize-docker-network.ps1 -Reset      # Reiniciar redes" "Highlight"
    Write-ColorOutput "   .\optimize-docker-network.ps1 -All        # Todo lo anterior" "Highlight"
    Write-ColorOutput ""
    Write-ColorOutput "Ejecutando análisis básico..." "Info"
    $Analyze = $true
    Start-NetworkOptimization
}
