#!/usr/bin/env python3
"""
Script de prueba para verificar que el bot de Telegram funciona correctamente
"""
import asyncio
import httpx
import os
from dotenv import load_dotenv

# Cargar variables de entorno
load_dotenv()

TELEGRAM_BOT_TOKEN = os.getenv('TELEGRAM_BOT_TOKEN')
TELEGRAM_CHAT_ID = os.getenv('TELEGRAM_CHAT_ID')

async def test_bot_functionality():
    """Prueba las funcionalidades básicas del bot"""
    
    print("🧪 Iniciando pruebas del bot de Telegram...")
    
    # 1. Verificar health check
    print("\n1. Verificando health check...")
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get("http://localhost:8082/health")
            if response.status_code == 200:
                print("✅ Health check OK:", response.json())
            else:
                print("❌ Health check falló:", response.status_code)
    except Exception as e:
        print("❌ Error en health check:", e)
    
    # 2. Verificar conectividad con Telegram
    print("\n2. Verificando conectividad con Telegram...")
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get(f"https://api.telegram.org/bot{TELEGRAM_BOT_TOKEN}/getMe")
            if response.status_code == 200:
                bot_info = response.json()
                print("✅ Bot conectado:", bot_info['result']['first_name'])
            else:
                print("❌ Error conectando con Telegram:", response.status_code)
    except Exception as e:
        print("❌ Error en conectividad Telegram:", e)
    
    # 3. Enviar mensaje de prueba
    print("\n3. Enviando mensaje de prueba...")
    try:
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"https://api.telegram.org/bot{TELEGRAM_BOT_TOKEN}/sendMessage",
                json={
                    "chat_id": TELEGRAM_CHAT_ID,
                    "text": "🧪 Prueba automática del bot - Funcionalidad restaurada ✅"
                }
            )
            if response.status_code == 200:
                print("✅ Mensaje de prueba enviado correctamente")
            else:
                print("❌ Error enviando mensaje:", response.status_code)
    except Exception as e:
        print("❌ Error enviando mensaje:", e)
    
    # 4. Verificar APIs de Sonarr y Radarr
    print("\n4. Verificando APIs de Sonarr y Radarr...")
    
    # Sonarr
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get(
                "http://192.168.18.10:8989/api/v3/system/status",
                headers={"X-Api-Key": "2f9da07e98744f4890c0960d15ead111"}
            )
            if response.status_code == 200:
                print("✅ Sonarr API OK")
            else:
                print("❌ Sonarr API falló:", response.status_code)
    except Exception as e:
        print("❌ Error en Sonarr API:", e)
    
    # Radarr
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get(
                "http://192.168.18.10:7878/api/v3/system/status",
                headers={"X-Api-Key": "cfc0cde90b0f483eb4190dc634ca86f2"}
            )
            if response.status_code == 200:
                print("✅ Radarr API OK")
            else:
                print("❌ Radarr API falló:", response.status_code)
    except Exception as e:
        print("❌ Error en Radarr API:", e)
    
    # 5. Probar proxy de imágenes
    print("\n5. Probando proxy de imágenes...")
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get("http://localhost:8082/image-proxy/radarr/ac22743a84c409af253a9b8865afdc0919dfb22831944eef015e01b45ff855af/mFvoEwSfLqbcWwFsDjQebn9bzFe.jpg")
            if response.status_code == 200:
                print("✅ Proxy de imágenes funcionando correctamente")
            else:
                print(f"❌ Proxy de imágenes falló: {response.status_code}")
    except Exception as e:
        print("❌ Error en proxy de imágenes:", e)

    print("\n🎉 Pruebas completadas!")
    print("\n📝 Instrucciones para probar manualmente:")
    print("1. Abre Telegram y busca tu bot")
    print("2. Envía el comando: /estado")
    print("3. Envía el comando: /agregar Lost")
    print("4. Verifica que aparezcan los botones de selección")
    print("5. Selecciona 'Buscar en Sonarr' - DEBERÍAS VER CARÁTULAS DE SERIES")
    print("6. ¡PRESIONA EL BOTÓN ➕ AÑADIR! - AHORA DEBERÍA FUNCIONAR CORRECTAMENTE")
    print("7. Selecciona 'Buscar en Radarr' - DEBERÍAS VER CARÁTULAS DE PELÍCULAS")
    print("8. ¡PRESIONA EL BOTÓN ➕ AÑADIR! - AHORA DEBERÍA FUNCIONAR CORRECTAMENTE")
    print("\n🔧 CORRECCIONES IMPLEMENTADAS:")
    print("   ✅ Parsing de callbacks de 'add' corregido")
    print("   ✅ Manejo de duplicados mejorado")
    print("   ✅ Logging detallado para debugging")
    print("   ✅ Limpieza automática de interacciones antiguas")
    print("   ✅ Manejo robusto de errores de API")
    print("\n🖼️ CARÁTULAS:")
    print("   ✅ URLs remotas directas cuando estén disponibles")
    print("   ✅ Proxy interno para imágenes locales de Sonarr/Radarr")
    print("   ✅ Fallback a imagen placeholder si todo falla")

if __name__ == "__main__":
    asyncio.run(test_bot_functionality())
