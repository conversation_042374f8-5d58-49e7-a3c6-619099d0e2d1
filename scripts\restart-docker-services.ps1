# restart-docker-services.ps1
# Script para reiniciar servicios Docker de forma segura

param(
    [switch]$Homepage,
    [switch]$All,
    [switch]$Arrs,
    [switch]$System,
    [string]$Service = ""
)

function Write-Status {
    param($Message, $Type = "Info")
    
    switch ($Type) {
        "Success" { Write-Host "✅ $Message" -ForegroundColor Green }
        "Error" { Write-Host "❌ $Message" -ForegroundColor Red }
        "Warning" { Write-Host "⚠️  $Message" -ForegroundColor Yellow }
        "Info" { Write-Host "ℹ️  $Message" -ForegroundColor Blue }
        default { Write-Host "$Message" }
    }
}

function Restart-SingleService {
    param($ServiceName, $ComposeFile)
    
    Write-Status "Reiniciando $ServiceName..." "Info"
    
    try {
        $OriginalLocation = Get-Location
        Set-Location (Split-Path $ComposeFile -Parent)
        
        # Detener servicio
        Write-Host "  Deteniendo $ServiceName..." -NoNewline
        docker-compose stop $ServiceName 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host " ✅" -ForegroundColor Green
        } else {
            Write-Host " ⚠️" -ForegroundColor Yellow
        }
        
        Start-Sleep -Seconds 3
        
        # Iniciar servicio
        Write-Host "  Iniciando $ServiceName..." -NoNewline
        docker-compose up -d $ServiceName 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host " ✅" -ForegroundColor Green
        } else {
            Write-Host " ❌" -ForegroundColor Red
            Set-Location $OriginalLocation
            return $false
        }
        
        Set-Location $OriginalLocation
        
        # Esperar un momento
        Start-Sleep -Seconds 5
        
        # Verificar estado
        $Status = docker inspect $ServiceName --format "{{.State.Status}}" 2>$null
        if ($Status -eq "running") {
            Write-Status "$ServiceName reiniciado correctamente" "Success"
            return $true
        } else {
            Write-Status "$ServiceName reiniciado pero estado: $Status" "Warning"
            return $false
        }
    }
    catch {
        Write-Status "Error reiniciando $ServiceName: $($_.Exception.Message)" "Error"
        Set-Location $OriginalLocation
        return $false
    }
}

function Restart-HomepageService {
    Write-Host ""
    Write-Host "🏠 REINICIANDO HOMEPAGE" -ForegroundColor Cyan
    Write-Host "=======================" -ForegroundColor Cyan
    Write-Host ""
    
    $Result = Restart-SingleService -ServiceName "homepage" -ComposeFile "C:\docker\duckdns_updater\DNS-compose.yml"
    
    if ($Result) {
        Write-Host ""
        Write-Status "Esperando que Homepage esté listo..." "Info"
        Start-Sleep -Seconds 15
        
        # Probar conectividad
        try {
            $Response = Invoke-WebRequest -Uri "http://localhost:3001" -UseBasicParsing -TimeoutSec 10
            if ($Response.StatusCode -eq 200) {
                Write-Status "Homepage funcionando correctamente" "Success"
            }
        }
        catch {
            Write-Status "Homepage iniciado pero aún no responde" "Warning"
        }
    }
}

function Restart-ArrServices {
    Write-Host ""
    Write-Host "📺 REINICIANDO SERVICIOS *ARR" -ForegroundColor Cyan
    Write-Host "=============================" -ForegroundColor Cyan
    Write-Host ""
    
    $ArrServices = @("sonarr", "radarr", "jackett", "jellyseerr", "qbittorrent", "jellyfin")
    $ComposeFile = "C:\docker\arrs\ARRS-compose.yml"
    
    foreach ($Service in $ArrServices) {
        Restart-SingleService -ServiceName $Service -ComposeFile $ComposeFile
        Write-Host ""
    }
}

function Restart-SystemServices {
    Write-Host ""
    Write-Host "⚙️  REINICIANDO SERVICIOS DE SISTEMA" -ForegroundColor Cyan
    Write-Host "====================================" -ForegroundColor Cyan
    Write-Host ""
    
    $SystemServices = @("caddy", "portainer", "duckdns_jucago705", "duckdns_jucago706")
    $ComposeFile = "C:\docker\duckdns_updater\DNS-compose.yml"
    
    foreach ($Service in $SystemServices) {
        Restart-SingleService -ServiceName $Service -ComposeFile $ComposeFile
        Write-Host ""
    }
}

function Restart-AllServices {
    Write-Host ""
    Write-Host "🔄 REINICIANDO TODOS LOS SERVICIOS" -ForegroundColor Cyan
    Write-Host "===================================" -ForegroundColor Cyan
    Write-Host ""
    
    $Confirmation = Read-Host "⚠️  Esto reiniciará todos los servicios. ¿Continuar? (s/N)"
    if ($Confirmation -ne 's' -and $Confirmation -ne 'S') {
        Write-Status "Operación cancelada" "Warning"
        return
    }
    
    try {
        # Detener todos los servicios
        Write-Status "Deteniendo servicios *arr..." "Info"
        Set-Location "C:\docker\arrs"
        docker-compose down
        
        Write-Status "Deteniendo servicios principales..." "Info"
        Set-Location "C:\docker\duckdns_updater"
        docker-compose down
        
        Start-Sleep -Seconds 10
        
        # Iniciar servicios principales
        Write-Status "Iniciando servicios principales..." "Info"
        docker-compose up -d
        
        Start-Sleep -Seconds 20
        
        # Iniciar servicios *arr
        Write-Status "Iniciando servicios *arr..." "Info"
        Set-Location "C:\docker\arrs"
        docker-compose up -d
        
        Write-Status "Esperando que todos los servicios estén listos..." "Info"
        Start-Sleep -Seconds 30
        
        Write-Status "Reinicio completo finalizado" "Success"
    }
    catch {
        Write-Status "Error durante el reinicio: $($_.Exception.Message)" "Error"
    }
}

function Show-ServiceStatus {
    Write-Host ""
    Write-Host "📊 ESTADO DE SERVICIOS" -ForegroundColor Cyan
    Write-Host "======================" -ForegroundColor Cyan
    Write-Host ""
    
    try {
        $Containers = docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
        $Containers | ForEach-Object { Write-Host "  $_" }
    }
    catch {
        Write-Status "Error obteniendo estado de contenedores" "Error"
    }
}

# Función principal
if ($Homepage) {
    Restart-HomepageService
    Show-ServiceStatus
} elseif ($Arrs) {
    Restart-ArrServices
    Show-ServiceStatus
} elseif ($System) {
    Restart-SystemServices
    Show-ServiceStatus
} elseif ($All) {
    Restart-AllServices
    Show-ServiceStatus
} elseif ($Service -ne "") {
    Write-Host ""
    Write-Host "🔄 REINICIANDO SERVICIO: $Service" -ForegroundColor Cyan
    Write-Host "================================" -ForegroundColor Cyan
    Write-Host ""
    
    # Determinar archivo compose
    $ComposeFile = if ($Service -in @("sonarr", "radarr", "jackett", "jellyseerr", "qbittorrent", "jellyfin")) {
        "C:\docker\arrs\ARRS-compose.yml"
    } else {
        "C:\docker\duckdns_updater\DNS-compose.yml"
    }
    
    Restart-SingleService -ServiceName $Service -ComposeFile $ComposeFile
    Show-ServiceStatus
} else {
    Write-Host ""
    Write-Host "🔄 SCRIPT DE REINICIO DE SERVICIOS" -ForegroundColor Cyan
    Write-Host "==================================" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Uso:" -ForegroundColor Yellow
    Write-Host "  .\restart-docker-services.ps1 -Homepage        # Solo Homepage" -ForegroundColor White
    Write-Host "  .\restart-docker-services.ps1 -Arrs            # Servicios *arr" -ForegroundColor White
    Write-Host "  .\restart-docker-services.ps1 -System          # Servicios sistema" -ForegroundColor White
    Write-Host "  .\restart-docker-services.ps1 -All             # Todos los servicios" -ForegroundColor White
    Write-Host "  .\restart-docker-services.ps1 -Service jellyfin # Servicio específico" -ForegroundColor White
    Write-Host ""
    Write-Host "Mostrando estado actual..." -ForegroundColor Green
    Show-ServiceStatus
}

Write-Host ""
