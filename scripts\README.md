# 🛠️ Scripts de Migración a Cloudflare

Esta carpeta contiene scripts de PowerShell para automatizar la migración de DuckDNS a Cloudflare y gestionar el proceso completo.

## 📁 Archivos Incluidos

### 🚀 `setup-cloudflare.ps1`
**Propósito**: Automatiza la configuración inicial de Cloudflare
- Crea backup de configuración actual
- Configura registros DNS en Cloudflare
- Prepara archivos de configuración de Caddy
- Valida credenciales de API

**Uso**:
```powershell
# Modo interactivo (recomendado)
.\setup-cloudflare.ps1

# Modo no interactivo
.\setup-cloudflare.ps1 -Domain "midominio.com" -CloudflareEmail "<EMAIL>" -CloudflareAPIKey "tu_api_key"

# Modo dry-run (simulación)
.\setup-cloudflare.ps1 -DryRun
```

### 🔍 `verify-cloudflare-migration.ps1`
**Propósito**: Verifica el estado de la migración y el funcionamiento de los servicios
- Prueba conectividad a todos los servicios
- Mide rendimiento y tiempos de respuesta
- Verifica propagación DNS
- Analiza headers de Cloudflare

**Uso**:
```powershell
# Verificación básica
.\verify-cloudflare-migration.ps1 -Domain "midominio.com"

# Verificación detallada con análisis de rendimiento
.\verify-cloudflare-migration.ps1 -Domain "midominio.com" -Detailed -Performance

# Verificación completa
.\verify-cloudflare-migration.ps1 -Domain "midominio.com" -All
```

### 🔄 `rollback-to-duckdns.ps1`
**Propósito**: Script de rollback de emergencia para volver a DuckDNS
- Restaura configuración desde backups
- Reinicia servicios Docker
- Verifica funcionamiento post-rollback
- Limpia archivos de Cloudflare

**Uso**:
```powershell
# Simulación de rollback (recomendado primero)
.\rollback-to-duckdns.ps1

# Ejecutar rollback real
.\rollback-to-duckdns.ps1 -Execute

# Rollback forzado (sin confirmación)
.\rollback-to-duckdns.ps1 -Execute -Force
```

## 🔧 Configuración Previa

### 📋 Requisitos
1. **PowerShell 5.1 o superior**
2. **Docker y Docker Compose** funcionando
3. **Cuenta de Cloudflare** con dominio configurado
4. **API Key de Cloudflare** (Global API Key o Token con permisos de Zone)

### 🔑 Obtener API Key de Cloudflare

#### Opción 1: Global API Key (Más Simple)
1. Ve a [Cloudflare Dashboard](https://dash.cloudflare.com/profile/api-tokens)
2. En "API Keys", click en "View" junto a "Global API Key"
3. Ingresa tu contraseña
4. Copia la API Key

#### Opción 2: API Token (Más Seguro)
1. Ve a [Cloudflare Dashboard](https://dash.cloudflare.com/profile/api-tokens)
2. Click en "Create Token"
3. Usa la plantilla "Edit zone DNS"
4. Configura:
   - **Zone Resources**: Include → Specific zone → tu dominio
   - **Account Resources**: Include → All accounts
5. Click "Continue to summary" y luego "Create Token"
6. Copia el token generado

### 🌐 Configurar Dominio en Cloudflare

1. **Agregar dominio a Cloudflare**:
   - En el dashboard, click "Add a Site"
   - Ingresa tu dominio
   - Selecciona plan "Free"

2. **Cambiar nameservers**:
   - Cloudflare te dará 2 nameservers
   - Ve a tu registrador de dominio
   - Cambia los nameservers por los de Cloudflare
   - Espera 24-48 horas para propagación

3. **Verificar activación**:
   ```powershell
   nslookup tudominio.com
   # Debe mostrar IPs de Cloudflare
   ```

## 📊 Flujo de Migración Recomendado

### 🎯 Fase 1: Preparación
```powershell
# 1. Crear backup y configurar Cloudflare
.\setup-cloudflare.ps1 -Domain "midominio.com"

# 2. Verificar configuración inicial
.\verify-cloudflare-migration.ps1 -Domain "midominio.com" -Detailed
```

### 🔧 Fase 2: Configuración Manual
1. **Configurar certificados SSL Origin** en Cloudflare
2. **Modificar Docker Compose** para usar nueva configuración
3. **Actualizar Caddyfile** con el configurado

### 🧪 Fase 3: Testing
```powershell
# Verificar funcionamiento completo
.\verify-cloudflare-migration.ps1 -Domain "midominio.com" -All -Performance
```

### 🚀 Fase 4: Finalización
- Desactivar DuckDNS (después de 48h de estabilidad)
- Configurar optimizaciones en Cloudflare
- Configurar monitoreo y alertas

## 🆘 Solución de Problemas

### ❌ Error: "No se pudo obtener la IP pública"
**Solución**:
```powershell
# Verificar conectividad
Test-NetConnection -ComputerName "*******" -Port 443

# Obtener IP manualmente
(Invoke-WebRequest -Uri "https://ipinfo.io/ip").Content
```

### ❌ Error: "Credenciales de Cloudflare inválidas"
**Soluciones**:
1. Verificar que el email sea correcto
2. Regenerar API Key en Cloudflare
3. Si usas API Token, verificar permisos

### ❌ Error: "No se encontró la zona para el dominio"
**Soluciones**:
1. Verificar que el dominio esté agregado a Cloudflare
2. Esperar a que la zona esté activa
3. Verificar que los nameservers estén configurados

### ❌ Servicios no responden después de migración
**Soluciones**:
```powershell
# Verificar servicios Docker
docker ps

# Ver logs de Caddy
docker logs caddy_proxy

# Rollback si es necesario
.\rollback-to-duckdns.ps1 -Execute
```

## 📈 Optimizaciones Post-Migración

### ⚙️ Page Rules Recomendadas
1. **Cache para assets estáticos**:
   ```
   *.tudominio.com/static/*
   Cache Level: Cache Everything
   ```

2. **Seguridad para paneles admin**:
   ```
   *.tudominio.com/admin*
   Security Level: High
   ```

3. **Performance para streaming**:
   ```
   flix.tudominio.com/*
   Rocket Loader: Off
   ```

### 🔒 Configuraciones de Seguridad
- **SSL/TLS**: Full (strict)
- **Always Use HTTPS**: On
- **HSTS**: Enable
- **Bot Fight Mode**: On

### 📊 Monitoreo
- Configurar **Uptime Monitoring** en Cloudflare
- Habilitar **Analytics** para métricas detalladas
- Configurar **Notifications** para alertas

## 📞 Soporte

### 🔗 Enlaces Útiles
- [Documentación Cloudflare](https://developers.cloudflare.com/)
- [Documentación Caddy](https://caddyserver.com/docs/)
- [Comunidad Cloudflare](https://community.cloudflare.com/)

### 🐛 Reportar Problemas
Si encuentras problemas con los scripts:
1. Ejecuta con `-Detailed` para más información
2. Revisa los logs de Docker
3. Verifica la configuración de Cloudflare
4. Considera usar el rollback si es necesario

---

**🎉 ¡Buena suerte con tu migración a Cloudflare!**
