
services:
  # === CADDY REVERSE PROXY ===
  caddy:
    image: caddy:2-alpine
    container_name: caddy_proxy
    restart: unless-stopped
    ports:
      - "80:80"     # HTTP
      - "443:443"   # HTTPS
      - "443:443/udp" # HTTP/3 (QUIC)
      - "8081:8081" # NTFY.SH HTTPS
    volumes:
      # Monta el Caddyfile adaptado para Docker (mantiene tu configuración)
      - C:\docker\Caddyfile.host:/etc/caddy/Caddyfile:ro
      # Datos persistentes de Caddy (certificados SSL, etc.)
      - caddy_data:/data
      - caddy_config:/config
      # Monta los directorios de logs manteniendo la estructura de tu configuración original
      - C:\docker\duckdns_updater\caddy\logs:/var/log/caddy
    networks:
      - caddy_network
    depends_on:
      - duckdns_jucago705
      - duckdns_jucago706
    healthcheck:
      test: ["CMD", "caddy", "validate", "--config", "/etc/caddy/Caddyfile"]
      interval: 5m
      timeout: 10s
      retries: 3
      start_period: 30s
    # Configuración adicional para acceso al host en Windows
    extra_hosts:
      - "host.docker.internal:host-gateway"

  # --- DuckDns 1 ---
  duckdns_jucago705:
    image: lscr.io/linuxserver/duckdns:latest
    container_name: duckdns_jucago705
    restart: unless-stopped
    environment:
      - PUID=1000 # Deja esto como 1000 para Windows
      - PGID=1000 # Deja esto como 1000 para Windows
      - TZ=Europe/Madrid # Zona horaria, cámbiala si estás en otro lugar
      - TOKEN=*************-4963-bd13-fc1199f2f5d9 # <--- ¡CAMBIA ESTO!
      - DOMAINS=tanketorrent.duckdns.org, tankeflix.duckdns.org, tankesonarr.duckdns.org, tankejackett.duckdns.org, tankeeee2.duckdns.org, tankejellyseerr.duckdns.org
      - SUBDOMAINS=false # Pon 'true' si usas subdominios que no estén en la lista DOMAINS
      - LOG_FILE=true # Opcional: crea un archivo de log dentro del volumen
    volumes:
      - ./config:/config # Mapea el volumen para guardar la configuración y logs
    networks:
      - caddy_network

  # --- DuckDns 2 ---
  duckdns_jucago706:
    image: lscr.io/linuxserver/duckdns:latest
    container_name: duckdns_jucago706
    restart: unless-stopped
    environment:
      - PUID=1000
      - PGID=1000
      - TZ=Europe/Madrid
      - TOKEN=1b37bdd3-38a3-4e5f-8eea-57a3c7419c7d
      - DOMAINS=tankeradarr.duckdns.org,
      - SUBDOMAINS=false
      - LOG_FILE=true
    volumes:
      - ./config:/config
    networks:
      - caddy_network

      

# === PORTAINER DOCKER MANAGEMENT ===
  portainer:
    image: portainer/portainer-ce:latest
    container_name: portainer
    environment:
      - TZ=Europe/Madrid
      # Configuración optimizada para acceso remoto via proxy reverso
      - PORTAINER_INSECURE_COOKIE=true
      - PORTAINER_ENABLE_HOST_MANAGEMENT_FEATURES=true
      # Configuración específica para proxy reverso
      - PORTAINER_HTTP_DISABLED=false
      - PORTAINER_HTTPS_DISABLED=true
    volumes:
      # Configuración persistente de Portainer
      - C:\docker\duckdns_updater\portainer\data:/data
      # Acceso completo al socket de Docker para gestión
      - //var/run/docker.sock:/var/run/docker.sock
    ports:
      - "9000:9000"
    restart: unless-stopped
    networks:
      - caddy_network
    # Configuración adicional para acceso al host en Windows
    extra_hosts:
      - "host.docker.internal:host-gateway"


# === WATCHTOWER AUTO-UPDATER ===
  watchtower:
    image: containrrr/watchtower:latest
    container_name: watchtower
    environment:
      - TZ=Europe/Madrid
      - WATCHTOWER_DEBUG=true
      - WATCHTOWER_LOG_LEVEL=debug
      - WATCHTOWER_LOG_FORMAT=json
      - WATCHTOWER_POLL_INTERVAL=1800
      - WATCHTOWER_NO_STARTUP_MESSAGE=false
      - WATCHTOWER_CLEANUP=true
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
    restart: unless-stopped
    networks:
      - caddy_network
    extra_hosts:
      - "host.docker.internal:host-gateway"
    labels:
      - "com.centurylinklabs.watchtower.enable=true"
    # Configuración de logging de Docker para archivos locales
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "5"
      
# === HOMEPAGE DASHBOARD ===
  homepage:
    image: ghcr.io/gethomepage/homepage:latest
    container_name: homepage
    environment:
      - PUID=1000
      - PGID=1000
      - TZ=Europe/Madrid
      - HOMEPAGE_ALLOWED_HOSTS=tankeeee2.duckdns.org,localhost,localhost:3001,host.docker.internal:3001
      # Configuración optimizada para estabilidad
      - LOG_LEVEL=info
      - HOMEPAGE_TIMEOUT=15000
      - HOMEPAGE_REFRESH_INTERVAL=60
    env_file:
      - ./homepage/config/api-keys.env
    volumes:
      - C:\docker\duckdns_updater\homepage\config:/app/config
      # Socket de Docker para los widgets de estado de contenedores (Windows)
      # Configuración mejorada para permisos
      - /var/run/docker.sock:/var/run/docker.sock:ro
      # Montajes de discos para monitoreo de almacenamiento
      - C:\:/mnt/c:ro
      - D:\:/mnt/d:ro
      - E:\:/mnt/e:ro
      - F:\:/mnt/f:ro
    ports:
      - "3001:3000"
    networks:
      - caddy_network
      - arr_net
    restart: unless-stopped
    depends_on:
      - caddy
    # Configuración adicional para acceso al host en Windows
    extra_hosts:
      - "host.docker.internal:host-gateway"
    # Etiquetas para Watchtower
    labels:
      - "com.centurylinklabs.watchtower.enable=true"
    # Health check optimizado para estabilidad
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "--timeout=10", "http://localhost:3000"]
      interval: 60s
      timeout: 15s
      retries: 5
      start_period: 60s
    # Configuración de recursos para estabilidad
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M
    # Configuración de logging para evitar logs excesivos
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

# === VOLÚMENES ===
volumes:
  caddy_data:
    driver: local
  caddy_config:
    driver: local


# === REDES ===
networks:
  caddy_network:
    driver: bridge
    name: caddy_network
  arr_net:
    external: true #