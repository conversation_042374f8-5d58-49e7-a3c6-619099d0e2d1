# rollback-to-duckdns.ps1
# Script de rollback de emergencia para volver a DuckDNS

param(
    [switch]$Execute,
    [switch]$DryRun = $true,
    [switch]$Force,
    [string]$BackupDate = ""
)

# Configuración de rutas
$DockerDir = "C:\docker"
$BackupBaseDir = "$DockerDir\backups"
$DuckDNSDir = "$DockerDir\duckdns_updater"

# Configuración de colores
$Colors = @{
    Success = "Green"
    Error = "Red"
    Warning = "Yellow"
    Info = "Blue"
    Highlight = "Cyan"
}

function Write-ColorOutput {
    param($Message, $Color = "White")
    Write-Host $Message -ForegroundColor $Colors[$Color]
}

function Get-LatestBackup {
    param($Pattern)
    
    try {
        $BackupFiles = Get-ChildItem "$BackupBaseDir\pre-cloudflare_*\$Pattern" -ErrorAction SilentlyContinue
        if ($BackupFiles) {
            return $BackupFiles | Sort-Object LastWriteTime -Descending | Select-Object -First 1
        }
        return $null
    }
    catch {
        Write-ColorOutput "❌ Error buscando backups: $($_.Exception.Message)" "Error"
        return $null
    }
}

function Test-ServiceStatus {
    param($ServiceName, $Port)
    
    try {
        $Response = Invoke-WebRequest -Uri "http://localhost:$Port" -UseBasicParsing -TimeoutSec 5 -ErrorAction SilentlyContinue
        return $Response.StatusCode -eq 200
    }
    catch {
        return $false
    }
}

function Stop-DockerServices {
    Write-ColorOutput "🛑 Deteniendo servicios Docker..." "Info"
    
    try {
        Set-Location $DuckDNSDir
        
        if ($DryRun) {
            Write-ColorOutput "   [DRY-RUN] docker-compose down" "Highlight"
        } else {
            $Result = docker-compose down 2>&1
            if ($LASTEXITCODE -eq 0) {
                Write-ColorOutput "✅ Servicios Docker detenidos correctamente" "Success"
            } else {
                Write-ColorOutput "⚠️  Advertencia al detener servicios: $Result" "Warning"
            }
        }
    }
    catch {
        Write-ColorOutput "❌ Error deteniendo servicios: $($_.Exception.Message)" "Error"
        return $false
    }
    
    return $true
}

function Restore-ConfigurationFiles {
    Write-ColorOutput "📄 Restaurando archivos de configuración..." "Info"
    
    # Restaurar Caddyfile principal
    $CaddyfileBackup = Get-LatestBackup "Caddyfile.backup"
    if ($CaddyfileBackup) {
        Write-ColorOutput "   📄 Restaurando Caddyfile desde: $($CaddyfileBackup.FullName)" "Info"
        if (-not $DryRun) {
            try {
                Copy-Item $CaddyfileBackup.FullName "$DockerDir\Caddyfile" -Force
                Write-ColorOutput "   ✅ Caddyfile restaurado" "Success"
            }
            catch {
                Write-ColorOutput "   ❌ Error restaurando Caddyfile: $($_.Exception.Message)" "Error"
                return $false
            }
        } else {
            Write-ColorOutput "   [DRY-RUN] Copy-Item '$($CaddyfileBackup.FullName)' '$DockerDir\Caddyfile'" "Highlight"
        }
    } else {
        Write-ColorOutput "   ❌ No se encontró backup del Caddyfile" "Error"
        return $false
    }
    
    # Restaurar Caddyfile.host
    $CaddyfileHostBackup = Get-LatestBackup "Caddyfile.host.backup"
    if ($CaddyfileHostBackup) {
        Write-ColorOutput "   📄 Restaurando Caddyfile.host desde: $($CaddyfileHostBackup.FullName)" "Info"
        if (-not $DryRun) {
            try {
                Copy-Item $CaddyfileHostBackup.FullName "$DockerDir\Caddyfile.host" -Force
                Write-ColorOutput "   ✅ Caddyfile.host restaurado" "Success"
            }
            catch {
                Write-ColorOutput "   ❌ Error restaurando Caddyfile.host: $($_.Exception.Message)" "Error"
                return $false
            }
        } else {
            Write-ColorOutput "   [DRY-RUN] Copy-Item '$($CaddyfileHostBackup.FullName)' '$DockerDir\Caddyfile.host'" "Highlight"
        }
    } else {
        Write-ColorOutput "   ❌ No se encontró backup del Caddyfile.host" "Error"
        return $false
    }
    
    # Restaurar Docker Compose
    $ComposeBackup = Get-LatestBackup "DNS-compose.yml.backup"
    if ($ComposeBackup) {
        Write-ColorOutput "   🐳 Restaurando Docker Compose desde: $($ComposeBackup.FullName)" "Info"
        if (-not $DryRun) {
            try {
                Copy-Item $ComposeBackup.FullName "$DuckDNSDir\DNS-compose.yml" -Force
                Write-ColorOutput "   ✅ Docker Compose restaurado" "Success"
            }
            catch {
                Write-ColorOutput "   ❌ Error restaurando Docker Compose: $($_.Exception.Message)" "Error"
                return $false
            }
        } else {
            Write-ColorOutput "   [DRY-RUN] Copy-Item '$($ComposeBackup.FullName)' '$DuckDNSDir\DNS-compose.yml'" "Highlight"
        }
    } else {
        Write-ColorOutput "   ❌ No se encontró backup del Docker Compose" "Error"
        return $false
    }
    
    return $true
}

function Start-DockerServices {
    Write-ColorOutput "🚀 Iniciando servicios Docker..." "Info"
    
    try {
        Set-Location $DuckDNSDir
        
        if ($DryRun) {
            Write-ColorOutput "   [DRY-RUN] docker-compose up -d" "Highlight"
        } else {
            $Result = docker-compose up -d 2>&1
            if ($LASTEXITCODE -eq 0) {
                Write-ColorOutput "✅ Servicios Docker iniciados correctamente" "Success"
                
                # Esperar un momento para que los servicios se inicien
                Write-ColorOutput "⏳ Esperando que los servicios se inicien..." "Info"
                Start-Sleep -Seconds 30
                
                return $true
            } else {
                Write-ColorOutput "❌ Error iniciando servicios: $Result" "Error"
                return $false
            }
        }
    }
    catch {
        Write-ColorOutput "❌ Error iniciando servicios: $($_.Exception.Message)" "Error"
        return $false
    }
    
    return $true
}

function Test-DuckDNSServices {
    Write-ColorOutput "🔍 Verificando servicios DuckDNS..." "Info"
    
    $DuckDNSServices = @(
        @{Name="Jellyfin"; URL="https://tankeflix.duckdns.org"; Port=8096},
        @{Name="qBittorrent"; URL="https://tanketorrent.duckdns.org"; Port=8091},
        @{Name="AdGuard Home"; URL="https://tankeguard.duckdns.org"; Port=8080},
        @{Name="Homepage"; URL="https://tankeeee2.duckdns.org"; Port=3001}
    )
    
    $SuccessCount = 0
    
    foreach ($Service in $DuckDNSServices) {
        try {
            Write-ColorOutput "   🔍 Probando $($Service.Name)..." "Info"
            
            if ($DryRun) {
                Write-ColorOutput "   [DRY-RUN] Test-Connection $($Service.URL)" "Highlight"
                $SuccessCount++
            } else {
                $Response = Invoke-WebRequest -Uri $Service.URL -UseBasicParsing -TimeoutSec 10 -ErrorAction SilentlyContinue
                if ($Response.StatusCode -eq 200) {
                    Write-ColorOutput "   ✅ $($Service.Name) - OK" "Success"
                    $SuccessCount++
                } else {
                    Write-ColorOutput "   ❌ $($Service.Name) - Error HTTP $($Response.StatusCode)" "Error"
                }
            }
        }
        catch {
            Write-ColorOutput "   ❌ $($Service.Name) - Error: $($_.Exception.Message)" "Error"
        }
        
        Start-Sleep -Seconds 2
    }
    
    Write-ColorOutput "📊 Servicios funcionando: $SuccessCount/$($DuckDNSServices.Count)" "Info"
    return $SuccessCount -eq $DuckDNSServices.Count
}

function Remove-CloudflareFiles {
    Write-ColorOutput "🧹 Limpiando archivos de Cloudflare..." "Info"
    
    $CloudflareFiles = @(
        "$DockerDir\Caddyfile.cloudflare",
        "$DockerDir\ssl\cloudflare"
    )
    
    foreach ($File in $CloudflareFiles) {
        if (Test-Path $File) {
            Write-ColorOutput "   🗑️  Eliminando: $File" "Info"
            if (-not $DryRun) {
                try {
                    Remove-Item $File -Recurse -Force
                    Write-ColorOutput "   ✅ Eliminado: $File" "Success"
                }
                catch {
                    Write-ColorOutput "   ⚠️  No se pudo eliminar: $File - $($_.Exception.Message)" "Warning"
                }
            } else {
                Write-ColorOutput "   [DRY-RUN] Remove-Item '$File' -Recurse -Force" "Highlight"
            }
        }
    }
}

# Función principal de rollback
function Start-Rollback {
    Write-ColorOutput "🔄 Iniciando rollback a DuckDNS..." "Warning"
    
    if ($DryRun) {
        Write-ColorOutput "🧪 MODO DRY-RUN - No se realizarán cambios reales" "Highlight"
    } else {
        Write-ColorOutput "⚠️  MODO EJECUCIÓN - Se realizarán cambios reales" "Warning"
        
        if (-not $Force) {
            $Confirmation = Read-Host "¿Estás seguro de que quieres continuar? (s/N)"
            if ($Confirmation -ne 's' -and $Confirmation -ne 'S') {
                Write-ColorOutput "❌ Rollback cancelado por el usuario" "Error"
                return $false
            }
        }
    }
    
    Write-ColorOutput ""
    
    # Paso 1: Detener servicios
    if (-not (Stop-DockerServices)) {
        Write-ColorOutput "❌ Error deteniendo servicios. Rollback abortado." "Error"
        return $false
    }
    
    # Paso 2: Restaurar archivos de configuración
    if (-not (Restore-ConfigurationFiles)) {
        Write-ColorOutput "❌ Error restaurando configuración. Rollback abortado." "Error"
        return $false
    }
    
    # Paso 3: Iniciar servicios
    if (-not (Start-DockerServices)) {
        Write-ColorOutput "❌ Error iniciando servicios. Revisa la configuración manualmente." "Error"
        return $false
    }
    
    # Paso 4: Verificar servicios (solo si no es dry-run)
    if (-not $DryRun) {
        if (Test-DuckDNSServices) {
            Write-ColorOutput "✅ Todos los servicios DuckDNS funcionan correctamente" "Success"
        } else {
            Write-ColorOutput "⚠️  Algunos servicios pueden requerir tiempo adicional para iniciarse" "Warning"
        }
    }
    
    # Paso 5: Limpiar archivos de Cloudflare
    Remove-CloudflareFiles
    
    return $true
}

# Inicio del script
Clear-Host
Write-ColorOutput "🔄 Script de Rollback a DuckDNS" "Info"
Write-ColorOutput "=" * 40 "Info"
Write-ColorOutput "📅 Fecha: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" "Info"
Write-ColorOutput ""

# Verificar que existen backups
$BackupDirs = Get-ChildItem "$BackupBaseDir\pre-cloudflare_*" -Directory -ErrorAction SilentlyContinue
if (-not $BackupDirs) {
    Write-ColorOutput "❌ No se encontraron backups de configuración pre-Cloudflare" "Error"
    Write-ColorOutput "   Verifica que existan backups en: $BackupBaseDir" "Info"
    exit 1
}

$LatestBackupDir = $BackupDirs | Sort-Object LastWriteTime -Descending | Select-Object -First 1
Write-ColorOutput "📦 Backup más reciente encontrado: $($LatestBackupDir.Name)" "Info"
Write-ColorOutput "📅 Fecha del backup: $($LatestBackupDir.LastWriteTime)" "Info"
Write-ColorOutput ""

if ($Execute) {
    $DryRun = $false
    $Result = Start-Rollback
    
    if ($Result) {
        Write-ColorOutput "🎉 ¡Rollback completado exitosamente!" "Success"
        Write-ColorOutput ""
        Write-ColorOutput "📋 Próximos pasos:" "Info"
        Write-ColorOutput "   • Verificar que todos los servicios funcionan correctamente" "Info"
        Write-ColorOutput "   • Comprobar los logs de Docker si hay problemas" "Info"
        Write-ColorOutput "   • Considerar qué causó la necesidad del rollback" "Info"
    } else {
        Write-ColorOutput "❌ Rollback falló. Revisa los errores anteriores." "Error"
        exit 1
    }
} else {
    Write-ColorOutput "⚠️  Para ejecutar el rollback real, usa:" "Warning"
    Write-ColorOutput "   .\rollback-to-duckdns.ps1 -Execute" "Highlight"
    Write-ColorOutput ""
    Write-ColorOutput "🧪 Ejecutando simulación (dry-run)..." "Info"
    Write-ColorOutput ""
    
    Start-Rollback
    
    Write-ColorOutput ""
    Write-ColorOutput "✅ Simulación completada. Usa -Execute para realizar el rollback real." "Success"
}
