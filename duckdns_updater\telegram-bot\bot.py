#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import logging
import asyncio
import json
import math
import time
from typing import Dict, List, Optional, Any
from datetime import datetime

import httpx
from fastapi import FastAPI, Request, HTTPException, Response
from fastapi.responses import JSONResponse, RedirectResponse
import uvicorn
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup, InputMediaPhoto
from telegram.ext import Application, CommandHandler, CallbackQueryHandler, ContextTypes
from telegram.constants import ParseMode
from dotenv import load_dotenv

# --- 1. CONFIGURACIÓN INICIAL Y LOGGING ---
load_dotenv()

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.FileHandler('/app/logs/arr-bot.log'), logging.StreamHandler()]
)
logger = logging.getLogger(__name__)

# --- 2. CARGA DE VARIABLES DE ENTORNO ---
TELEGRAM_BOT_TOKEN = os.getenv('TELEGRAM_BOT_TOKEN')
TELEGRAM_CHAT_ID = os.getenv('TELEGRAM_CHAT_ID')
RADARR_URL = os.getenv('RADARR_URL', 'http://host.docker.internal:7878')
RADARR_API_KEY = os.getenv('RADARR_API_KEY')
SONARR_URL = os.getenv('SONARR_URL', 'http://host.docker.internal:8989')
SONARR_API_KEY = os.getenv('SONARR_API_KEY')
WEBHOOK_PORT = int(os.getenv('WEBHOOK_PORT', '8082'))

if not all([TELEGRAM_BOT_TOKEN, TELEGRAM_CHAT_ID, RADARR_API_KEY, SONARR_API_KEY]):
    logger.critical("FALTAN VARIABLES DE ENTORNO CRÍTICAS. Revisa tu .env.")
    exit(1)

# --- 3. INSTANCIAS GLOBALES ---
app = FastAPI(title="Arr-Bot Webhook Server")
telegram_app: Optional[Application] = None
pending_interactions: Dict[str, Any] = {}
    

# --- 4. CLASE PARA COMUNICARSE CON RADARR Y SONARR ---
class ArrAPI:
    def __init__(self, base_url: str, api_key: str, service_name: str):
        self.base_url = base_url.rstrip('/')
        self.api_key = api_key
        self.service_name = service_name
        self.headers = {'X-Api-Key': api_key}
        self.client = httpx.AsyncClient(timeout=20)

    async def _make_request(self, endpoint: str, method: str = 'GET', data: Optional[Dict] = None) -> Optional[Any]:
        url = f"{self.base_url}/api/v3/{endpoint}"
        try:
            logger.info(f"Haciendo petición {method} a {url}")
            if data:
                logger.info(f"Datos enviados: {data}")
            response = await self.client.request(method, url, headers=self.headers, json=data)
            logger.info(f"Respuesta HTTP: {response.status_code}")
            response.raise_for_status()
            result = response.json() if response.content else {}
            logger.info(f"Respuesta exitosa de {self.service_name}")
            return result
        except httpx.HTTPStatusError as e:
            logger.error(f"Error HTTP en API de {self.service_name}: {e.response.status_code} - {e.response.text}")

            # Manejar casos específicos
            if e.response.status_code == 400:
                try:
                    error_data = e.response.json()
                    if isinstance(error_data, list) and len(error_data) > 0:
                        error_info = error_data[0]
                        if error_info.get('errorCode') in ['SeriesExistsValidator', 'MovieExistsValidator']:
                            logger.warning(f"Contenido ya existe en {self.service_name}: {error_info.get('errorMessage')}")
                            return {'error': 'duplicate', 'message': error_info.get('errorMessage')}
                except:
                    pass

            return None
        except httpx.RequestError as e:
            logger.error(f"Error de conexión en API de {self.service_name}: {e}")
            return None

    async def get_system_status(self):
        return await self._make_request('system/status')

    async def search_content(self, term: str):
        endpoint = f"movie/lookup?term={term}" if self.service_name == 'Radarr' else f"series/lookup?term={term}"
        return await self._make_request(endpoint)

    async def get_library(self):
        endpoint = "movie" if self.service_name == 'Radarr' else "series"
        return await self._make_request(endpoint)

    async def add_content(self, content_data: Dict):
        logger.info(f"Intentando agregar contenido en {self.service_name}: {content_data.get('title', 'N/A')}")
        logger.info(f"Usando perfil de calidad 'UHD - HD' (ID: 7) para garantizar mínimo 1080p")

        if self.service_name == 'Radarr':
            endpoint = 'movie'
            # Obtener carpetas raíz de Radarr
            root_folders = await self._make_request('rootfolder')
            root_path = root_folders[0]['path'] if root_folders else '/movies'

            content_data.update({
                'qualityProfileId': 7,  # Usar perfil "UHD - HD" para calidad mínima 1080p
                'rootFolderPath': root_path,
                'monitored': True,
                'addOptions': {'searchForMovie': True}
            })
        else:  # Sonarr
            endpoint = 'series'
            # Obtener carpetas raíz de Sonarr
            root_folders = await self._make_request('rootfolder')
            root_path = root_folders[0]['path'] if root_folders else '/tv'

            content_data.update({
                'qualityProfileId': 7,  # Usar perfil "UHD - HD" para calidad mínima 1080p
                'languageProfileId': 1,  # Requerido para Sonarr
                'rootFolderPath': root_path,
                'monitored': True,
                'addOptions': {'searchForMissingEpisodes': True}
            })

        logger.info(f"Datos finales para agregar: {content_data}")
        result = await self._make_request(endpoint, 'POST', content_data)

        if result is None:
            logger.error(f"❌ Error al agregar contenido en {self.service_name}")
            return {'success': False, 'error': 'api_error', 'message': 'Error de conexión con la API'}
        elif isinstance(result, dict) and result.get('error') == 'duplicate':
            logger.warning(f"⚠️ Contenido ya existe en {self.service_name}")
            return {'success': False, 'error': 'duplicate', 'message': result.get('message', 'El contenido ya existe')}
        else:
            logger.info(f"✅ Contenido agregado exitosamente en {self.service_name}")
            return {'success': True, 'id': result.get('id'), 'title': result.get('title')}

    async def get_releases(self, content_id: int):
        endpoint = f"release?movieId={content_id}" if self.service_name == 'Radarr' else f"release?seriesId={content_id}"
        return await self._make_request(endpoint)

    async def download_release(self, release: Dict):
        data = {'guid': release.get('guid'), 'indexerId': release.get('indexerId')}
        return await self._make_request('release', 'POST', data)

radarr_api = ArrAPI(RADARR_URL, RADARR_API_KEY, 'Radarr')
sonarr_api = ArrAPI(SONARR_URL, SONARR_API_KEY, 'Sonarr')


# --- 5. FUNCIONES DE AYUDA ---
def cleanup_old_interactions():
    """Limpiar interacciones pendientes antiguas para evitar problemas de memoria"""
    import time
    current_time = time.time()
    to_remove = []

    for interaction_id, data in pending_interactions.items():
        # Remover interacciones más antiguas de 30 minutos
        if hasattr(data, 'timestamp'):
            if current_time - data.get('timestamp', 0) > 1800:  # 30 minutos
                to_remove.append(interaction_id)
        else:
            # Si no tiene timestamp, agregar uno ahora
            data['timestamp'] = current_time

    for interaction_id in to_remove:
        logger.info(f"Limpiando interacción antigua: {interaction_id}")
        del pending_interactions[interaction_id]

def escape_markdown(text: str) -> str:
    if not text:
        return ""
    # Caracteres que necesitan escape en MarkdownV2
    escape_chars = r'_*[]()~`>#+-=|{}.!\\'
    escaped_text = ""
    for char in str(text):
        if char in escape_chars:
            escaped_text += f'\\{char}'
        else:
            escaped_text += char
    return escaped_text

def format_file_size(size_bytes: int) -> str:
    if size_bytes == 0:
        return "0B"
    size_name = ("B", "KB", "MB", "GB", "TB")
    i = int(math.floor(math.log(size_bytes, 1024)))
    p = math.pow(1024, i)
    s = round(size_bytes / p, 2)
    return f"{s} {size_name[i]}"

async def send_telegram_message(text: str, reply_markup=None):
    if telegram_app:
        await telegram_app.bot.send_message(
            chat_id=TELEGRAM_CHAT_ID, text=text,
            parse_mode=ParseMode.MARKDOWN_V2, reply_markup=reply_markup
        )

# --- 6. MANEJADORES DE WEBHOOKS Y PROXY DE IMÁGENES ---
@app.get("/health")
async def health_check():
    return {"status": "ok", "timestamp": datetime.now().isoformat()}

@app.get("/image-proxy/{service}/{image_hash:path}")
async def image_proxy(service: str, image_hash: str):
    """Proxy para servir imágenes de Sonarr/Radarr con autenticación"""
    try:
        # Determinar la API correcta
        api = radarr_api if service.lower() == 'radarr' else sonarr_api

        # Construir URL de imagen
        image_url = f"{api.base_url}/MediaCoverProxy/{image_hash}"
        logger.info(f"Intentando descargar imagen: {image_url}")

        # Estrategia 1: Intentar con API key
        async with httpx.AsyncClient() as client:
            response = await client.get(
                image_url,
                headers={'X-Api-Key': api.api_key},
                follow_redirects=False,
                timeout=10
            )

            if response.status_code == 200:
                logger.info(f"Imagen descargada exitosamente con API key")
                return Response(
                    content=response.content,
                    media_type=response.headers.get('content-type', 'image/jpeg'),
                    headers={
                        'Cache-Control': 'public, max-age=86400',  # Cache por 24 horas
                        'Content-Length': str(len(response.content))
                    }
                )

            # Estrategia 2: Si hay redirección, intentar sin autenticación
            elif response.status_code == 302:
                logger.info(f"Redirección detectada, intentando acceso directo")
                response = await client.get(image_url, follow_redirects=True, timeout=10)

                if response.status_code == 200:
                    logger.info(f"Imagen descargada exitosamente sin autenticación")
                    return Response(
                        content=response.content,
                        media_type=response.headers.get('content-type', 'image/jpeg'),
                        headers={
                            'Cache-Control': 'public, max-age=86400',
                            'Content-Length': str(len(response.content))
                        }
                    )

            # Estrategia 3: Intentar con la URL remota directa si está disponible
            logger.warning(f"No se pudo acceder a imagen local, código: {response.status_code}")
            return RedirectResponse(url="https://i.imgur.com/1Y2TfCj.png")

    except Exception as e:
        logger.error(f"Error en proxy de imagen {service}/{image_hash}: {e}")
        return RedirectResponse(url="https://i.imgur.com/1Y2TfCj.png")

@app.post("/radarr")
async def handle_radarr_webhook(request: Request):
    return await handle_webhook('Radarr', request)

@app.post("/sonarr")
async def handle_sonarr_webhook(request: Request):
    return await handle_webhook('Sonarr', request)

async def handle_webhook(service: str, request: Request):
    try:
        payload = await request.json()
        event_type = payload.get('eventType', 'N/A')
        logger.info(f"Webhook de {service} recibido. Evento: {event_type}. Payload: {json.dumps(payload, indent=2)}")

        if event_type == 'Grab':
            if payload.get('release', {}).get('rejected', False):
                await handle_grab_failure(payload, service)
        elif event_type in ['Download', 'Upgrade']:
            await handle_download_success(payload, service, event_type)
        
        return JSONResponse(content={'status': 'ok'}, status_code=200)
    except Exception as e:
        logger.error(f"Error procesando webhook de {service}: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Error interno del servidor")

async def handle_grab_failure(payload: Dict, service_name: str):
    release = payload.get('release', {})
    rejections = release.get('rejectionReasons', [])
    if not rejections: return

    title = release.get('releaseTitle', 'N/A')
    reasons_str = ", ".join(rejections)
    message = (
        f"‼️ *Rechazado por {service_name}:* {escape_markdown(title)}\n"
        f"   📏 Tamaño: {format_file_size(release.get('size', 0))}\n"
        f"   👤 Uploader: {escape_markdown(release.get('customInfo', {}).get('uploader', 'N/A'))}\n"
        f"   📜 Motivos: {escape_markdown(reasons_str)}"
    )
    
    content_id = payload.get('movie', {}).get('id') if service_name == 'Radarr' else payload.get('series', {}).get('id')
    if not content_id:
        await send_telegram_message(message)
        return

    releases = await (radarr_api.get_releases(content_id) if service_name == 'Radarr' else sonarr_api.get_releases(content_id))
    if not releases:
        await send_telegram_message(message)
        return

    text = f"{message}\n\n🔎 *Buscando alternativas...*"
    keyboard = []
    interaction_id = f"download_{service_name.lower()}_{content_id}"
    pending_interactions[interaction_id] = releases

    for i, rel in enumerate(releases[:5]):
        button_text = f"⬇️ {escape_markdown(rel['releaseTitle'][:50])}... ({format_file_size(rel.get('size',0))})"
        callback_data = f"{interaction_id}_{i}"
        keyboard.append([InlineKeyboardButton(button_text, callback_data=callback_data)])
    
    keyboard.append([InlineKeyboardButton("❌ Cancelar", callback_data="cancel")])
    await send_telegram_message(text, InlineKeyboardMarkup(keyboard))

async def handle_download_success(payload: Dict, service_name: str, event_type: str):
    prefix = "🚀 ¡Mejora de calidad!\\n\\n" if event_type == 'Upgrade' else ""
    try:
        if service_name == 'Radarr':
            title = payload.get('movie', {}).get('title', 'N/A')
            movie_file = payload.get('movieFile', {})
            if isinstance(movie_file, dict):
                quality_info = movie_file.get('quality', {})
                if isinstance(quality_info, dict):
                    quality = quality_info.get('quality', {}).get('name', 'N/A') if isinstance(quality_info.get('quality'), dict) else str(quality_info.get('quality', 'N/A'))
                else:
                    quality = str(quality_info)
            else:
                quality = 'N/A'
            message = f"{prefix}✅ Película importada: *{escape_markdown(title)}*\\n   ✨ Calidad: {escape_markdown(quality)}"
        else: # Sonarr
            series_title = payload.get('series', {}).get('title', 'N/A')
            episodes = " y ".join([f"S{ep.get('seasonNumber'):02d}E{ep.get('episodeNumber'):02d}" for ep in payload.get('episodes', [])])
            episode_file = payload.get('episodeFile', {})
            if isinstance(episode_file, dict):
                quality_info = episode_file.get('quality', {})
                if isinstance(quality_info, dict):
                    quality = quality_info.get('quality', {}).get('name', 'N/A') if isinstance(quality_info.get('quality'), dict) else str(quality_info.get('quality', 'N/A'))
                else:
                    quality = str(quality_info)
            else:
                quality = 'N/A'
            message = f"{prefix}✅ Episodio importado: *{escape_markdown(series_title)} {episodes}*\\n   ✨ Calidad: {escape_markdown(quality)}"

        await send_telegram_message(message)
    except Exception as e:
        logger.error(f"Error procesando descarga exitosa: {e}", exc_info=True)
        await send_telegram_message(f"✅ Contenido importado en {service_name}")


# --- 7. COMANDOS DE TELEGRAM ---
async def start_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    logger.info(f"Comando /start recibido de {update.effective_user.first_name if update.effective_user else 'Usuario desconocido'}")
    await update.message.reply_text("Hola, mi pastelito🤙. Usa /estado o /agregar [peli/serie].")

async def estado_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    logger.info("Comando /estado recibido")
    radarr_status = await radarr_api.get_system_status()
    sonarr_status = await sonarr_api.get_system_status()
    radarr_msg = f"🎬 *Radarr:* {'✅ Online' if radarr_status and radarr_status.get('appName') == 'Radarr' else '❌ Offline'}"
    sonarr_msg = f"📺 *Sonarr:* {'✅ Online' if sonarr_status and sonarr_status.get('appName') == 'Sonarr' else '❌ Offline'}"
    await update.message.reply_text(f"{radarr_msg}\n{sonarr_msg}", parse_mode=ParseMode.MARKDOWN_V2)

async def agregar_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    logger.info(f"Comando /agregar recibido con args: {context.args}")
    if not update.message:
        logger.error("Comando /agregar recibido sin mensaje")
        return

    if not context.args:
        await update.message.reply_text("Por favor, dime qué buscar. Uso: /agregar [peli o serie]")
        return

    search_term = " ".join(context.args)
    logger.info(f"Iniciando búsqueda para: {search_term}")
    await update.message.reply_text(f"Buscando '{escape_markdown(search_term)}' en Radarr y Sonarr...")

    # Crear botones para elegir el servicio
    keyboard = [
        [InlineKeyboardButton("🎬 Buscar en Radarr (Películas)", callback_data=f"search_radarr_{search_term}")],
        [InlineKeyboardButton("📺 Buscar en Sonarr (Series)", callback_data=f"search_sonarr_{search_term}")],
        [InlineKeyboardButton("❌ Cancelar", callback_data="cancel")]
    ]
    await send_telegram_message("¿Dónde quieres buscar?", InlineKeyboardMarkup(keyboard))

async def show_search_result(context: ContextTypes.DEFAULT_TYPE, interaction_id: str):
    try:
        interaction_data = pending_interactions[interaction_id]
        results = interaction_data['results']
        current_index = interaction_data['index']
        chat_id = interaction_data['chat_id']
        message_id = interaction_data['message_id']
        api = interaction_data['api']

        item = results[current_index]
        logger.info(f"Mostrando resultado {current_index + 1}/{len(results)}: {item.get('title', 'N/A')}")
        media_to_send: Any = "https://i.imgur.com/1Y2TfCj.png" # Imagen placeholder por defecto

        if item.get('images'):
            try:
                # Buscar imagen poster
                poster_img = next(img for img in item['images'] if img.get('coverType') == 'poster')

                # Estrategia 1: Usar URL remota directa si está disponible
                if poster_img.get('remoteUrl'):
                    media_to_send = poster_img['remoteUrl']
                    logger.info(f"Usando URL remota directa: {media_to_send}")

                # Estrategia 2: Usar nuestro proxy para URLs locales
                elif poster_img.get('url') and '/MediaCoverProxy/' in poster_img['url']:
                    image_hash = poster_img['url'].split('/MediaCoverProxy/')[-1]
                    service_name = api.service_name.lower()
                    proxy_url = f"http://localhost:8082/image-proxy/{service_name}/{image_hash}"
                    media_to_send = proxy_url
                    logger.info(f"Usando proxy de imagen: {proxy_url}")

                else:
                    logger.warning(f"No se encontró URL válida para imagen: {poster_img}")

            except (StopIteration, KeyError) as e:
                logger.warning(f"No se pudo procesar la carátula para '{item.get('title')}'. Error: {e}")
                # Se usará la imagen placeholder definida arriba

        # Crear caption sin caracteres problemáticos
        title = item.get('title', 'N/A')
        year = item.get('year', 'N/A')
        caption = f"*{escape_markdown(title)}* \\({year}\\)\n\n Selecciona una acción:"

        logger.info(f"Creando media con caption: {caption[:100]}...")
        media = InputMediaPhoto(media=media_to_send, caption=caption, parse_mode=ParseMode.MARKDOWN_V2)

        # Construcción del teclado de paginación
        nav_buttons = []
        if current_index > 0:
            nav_buttons.append(InlineKeyboardButton("◀️ Anterior", callback_data=f"{interaction_id}_prev"))

        nav_buttons.append(InlineKeyboardButton("➕ Añadir", callback_data=f"{interaction_id}_add_{current_index}"))

        if current_index < len(results) - 1:
            nav_buttons.append(InlineKeyboardButton("Siguiente ▶️", callback_data=f"{interaction_id}_next"))

        keyboard = [
            nav_buttons,
            [InlineKeyboardButton("❌ Cancelar", callback_data=f"{interaction_id}_cancel")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)

        try:
            await context.bot.edit_message_media(
                media=media,
                chat_id=chat_id,
                message_id=message_id,
                reply_markup=reply_markup
            )
        except Exception as e:
            logger.error(f"Error al editar el mensaje con la carátula: {e}", exc_info=True)
            try:
                await context.bot.edit_message_text(
                    text=f"Error al mostrar la carátula. {caption}",
                    chat_id=chat_id,
                    message_id=message_id,
                    reply_markup=reply_markup,
                    parse_mode=ParseMode.MARKDOWN_V2
                )
            except Exception as e2:
                logger.error(f"Error en fallback de show_search_result: {e2}", exc_info=True)
                # Último recurso: mensaje sin formato
                await context.bot.edit_message_text(
                    text=f"❌ Error al mostrar resultado. Título: {item.get('title', 'N/A')} ({item.get('year', 'N/A')})",
                    chat_id=chat_id,
                    message_id=message_id
                )
    except Exception as e:
        logger.error(f"Error general en show_search_result: {e}", exc_info=True)


async def button_callback(update: Update, context: ContextTypes.DEFAULT_TYPE):
    query = update.callback_query
    await query.answer()
    data = query.data

    logger.info(f"Callback recibido: {data}")

    # Limpiar interacciones antiguas
    cleanup_old_interactions()

    if data == "cancel":
        await query.edit_message_text("❌ Operación cancelada.")
        return

    # --- Flujo de Búsqueda Visual ---
    if data.startswith("search_"):
        try:
            parts = data.split('_', 2)
            service, search_term = parts[1], parts[2]
            api = radarr_api if service == 'radarr' else sonarr_api

            logger.info(f"Iniciando búsqueda en {service} para: {search_term}")
            placeholder_message = await query.message.edit_text(f"Buscando '{escape_markdown(search_term)}' en {service.capitalize()}...")

            results = await api.search_content(search_term)
            logger.info(f"Resultados obtenidos de {service}: {len(results) if results else 0}")

            if not results:
                await query.message.edit_text(f"🤷 No se encontraron resultados para '{escape_markdown(search_term)}' en {service.capitalize()}.")
                return

            interaction_id = f"visual_{service}_{query.id}"
            pending_interactions[interaction_id] = {
                'results': results,
                'index': 0,
                'chat_id': query.message.chat_id,
                'message_id': placeholder_message.message_id,
                'api': api,
                'timestamp': time.time()
            }
            logger.info(f"Mostrando resultado para interaction_id: {interaction_id}")
            await show_search_result(context, interaction_id)
            return
        except Exception as e:
            logger.error(f"Error en búsqueda {service}: {e}", exc_info=True)
            await query.message.edit_text(f"❌ Error al buscar en {service.capitalize()}. Inténtalo de nuevo.")
            return

    # --- Manejo de Callbacks de Interacción ---
    parts = data.split('_')

    # Detectar el tipo de acción correctamente
    if len(parts) >= 2 and parts[-2] == 'add':
        # Formato: interaction_id_add_index
        action = 'add'
        interaction_id = "_".join(parts[:-2])
        selected_index = int(parts[-1])
    elif parts[-1] in ['prev', 'next', 'cancel']:
        # Formato: interaction_id_action
        action = parts[-1]
        interaction_id = "_".join(parts[:-1])
        selected_index = None
    else:
        # Formato desconocido, intentar como antes
        action = parts[-1]
        interaction_id = "_".join(parts[:-1])
        selected_index = None

    logger.info(f"Callback procesado - Action: {action}, Interaction ID: {interaction_id}, Index: {selected_index}")

    if interaction_id not in pending_interactions:
        # Podría ser una interacción antigua, como la de descarga por fallo
        original_interaction_id = "_".join(parts[:-1])
        if original_interaction_id in pending_interactions:
             selected_index = int(parts[-1])
             selected_item = pending_interactions[original_interaction_id][selected_index]
             api = radarr_api if 'radarr' in original_interaction_id else sonarr_api
             success = await api.download_release(selected_item)
             message = f"✅ Descarga de '{escape_markdown(selected_item['releaseTitle'][:40])}...' iniciada." if success else "❌ Error al iniciar la descarga."
             await query.edit_message_text(message, parse_mode=ParseMode.MARKDOWN_V2)
             del pending_interactions[original_interaction_id]
        return

    interaction_data = pending_interactions[interaction_id]

    if action == 'cancel':
        await query.message.delete()
        del pending_interactions[interaction_id]
        return

    if action == 'next':
        interaction_data['index'] += 1
        await show_search_result(context, interaction_id)
    elif action == 'prev':
        interaction_data['index'] -= 1
        await show_search_result(context, interaction_id)
    elif action == 'add':
        if selected_index is None:
            logger.error("Error: selected_index es None para acción 'add'")
            return

        selected_item = interaction_data['results'][selected_index]
        api = interaction_data['api']

        logger.info(f"Usuario solicitó agregar: {selected_item.get('title')} en {api.service_name}")
        logger.info(f"Datos del item seleccionado: {selected_item}")

        result = await api.add_content(selected_item)

        if result.get('success'):
            message = f"✅ Añadido y buscando '{escape_markdown(selected_item.get('title'))}'\\."
            logger.info(f"✅ Contenido agregado exitosamente: {selected_item.get('title')}")
        elif result.get('error') == 'duplicate':
            message = f"⚠️ '{escape_markdown(selected_item.get('title'))}' ya está en tu biblioteca\\."
            logger.warning(f"⚠️ Contenido duplicado: {selected_item.get('title')}")
        else:
            error_msg = result.get('message', 'Error desconocido')
            message = f"❌ Error al añadir '{escape_markdown(selected_item.get('title'))}': {escape_markdown(error_msg)}\\."
            logger.error(f"❌ Error al agregar contenido: {selected_item.get('title')} - {error_msg}")

        await context.bot.edit_message_text(
            text=message,
            chat_id=interaction_data['chat_id'],
            message_id=interaction_data['message_id'],
            parse_mode=ParseMode.MARKDOWN_V2
        )
        del pending_interactions[interaction_id]


# --- 8. FUNCIÓN PRINCIPAL DE ARRANQUE ---
async def main():
    global telegram_app
    telegram_app = Application.builder().token(TELEGRAM_BOT_TOKEN).build()
    
    telegram_app.add_handler(CommandHandler("start", start_command))
    telegram_app.add_handler(CommandHandler("estado", estado_command))
    telegram_app.add_handler(CommandHandler("agregar", agregar_command))
    telegram_app.add_handler(CallbackQueryHandler(button_callback))

    config = uvicorn.Config(app, host="0.0.0.0", port=WEBHOOK_PORT, log_level="info")
    server = uvicorn.Server(config)

    async with telegram_app:
        logger.info("Iniciando bot de Telegram y servidor webhook...")
        await telegram_app.start()
        await telegram_app.updater.start_polling()
        
        await send_telegram_message("🤖 *Arr\\-Bot iniciado y en línea*\\.\n\nListo para recibir notificaciones y comandos\\.")
        
        await server.serve()
        
        # Cierre limpio del cliente httpx
        await radarr_api.client.aclose()
        await sonarr_api.client.aclose()
        
        await telegram_app.updater.stop()
        await telegram_app.stop()

# --- 9. PUNTO DE ENTRADA DEL SCRIPT ---
if __name__ == "__main__":
    logger.info("Lanzando Arr-Bot...")
    try:
        asyncio.run(main())
    except Exception as e:
        logger.critical(f"El bot ha crasheado: {e}", exc_info=True)