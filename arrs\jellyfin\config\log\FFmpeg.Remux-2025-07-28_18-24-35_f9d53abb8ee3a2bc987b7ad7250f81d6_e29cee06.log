{"Protocol":0,"Id":"f9d53abb8ee3a2bc987b7ad7250f81d6","Path":"/CONTENIDO/SERIES/Dexter/Season 4/Dexter - S04E07 - Slack Tide Bluray-1080p.mp4","EncoderPath":null,"EncoderProtocol":null,"Type":0,"Container":"mov,mp4,m4a,3gp,3g2,mj2","Size":2446460386,"Name":"Dexter - S04E07 - Slack Tide Bluray-1080p","IsRemote":false,"ETag":"67c1ec09837261a6ebbaabb9486afbf9","RunTimeTicks":31612586670,"ReadAtNativeFramerate":false,"IgnoreDts":false,"IgnoreIndex":false,"GenPtsInput":false,"SupportsTranscoding":true,"SupportsDirectStream":true,"SupportsDirectPlay":true,"IsInfiniteStream":false,"UseMostCompatibleTranscodingProfile":false,"RequiresOpening":false,"OpenToken":null,"RequiresClosing":false,"LiveStreamId":null,"BufferMs":null,"RequiresLooping":false,"SupportsProbing":true,"VideoType":0,"IsoType":null,"Video3DFormat":null,"MediaStreams":[{"Codec":"h264","CodecTag":"avc1","Language":"und","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/90000","CodecTimeBase":null,"Title":null,"VideoRange":1,"VideoRangeType":1,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":null,"LocalizedDefault":null,"LocalizedForced":null,"LocalizedExternal":null,"LocalizedHearingImpaired":null,"DisplayTitle":"1080p H264 SDR","NalLengthSize":"4","IsInterlaced":false,"IsAVC":true,"ChannelLayout":null,"BitRate":5928156,"BitDepth":8,"RefFrames":1,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":true,"IsForced":false,"IsHearingImpaired":false,"Height":1080,"Width":1920,"AverageFrameRate":23.976025,"RealFrameRate":23.976025,"ReferenceFrameRate":23.976025,"Profile":"High","Type":1,"AspectRatio":"16:9","Index":0,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":false,"Path":null,"PixelFormat":"yuv420p","Level":40,"IsAnamorphic":false},{"Codec":"aac","CodecTag":"mp4a","Language":"eng","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/48000","CodecTimeBase":null,"Title":null,"VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":null,"LocalizedDefault":"Predeterminado","LocalizedForced":null,"LocalizedExternal":"Externo","LocalizedHearingImpaired":null,"DisplayTitle":"English - HE-AAC - Stereo - Predeterminado","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":"stereo","BitRate":127999,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":2,"SampleRate":48000,"IsDefault":true,"IsForced":false,"IsHearingImpaired":false,"Height":null,"Width":null,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":"HE-AAC","Type":0,"AspectRatio":null,"Index":1,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":false,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"aac","CodecTag":"mp4a","Language":"spa","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/48000","CodecTimeBase":null,"Title":null,"VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":null,"LocalizedDefault":"Predeterminado","LocalizedForced":null,"LocalizedExternal":"Externo","LocalizedHearingImpaired":null,"DisplayTitle":"Spanish - HE-AAC - Stereo","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":"stereo","BitRate":127999,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":2,"SampleRate":48000,"IsDefault":false,"IsForced":false,"IsHearingImpaired":false,"Height":null,"Width":null,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":"HE-AAC","Type":0,"AspectRatio":null,"Index":2,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":false,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"mov_text","CodecTag":"tx3g","Language":"spa","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/90000","CodecTimeBase":null,"Title":null,"VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":"Indefinido","LocalizedDefault":"Predeterminado","LocalizedForced":"Forzado","LocalizedExternal":"Externo","LocalizedHearingImpaired":"Discapacidad Auditiva","DisplayTitle":"Spanish - Predeterminado - MOV_TEXT","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":null,"BitRate":70,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":true,"IsForced":false,"IsHearingImpaired":false,"Height":0,"Width":0,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":2,"AspectRatio":null,"Index":3,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":true,"SupportsExternalStream":true,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"mov_text","CodecTag":"tx3g","Language":"eng","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/90000","CodecTimeBase":null,"Title":null,"VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":"Indefinido","LocalizedDefault":"Predeterminado","LocalizedForced":"Forzado","LocalizedExternal":"Externo","LocalizedHearingImpaired":"Discapacidad Auditiva","DisplayTitle":"English - MOV_TEXT","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":null,"BitRate":71,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":false,"IsForced":false,"IsHearingImpaired":false,"Height":0,"Width":0,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":2,"AspectRatio":null,"Index":4,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":true,"SupportsExternalStream":true,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"mjpeg","CodecTag":null,"Language":null,"ColorRange":null,"ColorSpace":"bt470bg","ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/90000","CodecTimeBase":null,"Title":null,"VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":null,"LocalizedDefault":null,"LocalizedForced":null,"LocalizedExternal":null,"LocalizedHearingImpaired":null,"DisplayTitle":null,"NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":null,"BitRate":null,"BitDepth":8,"RefFrames":1,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":false,"IsForced":false,"IsHearingImpaired":false,"Height":197,"Width":350,"AverageFrameRate":null,"RealFrameRate":90000,"ReferenceFrameRate":90000,"Profile":"Baseline","Type":3,"AspectRatio":"16:9","Index":5,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":false,"Path":null,"PixelFormat":"yuvj420p","Level":-99,"IsAnamorphic":false}],"MediaAttachments":[{"Codec":"mjpeg","CodecTag":"[0][0][0][0]","Comment":null,"Index":5,"FileName":null,"MimeType":null,"DeliveryUrl":null}],"Formats":[],"Bitrate":6191104,"FallbackMaxStreamingBitrate":null,"Timestamp":null,"RequiredHttpHeaders":{},"TranscodingUrl":null,"TranscodingSubProtocol":0,"TranscodingContainer":null,"AnalyzeDurationMs":null,"DefaultAudioStreamIndex":null,"DefaultSubtitleStreamIndex":null,"HasSegments":false}

/usr/lib/jellyfin-ffmpeg/ffmpeg -analyzeduration 200M -probesize 1G -ss 00:45:54.500 -noaccurate_seek -fflags +genpts -f mov,mp4,m4a,3gp,3g2,mj2  -i file:"/CONTENIDO/SERIES/Dexter/Season 4/Dexter - S04E07 - Slack Tide Bluray-1080p.mp4" -map_metadata -1 -map_chapters -1 -threads 0 -map 0:0 -map 0:2 -map -0:s -codec:v:0 copy -bsf:v h264_mp4toannexb -start_at_zero -codec:a:0 copy -copyts -avoid_negative_ts disabled -max_muxing_queue_size 2048 -f hls -max_delay 5000000 -hls_time 6 -hls_segment_type fmp4 -hls_fmp4_init_filename "4e5551e160f1eed7a086927ce45703d0-1.mp4" -start_number 459 -hls_segment_filename "/cache/transcodes/4e5551e160f1eed7a086927ce45703d0%d.mp4" -hls_playlist_type vod -hls_list_size 0 -y "/cache/transcodes/4e5551e160f1eed7a086927ce45703d0.m3u8"


ffmpeg version 7.0.2-Jellyfin Copyright (c) 2000-2024 the FFmpeg developers
  built with gcc 12 (Debian 12.2.0-14)
  configuration: --prefix=/usr/lib/jellyfin-ffmpeg --target-os=linux --extra-version=Jellyfin --disable-doc --disable-ffplay --disable-ptx-compression --disable-static --disable-libxcb --disable-sdl2 --disable-xlib --enable-lto=auto --enable-gpl --enable-version3 --enable-shared --enable-gmp --enable-gnutls --enable-chromaprint --enable-opencl --enable-libdrm --enable-libxml2 --enable-libass --enable-libfreetype --enable-libfribidi --enable-libfontconfig --enable-libharfbuzz --enable-libbluray --enable-libmp3lame --enable-libopus --enable-libtheora --enable-libvorbis --enable-libopenmpt --enable-libdav1d --enable-libsvtav1 --enable-libwebp --enable-libvpx --enable-libx264 --enable-libx265 --enable-libzvbi --enable-libzimg --enable-libfdk-aac --arch=amd64 --enable-libshaderc --enable-libplacebo --enable-vulkan --enable-vaapi --enable-amf --enable-libvpl --enable-ffnvcodec --enable-cuda --enable-cuda-llvm --enable-cuvid --enable-nvdec --enable-nvenc
  libavutil      59.  8.100 / 59.  8.100
  libavcodec     61.  3.100 / 61.  3.100
  libavformat    61.  1.100 / 61.  1.100
  libavdevice    61.  1.100 / 61.  1.100
  libavfilter    10.  1.100 / 10.  1.100
  libswscale      8.  1.100 /  8.  1.100
  libswresample   5.  1.100 /  5.  1.100
  libpostproc    58.  1.100 / 58.  1.100
[mov,mp4,m4a,3gp,3g2,mj2 @ 0x61859dcbc8c0] stream 0, timescale not set
Input #0, mov,mp4,m4a,3gp,3g2,mj2, from 'file:/CONTENIDO/SERIES/Dexter/Season 4/Dexter - S04E07 - Slack Tide Bluray-1080p.mp4':
  Metadata:
    major_brand     : isom
    minor_version   : 512
    compatible_brands: isomiso2avc1mp41
    title           : Dexter [S04.E07] - Marea Perezosa
    encoder         : Lavf60.16.100
    description     : Dexter está cómodo con su vida, pero se siente frustrado en la búsqueda de una nueva víctima. Debra tiene un renovado interés por el turbio pasado de su padre.
    episode_id      : 7/4
  Duration: 00:52:41.26, start: 0.000000, bitrate: 6191 kb/s
  Stream #0:0[0x1](und): Video: h264 (High) (avc1 / 0x31637661), yuv420p(progressive), 1920x1080, 5928 kb/s, 23.98 fps, 23.98 tbr, 90k tbn (default)
      Metadata:
        handler_name    : VideoHandler
        vendor_id       : [0][0][0][0]
  Stream #0:1[0x2](eng): Audio: aac (HE-AAC) (mp4a / 0x6134706D), 48000 Hz, stereo, fltp, 127 kb/s (default)
      Metadata:
        handler_name    : SoundHandler
        vendor_id       : [0][0][0][0]
  Stream #0:2[0x3](spa): Audio: aac (HE-AAC) (mp4a / 0x6134706D), 48000 Hz, stereo, fltp, 127 kb/s
      Metadata:
        handler_name    : SoundHandler
        vendor_id       : [0][0][0][0]
  Stream #0:3[0x4](spa): Subtitle: mov_text (tx3g / 0x67337874), 0 kb/s (default)
      Metadata:
        handler_name    : SubtitleHandler
  Stream #0:4[0x5](eng): Subtitle: mov_text (tx3g / 0x67337874), 0 kb/s
      Metadata:
        handler_name    : SubtitleHandler
  Stream #0:5[0x0]: Video: mjpeg (Baseline), yuvj420p(pc, bt470bg/unknown/unknown), 350x197, 90k tbr, 90k tbn (attached pic)
[out#0/hls @ 0x61859dd4df40] Codec AVOption threads (set the number of threads) has not been used for any stream. The most likely reason is either wrong type (e.g. a video option with no video streams) or that it is a private option of some encoder which was not actually used for any stream.
Stream mapping:
  Stream #0:0 -> #0:0 (copy)
  Stream #0:2 -> #0:1 (copy)
[hls @ 0x61859dcd47c0] Opening '/cache/transcodes/4e5551e160f1eed7a086927ce45703d0-1.mp4' for writing
Output #0, hls, to '/cache/transcodes/4e5551e160f1eed7a086927ce45703d0.m3u8':
  Metadata:
    encoder         : Lavf61.1.100
  Stream #0:0: Video: h264 (High) (avc1 / 0x31637661), yuv420p(progressive), 1920x1080, q=2-31, 5928 kb/s, 23.98 fps, 23.98 tbr, 90k tbn (default)
  Stream #0:1: Audio: aac (HE-AAC) (mp4a / 0x6134706D), 48000 Hz, stereo, fltp, 127 kb/s
Press [q] to stop, [?] for help
[hls @ 0x61859dcd47c0] Opening '/cache/transcodes/4e5551e160f1eed7a086927ce45703d0459.mp4' for writing
[hls @ 0x61859dcd47c0] Opening '/cache/transcodes/4e5551e160f1eed7a086927ce45703d0460.mp4' for writing
[hls @ 0x61859dcd47c0] Opening '/cache/transcodes/4e5551e160f1eed7a086927ce45703d0461.mp4' for writing
[hls @ 0x61859dcd47c0] Opening '/cache/transcodes/4e5551e160f1eed7a086927ce45703d0462.mp4' for writing
[hls @ 0x61859dcd47c0] Opening '/cache/transcodes/4e5551e160f1eed7a086927ce45703d0463.mp4' for writing
size=N/A time=00:00:00.00 bitrate=N/A speed=   0x    
[hls @ 0x61859dcd47c0] Opening '/cache/transcodes/4e5551e160f1eed7a086927ce45703d0464.mp4' for writing
[hls @ 0x61859dcd47c0] Opening '/cache/transcodes/4e5551e160f1eed7a086927ce45703d0465.mp4' for writing
size=N/A time=00:00:13.01 bitrate=N/A speed=  13x    
[hls @ 0x61859dcd47c0] Opening '/cache/transcodes/4e5551e160f1eed7a086927ce45703d0466.mp4' for writing
[hls @ 0x61859dcd47c0] Opening '/cache/transcodes/4e5551e160f1eed7a086927ce45703d0467.mp4' for writing
[hls @ 0x61859dcd47c0] Opening '/cache/transcodes/4e5551e160f1eed7a086927ce45703d0468.mp4' for writing
[hls @ 0x61859dcd47c0] Opening '/cache/transcodes/4e5551e160f1eed7a086927ce45703d0469.mp4' for writing
[hls @ 0x61859dcd47c0] Opening '/cache/transcodes/4e5551e160f1eed7a086927ce45703d0470.mp4' for writing
[hls @ 0x61859dcd47c0] Opening '/cache/transcodes/4e5551e160f1eed7a086927ce45703d0471.mp4' for writing
[hls @ 0x61859dcd47c0] Opening '/cache/transcodes/4e5551e160f1eed7a086927ce45703d0472.mp4' for writing
[hls @ 0x61859dcd47c0] Opening '/cache/transcodes/4e5551e160f1eed7a086927ce45703d0473.mp4' for writing
[hls @ 0x61859dcd47c0] Opening '/cache/transcodes/4e5551e160f1eed7a086927ce45703d0474.mp4' for writing
[hls @ 0x61859dcd47c0] Opening '/cache/transcodes/4e5551e160f1eed7a086927ce45703d0475.mp4' for writing
size=N/A time=00:00:33.70 bitrate=N/A speed=22.4x    
[hls @ 0x61859dcd47c0] Opening '/cache/transcodes/4e5551e160f1eed7a086927ce45703d0476.mp4' for writing
[hls @ 0x61859dcd47c0] Opening '/cache/transcodes/4e5551e160f1eed7a086927ce45703d0477.mp4' for writing
[hls @ 0x61859dcd47c0] Opening '/cache/transcodes/4e5551e160f1eed7a086927ce45703d0478.mp4' for writing
[hls @ 0x61859dcd47c0] Opening '/cache/transcodes/4e5551e160f1eed7a086927ce45703d0479.mp4' for writing
[hls @ 0x61859dcd47c0] Opening '/cache/transcodes/4e5551e160f1eed7a086927ce45703d0480.mp4' for writing
[hls @ 0x61859dcd47c0] Opening '/cache/transcodes/4e5551e160f1eed7a086927ce45703d0481.mp4' for writing
[hls @ 0x61859dcd47c0] Opening '/cache/transcodes/4e5551e160f1eed7a086927ce45703d0482.mp4' for writing
[hls @ 0x61859dcd47c0] Opening '/cache/transcodes/4e5551e160f1eed7a086927ce45703d0483.mp4' for writing
[hls @ 0x61859dcd47c0] Opening '/cache/transcodes/4e5551e160f1eed7a086927ce45703d0484.mp4' for writing
[hls @ 0x61859dcd47c0] Opening '/cache/transcodes/4e5551e160f1eed7a086927ce45703d0485.mp4' for writing
[hls @ 0x61859dcd47c0] Opening '/cache/transcodes/4e5551e160f1eed7a086927ce45703d0486.mp4' for writing
[hls @ 0x61859dcd47c0] Opening '/cache/transcodes/4e5551e160f1eed7a086927ce45703d0487.mp4' for writing
[hls @ 0x61859dcd47c0] Opening '/cache/transcodes/4e5551e160f1eed7a086927ce45703d0488.mp4' for writing
[hls @ 0x61859dcd47c0] Opening '/cache/transcodes/4e5551e160f1eed7a086927ce45703d0489.mp4' for writing
[hls @ 0x61859dcd47c0] Opening '/cache/transcodes/4e5551e160f1eed7a086927ce45703d0490.mp4' for writing
[hls @ 0x61859dcd47c0] Opening '/cache/transcodes/4e5551e160f1eed7a086927ce45703d0491.mp4' for writing
size=N/A time=00:01:19.41 bitrate=N/A speed=39.7x    
[hls @ 0x61859dcd47c0] Opening '/cache/transcodes/4e5551e160f1eed7a086927ce45703d0492.mp4' for writing
[hls @ 0x61859dcd47c0] Opening '/cache/transcodes/4e5551e160f1eed7a086927ce45703d0493.mp4' for writing
[hls @ 0x61859dcd47c0] Opening '/cache/transcodes/4e5551e160f1eed7a086927ce45703d0494.mp4' for writing
[hls @ 0x61859dcd47c0] Opening '/cache/transcodes/4e5551e160f1eed7a086927ce45703d0495.mp4' for writing
[hls @ 0x61859dcd47c0] Opening '/cache/transcodes/4e5551e160f1eed7a086927ce45703d0496.mp4' for writing
[hls @ 0x61859dcd47c0] Opening '/cache/transcodes/4e5551e160f1eed7a086927ce45703d0497.mp4' for writing
[hls @ 0x61859dcd47c0] Opening '/cache/transcodes/4e5551e160f1eed7a086927ce45703d0498.mp4' for writing
size=N/A time=00:01:45.60 bitrate=N/A speed=42.2x    
[hls @ 0x61859dcd47c0] Opening '/cache/transcodes/4e5551e160f1eed7a086927ce45703d0499.mp4' for writing
[hls @ 0x61859dcd47c0] Opening '/cache/transcodes/4e5551e160f1eed7a086927ce45703d0500.mp4' for writing
[hls @ 0x61859dcd47c0] Opening '/cache/transcodes/4e5551e160f1eed7a086927ce45703d0501.mp4' for writing
[hls @ 0x61859dcd47c0] Opening '/cache/transcodes/4e5551e160f1eed7a086927ce45703d0502.mp4' for writing
[hls @ 0x61859dcd47c0] Opening '/cache/transcodes/4e5551e160f1eed7a086927ce45703d0503.mp4' for writing
[hls @ 0x61859dcd47c0] Opening '/cache/transcodes/4e5551e160f1eed7a086927ce45703d0504.mp4' for writing
[hls @ 0x61859dcd47c0] Opening '/cache/transcodes/4e5551e160f1eed7a086927ce45703d0505.mp4' for writing
[hls @ 0x61859dcd47c0] Opening '/cache/transcodes/4e5551e160f1eed7a086927ce45703d0506.mp4' for writing
[hls @ 0x61859dcd47c0] Opening '/cache/transcodes/4e5551e160f1eed7a086927ce45703d0507.mp4' for writing
[hls @ 0x61859dcd47c0] Opening '/cache/transcodes/4e5551e160f1eed7a086927ce45703d0508.mp4' for writing
[hls @ 0x61859dcd47c0] Opening '/cache/transcodes/4e5551e160f1eed7a086927ce45703d0509.mp4' for writing
[hls @ 0x61859dcd47c0] Opening '/cache/transcodes/4e5551e160f1eed7a086927ce45703d0510.mp4' for writing
[hls @ 0x61859dcd47c0] Opening '/cache/transcodes/4e5551e160f1eed7a086927ce45703d0511.mp4' for writing
[hls @ 0x61859dcd47c0] Opening '/cache/transcodes/4e5551e160f1eed7a086927ce45703d0512.mp4' for writing
[hls @ 0x61859dcd47c0] Opening '/cache/transcodes/4e5551e160f1eed7a086927ce45703d0513.mp4' for writing
[hls @ 0x61859dcd47c0] Opening '/cache/transcodes/4e5551e160f1eed7a086927ce45703d0514.mp4' for writing
size=N/A time=00:02:21.03 bitrate=N/A speed=  47x    
[hls @ 0x61859dcd47c0] Opening '/cache/transcodes/4e5551e160f1eed7a086927ce45703d0515.mp4' for writing
[hls @ 0x61859dcd47c0] Opening '/cache/transcodes/4e5551e160f1eed7a086927ce45703d0516.mp4' for writing
[hls @ 0x61859dcd47c0] Opening '/cache/transcodes/4e5551e160f1eed7a086927ce45703d0517.mp4' for writing
[hls @ 0x61859dcd47c0] Opening '/cache/transcodes/4e5551e160f1eed7a086927ce45703d0518.mp4' for writing
[hls @ 0x61859dcd47c0] Opening '/cache/transcodes/4e5551e160f1eed7a086927ce45703d0519.mp4' for writing
[hls @ 0x61859dcd47c0] Opening '/cache/transcodes/4e5551e160f1eed7a086927ce45703d0520.mp4' for writing
[hls @ 0x61859dcd47c0] Opening '/cache/transcodes/4e5551e160f1eed7a086927ce45703d0521.mp4' for writing
[hls @ 0x61859dcd47c0] Opening '/cache/transcodes/4e5551e160f1eed7a086927ce45703d0522.mp4' for writing
[hls @ 0x61859dcd47c0] Opening '/cache/transcodes/4e5551e160f1eed7a086927ce45703d0523.mp4' for writing
[hls @ 0x61859dcd47c0] Opening '/cache/transcodes/4e5551e160f1eed7a086927ce45703d0524.mp4' for writing
size=N/A time=00:02:56.67 bitrate=N/A speed=50.4x    
[hls @ 0x61859dcd47c0] Opening '/cache/transcodes/4e5551e160f1eed7a086927ce45703d0525.mp4' for writing
[hls @ 0x61859dcd47c0] Opening '/cache/transcodes/4e5551e160f1eed7a086927ce45703d0526.mp4' for writing
size=N/A time=00:03:17.07 bitrate=N/A speed=49.2x    
[hls @ 0x61859dcd47c0] Opening '/cache/transcodes/4e5551e160f1eed7a086927ce45703d0527.mp4' for writing
[hls @ 0x61859dcd47c0] Opening '/cache/transcodes/4e5551e160f1eed7a086927ce45703d0528.mp4' for writing
size=N/A time=00:03:25.37 bitrate=N/A speed=45.6x    
[hls @ 0x61859dcd47c0] Opening '/cache/transcodes/4e5551e160f1eed7a086927ce45703d0529.mp4' for writing
[hls @ 0x61859dcd47c0] Opening '/cache/transcodes/4e5551e160f1eed7a086927ce45703d0530.mp4' for writing
size=N/A time=00:03:36.81 bitrate=N/A speed=43.3x    
[hls @ 0x61859dcd47c0] Opening '/cache/transcodes/4e5551e160f1eed7a086927ce45703d0531.mp4' for writing
[hls @ 0x61859dcd47c0] Opening '/cache/transcodes/4e5551e160f1eed7a086927ce45703d0532.mp4' for writing
size=N/A time=00:03:41.03 bitrate=N/A speed=40.1x    
[hls @ 0x61859dcd47c0] Opening '/cache/transcodes/4e5551e160f1eed7a086927ce45703d0533.mp4' for writing
[hls @ 0x61859dcd47c0] Opening '/cache/transcodes/4e5551e160f1eed7a086927ce45703d0534.mp4' for writing
size=N/A time=00:03:56.65 bitrate=N/A speed=39.4x    
[hls @ 0x61859dcd47c0] Opening '/cache/transcodes/4e5551e160f1eed7a086927ce45703d0535.mp4' for writing
size=N/A time=00:04:07.08 bitrate=N/A speed=  38x    
[hls @ 0x61859dcd47c0] Opening '/cache/transcodes/4e5551e160f1eed7a086927ce45703d0536.mp4' for writing
[hls @ 0x61859dcd47c0] Opening '/cache/transcodes/4e5551e160f1eed7a086927ce45703d0537.mp4' for writing
[hls @ 0x61859dcd47c0] Opening '/cache/transcodes/4e5551e160f1eed7a086927ce45703d0538.mp4' for writing
[hls @ 0x61859dcd47c0] Opening '/cache/transcodes/4e5551e160f1eed7a086927ce45703d0539.mp4' for writing
size=N/A time=00:04:24.76 bitrate=N/A speed=37.8x    
[hls @ 0x61859dcd47c0] Opening '/cache/transcodes/4e5551e160f1eed7a086927ce45703d0540.mp4' for writing
[hls @ 0x61859dcd47c0] Opening '/cache/transcodes/4e5551e160f1eed7a086927ce45703d0541.mp4' for writing
[hls @ 0x61859dcd47c0] Opening '/cache/transcodes/4e5551e160f1eed7a086927ce45703d0542.mp4' for writing
[hls @ 0x61859dcd47c0] Opening '/cache/transcodes/4e5551e160f1eed7a086927ce45703d0543.mp4' for writing
[hls @ 0x61859dcd47c0] Opening '/cache/transcodes/4e5551e160f1eed7a086927ce45703d0544.mp4' for writing
[hls @ 0x61859dcd47c0] Opening '/cache/transcodes/4e5551e160f1eed7a086927ce45703d0545.mp4' for writing
[hls @ 0x61859dcd47c0] Opening '/cache/transcodes/4e5551e160f1eed7a086927ce45703d0546.mp4' for writing
[hls @ 0x61859dcd47c0] Opening '/cache/transcodes/4e5551e160f1eed7a086927ce45703d0547.mp4' for writing
size=N/A time=00:04:55.16 bitrate=N/A speed=39.3x    
[hls @ 0x61859dcd47c0] Opening '/cache/transcodes/4e5551e160f1eed7a086927ce45703d0548.mp4' for writing
[hls @ 0x61859dcd47c0] Opening '/cache/transcodes/4e5551e160f1eed7a086927ce45703d0549.mp4' for writing
[hls @ 0x61859dcd47c0] Opening '/cache/transcodes/4e5551e160f1eed7a086927ce45703d0550.mp4' for writing
[hls @ 0x61859dcd47c0] Opening '/cache/transcodes/4e5551e160f1eed7a086927ce45703d0551.mp4' for writing
[hls @ 0x61859dcd47c0] Opening '/cache/transcodes/4e5551e160f1eed7a086927ce45703d0552.mp4' for writing
[hls @ 0x61859dcd47c0] Opening '/cache/transcodes/4e5551e160f1eed7a086927ce45703d0553.mp4' for writing
[hls @ 0x61859dcd47c0] Opening '/cache/transcodes/4e5551e160f1eed7a086927ce45703d0554.mp4' for writing
[hls @ 0x61859dcd47c0] Opening '/cache/transcodes/4e5551e160f1eed7a086927ce45703d0555.mp4' for writing
[hls @ 0x61859dcd47c0] Opening '/cache/transcodes/4e5551e160f1eed7a086927ce45703d0556.mp4' for writing
[hls @ 0x61859dcd47c0] Opening '/cache/transcodes/4e5551e160f1eed7a086927ce45703d0557.mp4' for writing
[hls @ 0x61859dcd47c0] Opening '/cache/transcodes/4e5551e160f1eed7a086927ce45703d0558.mp4' for writing
[hls @ 0x61859dcd47c0] Opening '/cache/transcodes/4e5551e160f1eed7a086927ce45703d0559.mp4' for writing
[hls @ 0x61859dcd47c0] Opening '/cache/transcodes/4e5551e160f1eed7a086927ce45703d0560.mp4' for writing
[hls @ 0x61859dcd47c0] Opening '/cache/transcodes/4e5551e160f1eed7a086927ce45703d0561.mp4' for writing
[hls @ 0x61859dcd47c0] Opening '/cache/transcodes/4e5551e160f1eed7a086927ce45703d0562.mp4' for writing
[hls @ 0x61859dcd47c0] Opening '/cache/transcodes/4e5551e160f1eed7a086927ce45703d0563.mp4' for writing
[hls @ 0x61859dcd47c0] Opening '/cache/transcodes/4e5551e160f1eed7a086927ce45703d0564.mp4' for writing
[hls @ 0x61859dcd47c0] Opening '/cache/transcodes/4e5551e160f1eed7a086927ce45703d0565.mp4' for writing
[out#0/hls @ 0x61859dd4df40] video:242619KiB audio:6464KiB subtitle:0KiB other streams:0KiB global headers:0KiB muxing overhead: unknown
size=N/A time=00:06:22.09 bitrate=N/A speed=47.9x    